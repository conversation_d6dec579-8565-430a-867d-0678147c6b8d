{"version": 3, "sources": ["turbopack:///[project]/src/lib/database.ts", "turbopack:///[project]/src/lib/gemini.ts", "turbopack:///[project]/src/app/api/gemini/reset/route.ts", "turbopack:///[project]/node_modules/next/dist/esm/build/templates/app-route.js"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport crypto from 'crypto';\n\n// 数据类型定义\nexport interface Novel {\n  id: string;\n  title: string;\n  filename: string;\n  createdAt: string;\n  chapterCount?: number;\n}\n\nexport interface Chapter {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  title: string;\n  content: string;\n  filename: string;\n  createdAt: string;\n}\n\nexport interface RewriteRule {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Character {\n  id: string;\n  novelId: string;\n  name: string;\n  role: string; // 角色类型：男主、女主、配角、反派、其他\n  description: string;\n  personality?: string; // 性格特点\n  appearance?: string; // 外貌描述\n  relationships?: string; // 人物关系\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 小说整体上下文\nexport interface NovelContext {\n  id: string;\n  novelId: string;\n  summary: string; // 小说整体摘要\n  mainCharacters: Array<{\n    name: string;\n    role: string;\n    description: string;\n    relationships?: string;\n  }>; // 主要人物信息\n  worldSetting: string; // 世界观设定\n  writingStyle: string; // 写作风格特征\n  mainPlotlines: string[]; // 主要情节线\n  themes: string[]; // 主题\n  tone: string; // 语调风格\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 章节上下文\nexport interface ChapterContext {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  keyEvents: string[]; // 关键事件\n  characterStates: Array<{\n    name: string;\n    status: string; // 人物在本章的状态\n    emotions: string; // 情感状态\n    relationships: string; // 关系变化\n  }>; // 人物状态\n  plotProgress: string; // 情节推进要点\n  previousChapterSummary?: string; // 前一章摘要\n  nextChapterHints?: string; // 对下一章的暗示\n  contextualNotes: string; // 上下文注释\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Preset {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface RewriteJob {\n  id: string;\n  novelId: string;\n  chapters: number[];\n  ruleId: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n  // 新增详细信息字段\n  details?: {\n    totalChapters: number;\n    completedChapters: number;\n    failedChapters: number;\n    totalTokensUsed: number;\n    totalProcessingTime: number;\n    averageTimePerChapter: number;\n    apiKeyStats: Array<{\n      name: string;\n      requestCount: number;\n      weight: number;\n      isAvailable: boolean;\n    }>;\n    chapterResults: Array<{\n      chapterNumber: number;\n      chapterTitle: string;\n      success: boolean;\n      error?: string;\n      apiKeyUsed?: string;\n      tokensUsed?: number;\n      processingTime?: number;\n      completedAt?: string;\n    }>;\n    model?: string;\n    concurrency?: number;\n  };\n}\n\n// 数据存储路径\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst NOVELS_FILE = path.join(DATA_DIR, 'novels.json');\nconst CHAPTERS_FILE = path.join(DATA_DIR, 'chapters.json');\nconst RULES_FILE = path.join(DATA_DIR, 'rewrite_rules.json');\nconst JOBS_FILE = path.join(DATA_DIR, 'rewrite_jobs.json');\nconst CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');\nconst PRESETS_FILE = path.join(DATA_DIR, 'presets.json');\nconst NOVEL_CONTEXTS_FILE = path.join(DATA_DIR, 'novel-contexts.json');\nconst CHAPTER_CONTEXTS_FILE = path.join(DATA_DIR, 'chapter-contexts.json');\n\n// 确保数据目录存在\nfunction ensureDataDir() {\n  if (!fs.existsSync(DATA_DIR)) {\n    fs.mkdirSync(DATA_DIR, { recursive: true });\n  }\n}\n\n// 读取JSON文件\nfunction readJsonFile<T>(filePath: string): T[] {\n  ensureDataDir();\n  if (!fs.existsSync(filePath)) {\n    return [];\n  }\n  try {\n    const data = fs.readFileSync(filePath, 'utf-8');\n    return JSON.parse(data);\n  } catch (error) {\n    console.error(`Error reading ${filePath}:`, error);\n    return [];\n  }\n}\n\n// 写入JSON文件\nfunction writeJsonFile<T>(filePath: string, data: T[]) {\n  ensureDataDir();\n  try {\n    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');\n  } catch (error) {\n    console.error(`Error writing ${filePath}:`, error);\n    throw error;\n  }\n}\n\n// 生成唯一ID\nfunction generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substring(2);\n}\n\n// 基于内容生成确定性ID\nfunction generateDeterministicId(content: string): string {\n  return crypto.createHash('md5').update(content).digest('hex').substring(0, 18);\n}\n\n// 小说相关操作\nexport const novelDb = {\n  getAll: (): Novel[] => readJsonFile<Novel>(NOVELS_FILE),\n\n  getById: (id: string): Novel | undefined => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    return novels.find(novel => novel.id === id);\n  },\n\n  create: (novel: Omit<Novel, 'id' | 'createdAt'>): Novel => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n\n    // 使用书名生成确定性ID\n    const novelId = generateDeterministicId(novel.title);\n\n    // 检查是否已存在相同ID的小说\n    const existingNovel = novels.find(n => n.id === novelId);\n    if (existingNovel) {\n      // 如果已存在，更新现有记录\n      existingNovel.filename = novel.filename;\n      existingNovel.chapterCount = novel.chapterCount;\n      writeJsonFile(NOVELS_FILE, novels);\n      return existingNovel;\n    }\n\n    const newNovel: Novel = {\n      ...novel,\n      id: novelId,\n      createdAt: new Date().toISOString(),\n    };\n    novels.push(newNovel);\n    writeJsonFile(NOVELS_FILE, novels);\n    return newNovel;\n  },\n\n  update: (id: string, updates: Partial<Novel>): Novel | null => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return null;\n\n    novels[index] = { ...novels[index], ...updates };\n    writeJsonFile(NOVELS_FILE, novels);\n    return novels[index];\n  },\n\n  delete: (id: string): boolean => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return false;\n\n    novels.splice(index, 1);\n    writeJsonFile(NOVELS_FILE, novels);\n    return true;\n  }\n};\n\n// 章节相关操作\nexport const chapterDb = {\n  getAll: (): Chapter[] => readJsonFile<Chapter>(CHAPTERS_FILE),\n\n  getByNovelId: (novelId: string): Chapter[] => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.filter(chapter => chapter.novelId === novelId);\n  },\n\n  getById: (id: string): Chapter | undefined => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.find(chapter => chapter.id === id);\n  },\n\n  create: (chapter: Omit<Chapter, 'id' | 'createdAt'>): Chapter => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapter: Chapter = {\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    chapters.push(newChapter);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return newChapter;\n  },\n\n  createBatch: (chapters: Omit<Chapter, 'id' | 'createdAt'>[]): Chapter[] => {\n    const existingChapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapters = chapters.map(chapter => ({\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    }));\n    existingChapters.push(...newChapters);\n    writeJsonFile(CHAPTERS_FILE, existingChapters);\n    return newChapters;\n  },\n\n  delete: (id: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const index = chapters.findIndex(chapter => chapter.id === id);\n    if (index === -1) return false;\n\n    chapters.splice(index, 1);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const filteredChapters = chapters.filter(chapter => chapter.novelId !== novelId);\n    writeJsonFile(CHAPTERS_FILE, filteredChapters);\n    return true;\n  }\n};\n\n// 改写规则相关操作\nexport const ruleDb = {\n  getAll: (): RewriteRule[] => readJsonFile<RewriteRule>(RULES_FILE),\n\n  getById: (id: string): RewriteRule | undefined => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    return rules.find(rule => rule.id === id);\n  },\n\n  create: (rule: Omit<RewriteRule, 'id' | 'createdAt' | 'updatedAt'>): RewriteRule => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const newRule: RewriteRule = {\n      ...rule,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    rules.push(newRule);\n    writeJsonFile(RULES_FILE, rules);\n    return newRule;\n  },\n\n  update: (id: string, updates: Partial<RewriteRule>): RewriteRule | null => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return null;\n\n    rules[index] = {\n      ...rules[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(RULES_FILE, rules);\n    return rules[index];\n  },\n\n  delete: (id: string): boolean => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return false;\n\n    rules.splice(index, 1);\n    writeJsonFile(RULES_FILE, rules);\n    return true;\n  }\n};\n\n// 改写任务相关操作\nexport const jobDb = {\n  getAll: (): RewriteJob[] => readJsonFile<RewriteJob>(JOBS_FILE),\n\n  getById: (id: string): RewriteJob | undefined => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    return jobs.find(job => job.id === id);\n  },\n\n  create: (job: Omit<RewriteJob, 'id' | 'createdAt' | 'updatedAt'>): RewriteJob => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const newJob: RewriteJob = {\n      ...job,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    jobs.push(newJob);\n    writeJsonFile(JOBS_FILE, jobs);\n    return newJob;\n  },\n\n  update: (id: string, updates: Partial<RewriteJob>): RewriteJob | null => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return null;\n\n    jobs[index] = {\n      ...jobs[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(JOBS_FILE, jobs);\n    return jobs[index];\n  },\n\n  delete: (id: string): boolean => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return false;\n\n    jobs.splice(index, 1);\n    writeJsonFile(JOBS_FILE, jobs);\n    return true;\n  }\n};\n\n// 人物设定相关操作\nexport const characterDb = {\n  getAll: (): Character[] => readJsonFile<Character>(CHARACTERS_FILE),\n\n  getByNovelId: (novelId: string): Character[] => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.filter(character => character.novelId === novelId);\n  },\n\n  getById: (id: string): Character | undefined => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.find(character => character.id === id);\n  },\n\n  create: (character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>): Character => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const newCharacter: Character = {\n      ...character,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    characters.push(newCharacter);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return newCharacter;\n  },\n\n  update: (id: string, updates: Partial<Character>): Character | null => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return null;\n\n    characters[index] = {\n      ...characters[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return characters[index];\n  },\n\n  delete: (id: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return false;\n\n    characters.splice(index, 1);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const filteredCharacters = characters.filter(character => character.novelId !== novelId);\n    writeJsonFile(CHARACTERS_FILE, filteredCharacters);\n    return true;\n  }\n};\n\n// 预设相关操作\nexport const presetDb = {\n  getAll: (): Preset[] => readJsonFile<Preset>(PRESETS_FILE),\n\n  getById: (id: string): Preset | undefined => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    return presets.find(preset => preset.id === id);\n  },\n\n  create: (preset: Omit<Preset, 'id' | 'createdAt' | 'updatedAt'>): Preset => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const newPreset: Preset = {\n      ...preset,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    presets.push(newPreset);\n    writeJsonFile(PRESETS_FILE, presets);\n    return newPreset;\n  },\n\n  update: (id: string, updates: Partial<Preset>): Preset | null => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const index = presets.findIndex(preset => preset.id === id);\n    if (index === -1) return null;\n\n    presets[index] = {\n      ...presets[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(PRESETS_FILE, presets);\n    return presets[index];\n  },\n\n  delete: (id: string): boolean => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const index = presets.findIndex(preset => preset.id === id);\n    if (index === -1) return false;\n\n    presets.splice(index, 1);\n    writeJsonFile(PRESETS_FILE, presets);\n    return true;\n  }\n};\n\n// 小说上下文相关操作\nexport const novelContextDb = {\n  getAll: (): NovelContext[] => readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE),\n\n  getByNovelId: (novelId: string): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    return contexts.find(context => context.novelId === novelId);\n  },\n\n  getById: (id: string): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    return contexts.find(context => context.id === id);\n  },\n\n  create: (context: Omit<NovelContext, 'id' | 'createdAt' | 'updatedAt'>): NovelContext => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const newContext: NovelContext = {\n      ...context,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    contexts.push(newContext);\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return newContext;\n  },\n\n  update: (id: string, updates: Partial<Omit<NovelContext, 'id' | 'createdAt'>>): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return undefined;\n\n    contexts[index] = {\n      ...contexts[index],\n      ...updates,\n      updatedAt: new Date().toISOString(),\n    };\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return contexts[index];\n  },\n\n  delete: (id: string): boolean => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return false;\n\n    contexts.splice(index, 1);\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return true;\n  }\n};\n\n// 章节上下文相关操作\nexport const chapterContextDb = {\n  getAll: (): ChapterContext[] => readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE),\n\n  getByNovelId: (novelId: string): ChapterContext[] => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.filter(context => context.novelId === novelId);\n  },\n\n  getByChapter: (novelId: string, chapterNumber: number): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.find(context =>\n      context.novelId === novelId && context.chapterNumber === chapterNumber\n    );\n  },\n\n  getById: (id: string): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.find(context => context.id === id);\n  },\n\n  create: (context: Omit<ChapterContext, 'id' | 'createdAt' | 'updatedAt'>): ChapterContext => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const newContext: ChapterContext = {\n      ...context,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    contexts.push(newContext);\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return newContext;\n  },\n\n  update: (id: string, updates: Partial<Omit<ChapterContext, 'id' | 'createdAt'>>): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return undefined;\n\n    contexts[index] = {\n      ...contexts[index],\n      ...updates,\n      updatedAt: new Date().toISOString(),\n    };\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return contexts[index];\n  },\n\n  delete: (id: string): boolean => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return false;\n\n    contexts.splice(index, 1);\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return true;\n  },\n\n  // 获取章节的上下文窗口（前后几章的上下文）\n  getContextWindow: (novelId: string, chapterNumber: number, windowSize: number = 2): ChapterContext[] => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const novelContexts = contexts.filter(context => context.novelId === novelId);\n\n    const startChapter = Math.max(1, chapterNumber - windowSize);\n    const endChapter = chapterNumber + windowSize;\n\n    return novelContexts.filter(context =>\n      context.chapterNumber >= startChapter && context.chapterNumber <= endChapter\n    ).sort((a, b) => a.chapterNumber - b.chapterNumber);\n  }\n};\n", "// Gemini API 集成 - 多Key池管理\nconst API_KEYS = [\n  {\n    key: 'AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw', // My First Project - 4倍强度\n    name: 'My First Project',\n    weight: 1, // 权重，表示相对强度\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0, // 冷却时间\n  },\n  {\n    key: 'AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y', // ankibot\n    name: 'ankibot',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY', // Generative Language Client\n    name: 'Generative Language Client',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc', // In The Novel\n    name: 'In The Novel',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk', // chat\n    name: 'chat',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  }\n];\n\n// API配置\nconst getGeminiApiUrl = (model: string = 'gemini-2.5-flash-lite') =>\n  `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;\nconst REQUEST_DELAY = 1000; // 请求间隔（毫秒）\nconst COOLDOWN_DURATION = 60000; // 429错误后的冷却时间（毫秒）\nconst MAX_RETRIES = 5; // 增加最大重试次数\nconst EXPONENTIAL_BACKOFF_BASE = 2000; // 指数退避基础时间（毫秒）\nconst MAX_WAIT_TIME = 30000; // 最大等待时间（毫秒）\n\n// API Key管理类\nclass ApiKeyManager {\n  private keys = [...API_KEYS];\n\n  // 获取最佳可用的API Key\n  getBestAvailableKey() {\n    const now = Date.now();\n\n    // 过滤掉冷却中的key\n    const availableKeys = this.keys.filter(key => key.cooldownUntil <= now);\n\n    if (availableKeys.length === 0) {\n      // 如果所有key都在冷却中，返回冷却时间最短的\n      return this.keys.reduce((min, key) =>\n        key.cooldownUntil < min.cooldownUntil ? key : min\n      );\n    }\n\n    // 根据权重和使用频率选择最佳key\n    const bestKey = availableKeys.reduce((best, key) => {\n      const keyScore = key.weight / (key.requestCount + 1);\n      const bestScore = best.weight / (best.requestCount + 1);\n      return keyScore > bestScore ? key : best;\n    });\n\n    return bestKey;\n  }\n\n  // 记录API使用\n  recordUsage(keyName: string, success: boolean) {\n    const key = this.keys.find(k => k.name === keyName);\n    if (key) {\n      key.requestCount++;\n      key.lastUsed = Date.now();\n\n      if (!success) {\n        // 如果失败，设置冷却时间\n        key.cooldownUntil = Date.now() + COOLDOWN_DURATION;\n      }\n    }\n  }\n\n  // 获取统计信息\n  getStats() {\n    return this.keys.map(key => ({\n      name: key.name,\n      requestCount: key.requestCount,\n      weight: key.weight,\n      isAvailable: key.cooldownUntil <= Date.now(),\n      cooldownRemaining: Math.max(0, key.cooldownUntil - Date.now()),\n    }));\n  }\n}\n\nconst keyManager = new ApiKeyManager();\n\nexport interface RewriteRequest {\n  originalText: string;\n  rules: string;\n  chapterTitle?: string;\n  chapterNumber?: number;\n  model?: string;\n  // 上下文信息\n  novelContext?: {\n    summary: string;\n    mainCharacters: Array<{\n      name: string;\n      role: string;\n      description: string;\n      relationships?: string;\n    }>;\n    worldSetting: string;\n    writingStyle: string;\n    tone: string;\n  };\n  chapterContext?: {\n    previousChapterSummary?: string;\n    keyEvents: string[];\n    characterStates: Array<{\n      name: string;\n      status: string;\n      emotions: string;\n      relationships: string;\n    }>;\n    plotProgress: string;\n    contextualNotes: string;\n  };\n}\n\nexport interface RewriteResponse {\n  rewrittenText: string;\n  success: boolean;\n  error?: string;\n  apiKeyUsed?: string;\n  tokensUsed?: number;\n  model?: string;\n  processingTime?: number;\n  detailedError?: string; // 新增详细错误信息\n  retryCount?: number; // 新增重试次数记录\n}\n\n// 构建改写提示词\nfunction buildPrompt(request: RewriteRequest): string {\n  const { originalText, rules, chapterTitle, chapterNumber, novelContext, chapterContext } = request;\n\n  let prompt = `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：\n\n改写规则：\n${rules}\n\n${chapterTitle ? `当前章节：${chapterTitle}` : ''}`;\n\n  // 添加小说整体上下文\n  if (novelContext) {\n    prompt += `\n\n【小说背景信息】\n小说摘要：${novelContext.summary}\n\n主要人物：\n${novelContext.mainCharacters.map(char =>\n      `- ${char.name}(${char.role}): ${char.description}${char.relationships ? ` | 关系：${char.relationships}` : ''}`\n    ).join('\\n')}\n\n世界观设定：${novelContext.worldSetting}\n\n写作风格：${novelContext.writingStyle}\n\n整体语调：${novelContext.tone}`;\n  }\n\n  // 添加章节上下文\n  if (chapterContext) {\n    prompt += `\n\n【章节上下文信息】`;\n\n    if (chapterContext.previousChapterSummary) {\n      prompt += `\n前一章摘要：${chapterContext.previousChapterSummary}`;\n    }\n\n    if (chapterContext.keyEvents.length > 0) {\n      prompt += `\n本章关键事件：${chapterContext.keyEvents.join('、')}`;\n    }\n\n    if (chapterContext.characterStates.length > 0) {\n      prompt += `\n人物状态：\n${chapterContext.characterStates.map(state =>\n        `- ${state.name}: ${state.status} | 情感：${state.emotions} | 关系：${state.relationships}`\n      ).join('\\n')}`;\n    }\n\n    prompt += `\n情节推进：${chapterContext.plotProgress}`;\n\n    if (chapterContext.contextualNotes) {\n      prompt += `\n重要注释：${chapterContext.contextualNotes}`;\n    }\n  }\n\n  prompt += `\n\n原文内容：\n${originalText}\n\n请严格按照改写规则进行改写，同时保持与小说整体背景和上下文的连贯性。改写后的内容应该：\n1. 遵循所有指定的改写规则\n2. 保持与小说整体背景的一致性\n3. 确保人物性格和关系的连贯性\n4. 保持情节发展的逻辑性\n5. 维持原有的写作风格和语调\n6. 确保文字流畅自然\n\n请直接输出改写后的内容，不要添加任何解释或说明：`;\n\n  return prompt;\n}\n\n// 调用Gemini API进行文本改写 - 增强错误处理和重试机制\nexport async function rewriteText(request: RewriteRequest): Promise<RewriteResponse> {\n  const startTime = Date.now();\n  let lastError = '';\n  let lastDetailedError = '';\n\n  for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {\n    try {\n      const apiKey = keyManager.getBestAvailableKey();\n\n      // 智能等待策略：如果key在冷却中，使用指数退避\n      if (apiKey.cooldownUntil > Date.now()) {\n        const cooldownWait = Math.min(apiKey.cooldownUntil - Date.now(), MAX_WAIT_TIME);\n        const exponentialWait = Math.min(EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt), MAX_WAIT_TIME);\n        const waitTime = Math.max(cooldownWait, exponentialWait);\n\n        console.log(`等待 ${waitTime}ms (尝试 ${attempt + 1}/${MAX_RETRIES}, API Key: ${apiKey.name})`);\n        await new Promise(resolve => setTimeout(resolve, waitTime));\n      }\n\n      const prompt = buildPrompt(request);\n\n      // 增加请求超时设置\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60秒超时\n\n      const apiUrl = getGeminiApiUrl(request.model);\n      const response = await fetch(`${apiUrl}?key=${apiKey.key}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          contents: [{\n            parts: [{\n              text: prompt\n            }]\n          }],\n          generationConfig: {\n            temperature: 0.6,\n            topK: 10,\n            topP: 0.8,\n            \"thinkingConfig\": {\n              \"thinkingBudget\": 0\n            }\n          },\n        }),\n        signal: controller.signal,\n      });\n\n      clearTimeout(timeoutId);\n      const processingTime = Date.now() - startTime;\n\n      // 处理429错误（API限流）\n      if (response.status === 429) {\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = `API限流 (${apiKey.name})`;\n        lastDetailedError = `第${attempt + 1}次尝试: API Key \"${apiKey.name}\" 遇到限流，状态码: 429`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          const retryDelay = EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt);\n          console.log(`API限流，${retryDelay}ms后重试...`);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n      }\n\n      // 处理其他HTTP错误\n      if (!response.ok) {\n        const errorData = await response.text();\n        console.error('Gemini API error:', errorData);\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = `API请求失败: ${response.status} ${response.statusText}`;\n        lastDetailedError = `第${attempt + 1}次尝试: HTTP ${response.status} ${response.statusText}, 响应: ${errorData.substring(0, 200)}`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          const retryDelay = REQUEST_DELAY * (attempt + 1);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n        };\n      }\n\n      // 解析响应数据\n      let data;\n      try {\n        data = await response.json();\n      } catch (parseError) {\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = 'JSON解析失败';\n        lastDetailedError = `第${attempt + 1}次尝试: 无法解析API响应为JSON, 错误: ${parseError instanceof Error ? parseError.message : '未知错误'}`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          const retryDelay = REQUEST_DELAY * (attempt + 1);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n          retryCount: attempt + 1,\n        };\n      }\n\n      // 记录成功使用\n      keyManager.recordUsage(apiKey.name, true);\n\n      // 增强响应验证\n      if (!data.candidates || data.candidates.length === 0) {\n        lastError = '没有收到有效的响应内容';\n        lastDetailedError = `第${attempt + 1}次尝试: API响应中没有candidates字段或为空数组, 完整响应: ${JSON.stringify(data).substring(0, 500)}`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          keyManager.recordUsage(apiKey.name, false); // 标记为失败，触发冷却\n          const retryDelay = EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n          retryCount: attempt + 1,\n        };\n      }\n\n      const candidate = data.candidates[0];\n\n      if (candidate.finishReason === 'SAFETY') {\n        return {\n          rewrittenText: '',\n          success: false,\n          error: '内容被安全过滤器拦截，请调整改写规则或原文内容',\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: `内容被安全过滤器拦截，finishReason: SAFETY`,\n          retryCount: attempt + 1,\n        };\n      }\n\n      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {\n        lastError = '响应内容格式错误';\n        lastDetailedError = `第${attempt + 1}次尝试: candidate内容格式错误, candidate: ${JSON.stringify(candidate).substring(0, 300)}`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          keyManager.recordUsage(apiKey.name, false);\n          const retryDelay = REQUEST_DELAY * (attempt + 1);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n          retryCount: attempt + 1,\n        };\n      }\n\n      const rewrittenText = candidate.content.parts[0].text;\n\n      // 验证生成的内容质量\n      if (!rewrittenText || rewrittenText.trim().length < 10) {\n        lastError = '生成的内容过短或为空';\n        lastDetailedError = `第${attempt + 1}次尝试: 生成的内容长度: ${rewrittenText?.length || 0}, 内容: \"${rewrittenText?.substring(0, 100) || 'null'}\"`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          keyManager.recordUsage(apiKey.name, false);\n          const retryDelay = REQUEST_DELAY * (attempt + 1);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n          retryCount: attempt + 1,\n        };\n      }\n\n      // 尝试从响应中提取token使用信息\n      const tokensUsed = data.usageMetadata?.totalTokenCount || 0;\n\n      return {\n        rewrittenText: rewrittenText.trim(),\n        success: true,\n        apiKeyUsed: apiKey.name,\n        tokensUsed,\n        model: request.model || 'gemini-2.5-flash-lite',\n        processingTime,\n        retryCount: attempt + 1,\n      };\n\n    } catch (error) {\n      console.error('Gemini API调用错误:', error);\n\n      // 处理不同类型的错误\n      if (error instanceof Error && error.name === 'AbortError') {\n        lastError = '请求超时';\n        lastDetailedError = `第${attempt + 1}次尝试: 请求超时 (60秒)`;\n      } else {\n        lastError = `网络错误: ${error instanceof Error ? error.message : '未知错误'}`;\n        lastDetailedError = `第${attempt + 1}次尝试: ${error instanceof Error ? error.stack || error.message : '未知网络错误'}`;\n      }\n\n      if (attempt < MAX_RETRIES - 1) {\n        const retryDelay = EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt);\n        console.log(`网络错误，${retryDelay}ms后重试...`);\n        await new Promise(resolve => setTimeout(resolve, retryDelay));\n      }\n    }\n  }\n\n  return {\n    rewrittenText: '',\n    success: false,\n    error: `重试${MAX_RETRIES}次后仍然失败: ${lastError}`,\n    processingTime: Date.now() - startTime,\n    detailedError: lastDetailedError,\n    retryCount: MAX_RETRIES,\n  };\n}\n\n// 改进的批量改写函数 - 支持实时写入、详细进度跟踪和失败恢复\nexport async function rewriteChapters(\n  chapters: Array<{ content: string; title: string; number: number }>,\n  rules: string,\n  onProgress?: (progress: number, currentChapter: number, details?: any) => void,\n  onChapterComplete?: (chapterIndex: number, result: any) => void,\n  concurrency: number = 3, // 降低并发数以避免429错误\n  model: string = 'gemini-2.5-flash-lite', // 模型选择\n  enableFailureRecovery: boolean = true // 启用失败恢复机制\n): Promise<Array<{ success: boolean; content: string; error?: string; details?: any }>> {\n  const results: Array<{ success: boolean; content: string; error?: string; details?: any }> = new Array(chapters.length);\n  let completed = 0;\n  let totalTokensUsed = 0;\n  const startTime = Date.now();\n\n  // 使用更保守的并发策略\n  const semaphore = new Semaphore(concurrency);\n\n  const processChapter = async (chapter: { content: string; title: string; number: number }, index: number) => {\n    await semaphore.acquire();\n    const chapterStartTime = Date.now();\n\n    try {\n\n      const result = await rewriteText({\n        originalText: chapter.content,\n        rules,\n        chapterTitle: chapter.title,\n        chapterNumber: chapter.number,\n        model,\n      });\n\n      const chapterProcessingTime = Date.now() - chapterStartTime;\n\n      if (result.tokensUsed) {\n        totalTokensUsed += result.tokensUsed;\n      }\n\n      const chapterResult = {\n        success: result.success,\n        content: result.rewrittenText,\n        error: result.error,\n        details: {\n          apiKeyUsed: result.apiKeyUsed,\n          tokensUsed: result.tokensUsed,\n          model: result.model,\n          processingTime: chapterProcessingTime,\n          chapterNumber: chapter.number,\n          chapterTitle: chapter.title,\n        }\n      };\n\n      results[index] = chapterResult;\n      completed++;\n\n      // 实时回调章节完成\n      if (onChapterComplete) {\n        onChapterComplete(index, chapterResult);\n      }\n\n      // 更新进度，包含详细信息\n      if (onProgress) {\n        const progressDetails = {\n          completed,\n          total: chapters.length,\n          totalTokensUsed,\n          totalTime: Date.now() - startTime,\n          averageTimePerChapter: (Date.now() - startTime) / completed,\n          apiKeyStats: keyManager.getStats(),\n          currentChapter: {\n            number: chapter.number,\n            title: chapter.title,\n            processingTime: chapterProcessingTime,\n            apiKey: result.apiKeyUsed,\n            tokens: result.tokensUsed,\n          }\n        };\n\n        onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n      }\n\n      // 添加请求间隔\n      await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));\n\n      return result;\n    } catch (error) {\n      const chapterErrorTime = Date.now();\n      const errorResult = {\n        success: false,\n        content: '',\n        error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        details: {\n          chapterNumber: chapter.number,\n          chapterTitle: chapter.title,\n          processingTime: chapterErrorTime - chapterStartTime,\n        }\n      };\n\n      results[index] = errorResult;\n      completed++;\n\n      if (onChapterComplete) {\n        onChapterComplete(index, errorResult);\n      }\n\n      if (onProgress) {\n        const progressDetails = {\n          completed,\n          total: chapters.length,\n          totalTokensUsed,\n          totalTime: Date.now() - startTime,\n          averageTimePerChapter: (Date.now() - startTime) / completed,\n          apiKeyStats: keyManager.getStats(),\n          currentChapter: {\n            number: chapter.number,\n            title: chapter.title,\n            error: error instanceof Error ? error.message : '未知错误',\n          }\n        };\n\n        onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n      }\n\n      return null;\n    } finally {\n      semaphore.release();\n    }\n  };\n\n  // 并发处理所有章节\n  const promises = chapters.map((chapter, index) => processChapter(chapter, index));\n  await Promise.all(promises);\n\n  // 失败恢复机制：对失败的章节进行额外重试\n  if (enableFailureRecovery) {\n    const failedChapters = results\n      .map((result, index) => ({ result, index, chapter: chapters[index] }))\n      .filter(item => !item.result.success);\n\n    if (failedChapters.length > 0) {\n      console.log(`开始恢复 ${failedChapters.length} 个失败的章节...`);\n\n      // 为失败恢复使用更保守的设置\n      const recoverySemaphore = new Semaphore(1); // 串行处理失败的章节\n\n      for (const { index, chapter } of failedChapters) {\n        await recoverySemaphore.acquire();\n\n        try {\n          console.log(`正在恢复第 ${chapter.number} 章: ${chapter.title}`);\n\n          // 等待更长时间再重试\n          await new Promise(resolve => setTimeout(resolve, 5000));\n\n          const recoveryResult = await rewriteText({\n            originalText: chapter.content,\n            rules,\n            chapterTitle: chapter.title,\n            chapterNumber: chapter.number,\n            model,\n          });\n\n          if (recoveryResult.success) {\n            console.log(`成功恢复第 ${chapter.number} 章`);\n\n            const recoveredChapterResult = {\n              success: true,\n              content: recoveryResult.rewrittenText,\n              error: undefined,\n              details: {\n                ...recoveryResult,\n                chapterNumber: chapter.number,\n                chapterTitle: chapter.title,\n                isRecovered: true, // 标记为恢复的章节\n              }\n            };\n\n            results[index] = recoveredChapterResult;\n            completed++;\n\n            // 通知章节恢复完成\n            if (onChapterComplete) {\n              onChapterComplete(index, recoveredChapterResult);\n            }\n\n            // 更新进度\n            if (onProgress) {\n              const progressDetails = {\n                completed,\n                total: chapters.length,\n                totalTokensUsed: totalTokensUsed + (recoveryResult.tokensUsed || 0),\n                totalTime: Date.now() - startTime,\n                averageTimePerChapter: (Date.now() - startTime) / completed,\n                apiKeyStats: keyManager.getStats(),\n                currentChapter: {\n                  number: chapter.number,\n                  title: chapter.title,\n                  processingTime: recoveryResult.processingTime,\n                  apiKey: recoveryResult.apiKeyUsed,\n                  tokens: recoveryResult.tokensUsed,\n                  isRecovered: true,\n                }\n              };\n\n              onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n            }\n          } else {\n            console.log(`第 ${chapter.number} 章恢复失败: ${recoveryResult.error}`);\n            // 更新失败信息，包含恢复尝试的详细信息\n            results[index] = {\n              ...results[index],\n              error: `原始失败: ${results[index].error}; 恢复失败: ${recoveryResult.error}`,\n              details: {\n                ...results[index].details,\n                recoveryAttempted: true,\n                recoveryError: recoveryResult.error,\n                recoveryDetailedError: recoveryResult.detailedError,\n              }\n            };\n          }\n        } catch (error) {\n          console.error(`恢复第 ${chapter.number} 章时发生异常:`, error);\n          results[index] = {\n            ...results[index],\n            error: `${results[index].error}; 恢复异常: ${error instanceof Error ? error.message : '未知错误'}`,\n            details: {\n              ...results[index].details,\n              recoveryAttempted: true,\n              recoveryException: error instanceof Error ? error.message : '未知错误',\n            }\n          };\n        } finally {\n          recoverySemaphore.release();\n        }\n      }\n    }\n  }\n\n  return results;\n}\n\n// 信号量类，用于控制并发\nclass Semaphore {\n  private permits: number;\n  private waitQueue: Array<() => void> = [];\n\n  constructor(permits: number) {\n    this.permits = permits;\n  }\n\n  async acquire(): Promise<void> {\n    if (this.permits > 0) {\n      this.permits--;\n      return Promise.resolve();\n    }\n\n    return new Promise<void>((resolve) => {\n      this.waitQueue.push(resolve);\n    });\n  }\n\n  release(): void {\n    this.permits++;\n    if (this.waitQueue.length > 0) {\n      const resolve = this.waitQueue.shift();\n      if (resolve) {\n        this.permits--;\n        resolve();\n      }\n    }\n  }\n}\n\n// 测试API连接 - 增强版\nexport async function testGeminiConnection(): Promise<{\n  success: boolean;\n  error?: string;\n  details?: any;\n}> {\n  try {\n    const testResult = await rewriteText({\n      originalText: '这是一个测试文本。',\n      rules: '保持原文不变',\n    });\n\n    return {\n      success: testResult.success,\n      error: testResult.error,\n      details: {\n        apiKeyUsed: testResult.apiKeyUsed,\n        tokensUsed: testResult.tokensUsed,\n        model: testResult.model,\n        processingTime: testResult.processingTime,\n        apiKeyStats: keyManager.getStats(),\n      }\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,\n      details: {\n        apiKeyStats: keyManager.getStats(),\n      }\n    };\n  }\n}\n\n// 获取API Key使用统计\nexport function getApiKeyStats() {\n  return keyManager.getStats();\n}\n\n// 重置API Key统计\nexport function resetApiKeyStats() {\n  API_KEYS.forEach(key => {\n    key.requestCount = 0;\n    key.lastUsed = 0;\n    key.cooldownUntil = 0;\n  });\n}\n\n// 预设的改写规则模板\nexport let PRESET_RULES: Record<string, { name: string; description: string; rules: string }> = {\n  romance_focus: {\n    name: '感情戏增强',\n    description: '扩写男女主互动内容，对非感情戏部分一笔带过',\n    rules: `请按照以下规则改写：\n1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节\n2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过\n3. 增加角色间的情感张力和暧昧氛围\n4. 保持故事主线不变，但重点突出感情发展`\n  },\n\n  character_fix: {\n    name: '人设修正',\n    description: '修正主角人设和对话风格',\n    rules: `请按照以下规则改写：\n1. 修正主角的性格设定，使其更加立体和讨喜\n2. 改善对话风格，使其更加自然流畅\n3. 去除过于中二或不合理的行为描写\n4. 保持角色的核心特征，但优化表现方式`\n  },\n\n  toxic_content_removal: {\n    name: '毒点清除',\n    description: '移除送女、绿帽等毒点情节',\n    rules: `请按照以下规则改写：\n1. 完全移除或修改送女、绿帽、圣母等毒点情节\n2. 删除或改写让读者不适的桥段\n3. 保持故事逻辑的完整性\n4. 用更合理的情节替代被删除的内容`\n  },\n\n  pacing_improvement: {\n    name: '节奏优化',\n    description: '优化故事节奏，删除拖沓内容',\n    rules: `请按照以下规则改写：\n1. 删除重复和拖沓的描写\n2. 加快故事节奏，突出重点情节\n3. 简化过于冗长的对话和心理描写\n4. 保持故事的紧凑性和可读性`\n  },\n\n  custom: {\n    name: '自定义规则',\n    description: '用户自定义的改写规则',\n    rules: ''\n  }\n};\n\n// 从数据库加载自定义预设并合并到 PRESET_RULES（仅在服务端使用）\nexport function loadCustomPresets() {\n  // 这个函数只在服务端使用，客户端组件应该通过 API 获取预设\n  if (typeof window !== 'undefined') {\n    console.warn('loadCustomPresets should not be called on client side');\n    return;\n  }\n\n  try {\n    const { presetDb } = require('@/lib/database');\n    const customPresets = presetDb.getAll();\n\n    // 将数据库中的预设添加到 PRESET_RULES\n    customPresets.forEach((preset: any) => {\n      PRESET_RULES[`custom_${preset.id}`] = {\n        name: preset.name,\n        description: preset.description,\n        rules: preset.rules\n      };\n    });\n  } catch (error) {\n    console.error('加载自定义预设失败:', error);\n  }\n}\n\n// 添加自定义预设规则（保持向后兼容）\nexport function addCustomPreset(name: string, description: string, rules: string): string {\n  const key = `custom_${Date.now()}`;\n  PRESET_RULES = {\n    ...PRESET_RULES,\n    [key]: {\n      name,\n      description,\n      rules\n    }\n  };\n  return key;\n}\n\n// 带上下文的重写函数（仅服务端使用）\nexport async function rewriteTextWithContext(\n  novelId: string,\n  chapterNumber: number,\n  originalText: string,\n  rules: string,\n  chapterTitle?: string,\n  model?: string\n): Promise<RewriteResponse> {\n  // 检查是否在服务端环境\n  if (typeof window !== 'undefined') {\n    console.warn('rewriteTextWithContext should only be used on server side');\n    // 在客户端环境下回退到普通重写\n    return await rewriteText({\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model\n    });\n  }\n\n  try {\n    // 动态导入避免循环依赖，只在服务端执行\n    const { novelContextDb, chapterContextDb } = require('./database');\n\n    // 获取小说整体上下文\n    const novelContext = novelContextDb.getByNovelId(novelId);\n\n    // 获取章节上下文\n    const chapterContext = chapterContextDb.getByChapter(novelId, chapterNumber);\n\n    // 构建请求\n    const request: RewriteRequest = {\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model,\n      novelContext: novelContext ? {\n        summary: novelContext.summary,\n        mainCharacters: novelContext.mainCharacters,\n        worldSetting: novelContext.worldSetting,\n        writingStyle: novelContext.writingStyle,\n        tone: novelContext.tone\n      } : undefined,\n      chapterContext: chapterContext ? {\n        previousChapterSummary: chapterContext.previousChapterSummary,\n        keyEvents: chapterContext.keyEvents,\n        characterStates: chapterContext.characterStates,\n        plotProgress: chapterContext.plotProgress,\n        contextualNotes: chapterContext.contextualNotes\n      } : undefined\n    };\n\n    return await rewriteText(request);\n  } catch (error) {\n    console.error('带上下文重写失败:', error);\n    // 如果获取上下文失败，回退到普通重写\n    return await rewriteText({\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model\n    });\n  }\n}\n", "import { NextResponse } from 'next/server';\nimport { resetApiKeyStats } from '@/lib/gemini';\n\n// POST - 重置API Key统计\nexport async function POST() {\n  try {\n    resetApiKeyStats();\n    \n    return NextResponse.json({\n      success: true,\n      message: 'API Key统计已重置',\n    });\n  } catch (error) {\n    console.error('重置API统计失败:', error);\n    return NextResponse.json(\n      { success: false, error: '重置API统计失败' },\n      { status: 500 }\n    );\n  }\n}\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/gemini/reset/route\",\n        pathname: \"/api/gemini/reset\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/src/app/api/gemini/reset/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/gemini/reset/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n"], "names": [], "mappings": "m1CAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAoIA,IAAM,EAAW,EAAA,OAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAI,QACpC,EAAc,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,eAClC,EAAgB,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,iBACpC,EAAa,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,sBACjC,EAAY,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,qBAChC,EAAkB,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,mBACtC,EAAe,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,gBACnC,EAAsB,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,uBAC1C,EAAwB,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,yBAGlD,SAAS,IACH,AAAC,EAAA,OAAE,CAAC,UAAU,CAAC,IACjB,EAAA,KAD4B,EAC1B,CAAC,SAAS,CAAC,EAAU,CAAE,UAAW,EAAK,EAE7C,CAGA,SAAS,EAAgB,CAAgB,EAEvC,GADA,IACI,CAAC,EAAA,OAAE,CAAC,UAAU,CAAC,GACjB,MAAO,EADqB,AACnB,CAEX,GAAI,CACF,IAAM,EAAO,EAAA,OAAE,CAAC,YAAY,CAAC,EAAU,SACvC,OAAO,KAAK,KAAK,CAAC,EACpB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,EAAS,CAAC,CAAC,CAAE,GACrC,EAAE,AACX,CACF,CAGA,SAAS,EAAiB,CAAgB,CAAE,CAAS,EACnD,IACA,GAAI,CACF,EAAA,OAAE,CAAC,aAAa,CAAC,EAAU,KAAK,SAAS,CAAC,EAAM,KAAM,GAAI,QAC5D,CAAE,MAAO,EAAO,CAEd,MADA,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,EAAS,CAAC,CAAC,CAAE,GACtC,CACR,CACF,CAGA,SAAS,IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,IAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,EACxE,CAQO,IAAM,EAAU,CACrB,OAAQ,IAAe,EAAoB,GAE3C,QAAS,AAAC,GACO,AACR,EAD4B,GACrB,IAAI,CAAC,GAAS,EAAM,EAAE,GAAK,GAG3C,OAAQ,AAAC,UACP,IAAM,EAAS,EAAoB,GAG7B,GAjBuB,EAiBW,EAAM,GAA9B,AAjB4B,EAiBO,CAhB9C,EAAA,OAAM,CAAC,UAAU,CAAC,OAAO,MAAM,CAAC,GAAS,MAAM,CAAC,OAAO,SAAS,CAAC,EAAG,KAmBnE,EAAgB,EAAO,IAAI,CAAC,GAAK,EAAE,EAAE,GAAK,GAChD,GAAI,EAKF,OAHA,EAAc,IAFG,IAEK,CAAG,EAAM,QAAQ,CACvC,EAAc,YAAY,CAAG,EAAM,YAAY,CAC/C,EAAc,EAAa,GACpB,EAGT,IAAM,EAAkB,CACtB,GAAG,CAAK,CACR,GAAI,EACJ,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAO,IAAI,CAAC,GACZ,EAAc,EAAa,GACpB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAS,EAAoB,GAC7B,EAAQ,EAAO,SAAS,CAAC,GAAS,EAAM,EAAE,GAAK,UACrD,AAAc,CAAC,GAAG,CAAd,EAAqB,MAEzB,CAAM,CAAC,EAAM,CAAG,CAAE,GAAG,CAAM,CAAC,EAAM,CAAE,GAAG,CAAQ,AAAD,EAC9C,EAAc,EAAa,GACpB,CAAM,CAAC,EAAM,CACtB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAS,EAAoB,GAC7B,EAAQ,EAAO,SAAS,CAAC,GAAS,EAAM,EAAE,GAAK,UACrD,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAElB,MAAM,CAAC,EAAO,GACrB,EAAc,EAAa,IACpB,EACT,CACF,EAGa,EAAY,CACvB,OAAQ,IAAiB,EAAsB,GAE/C,aAAc,AAAC,GACI,AACV,EADgC,GACvB,MAAM,CAAC,GAAW,EAAQ,OAAO,GAAK,GAGxD,QAAS,AAAC,GAED,AADU,EAAsB,GACvB,IAAI,CAAC,GAAW,EAAQ,EAAE,GAAK,GAGjD,OAAQ,AAAC,IACP,IAAM,EAAW,EAAsB,GACjC,EAAsB,CAC1B,GAAG,CAAO,CACV,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAS,IAAI,CAAC,GACd,EAAc,EAAe,GACtB,CACT,EAEA,YAAc,AAAD,IACX,IAAM,EAAmB,EAAsB,GACzC,EAAc,EAAS,GAAG,CAAC,IAAY,CAC3C,GAAG,CAAO,CADgC,AAE1C,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACnC,CAAC,EAGD,OAFA,EAAiB,IAAI,IAAI,GACzB,EAAc,EAAe,GACtB,CACT,EAEA,OAAQ,AAAC,IACP,IAAM,EAAW,EAAsB,GACjC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,UAC3D,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAEhB,MAAM,CAAC,EAAO,GACvB,EAAc,EAAe,IACtB,EACT,EAEA,gBAAiB,AAAC,IAEhB,IAAM,EADW,AACQ,EADc,GACL,MAAM,CAAC,GAAW,EAAQ,OAAO,GAAK,GAExE,OADA,EAAc,EAAe,IACtB,CACT,CACF,EAGa,EAAS,CACpB,OAAQ,IAAqB,EAA0B,GAEvD,QAAS,AAAC,GACM,AACP,EADiC,GAC3B,IAAI,CAAC,GAAQ,EAAK,EAAE,GAAK,GAGxC,OAAQ,AAAC,IACP,IAAM,EAAQ,EAA0B,GAClC,EAAuB,CAC3B,GAAG,CAAI,CACP,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAM,IAAI,CAAC,GACX,EAAc,EAAY,GACnB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAQ,EAA0B,GAClC,EAAQ,EAAM,SAAS,CAAC,GAAQ,EAAK,EAAE,GAAK,UAClD,AAAI,AAAU,CAAC,GAAG,GAAO,MAEzB,CAAK,CAAC,EAAM,CAAG,CACb,GAAG,CAAK,CAAC,EAAM,CACf,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAY,GACnB,CAAK,CAAC,EAAM,CACrB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAQ,EAA0B,GAClC,EAAQ,EAAM,SAAS,CAAC,GAAQ,EAAK,EAAE,GAAK,UACpC,AAAd,CAAe,GAAG,CAAd,IAEJ,EAAM,AAFmB,MAEb,CAAC,EAAO,GACpB,EAAc,EAAY,IACnB,EACT,CACF,EAGa,EAAQ,CACnB,OAAQ,IAAoB,EAAyB,GAErD,QAAS,AAAC,GACK,AACN,EAD+B,GAC1B,IAAI,CAAC,GAAO,EAAI,EAAE,GAAK,GAGrC,OAAQ,AAAC,IACP,IAAM,EAAO,EAAyB,GAChC,EAAqB,CACzB,GAAG,CAAG,CACN,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAK,IAAI,CAAC,GACV,EAAc,EAAW,GAClB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAO,EAAyB,GAChC,EAAQ,EAAK,SAAS,CAAC,GAAO,EAAI,EAAE,GAAK,UACjC,AAAd,CAAe,GAAG,CAAd,EAAqB,MAEzB,CAAI,CAAC,EAAM,CAAG,CACZ,GAAG,CAAI,CAAC,EAAM,CACd,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAW,GAClB,CAAI,CAAC,EAAM,CACpB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAO,EAAyB,GAChC,EAAQ,EAAK,SAAS,CAAC,GAAO,EAAI,EAAE,GAAK,UAC/C,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAEpB,MAAM,CAAC,EAAO,GACnB,EAAc,EAAW,IAClB,EACT,CACF,EAGa,EAAc,CACzB,OAAQ,IAAmB,EAAwB,GAEnD,aAAc,AAAC,GACM,AACZ,EADoC,GACzB,MAAM,CAAC,GAAa,EAAU,OAAO,GAAK,GAG9D,QAAS,AAAC,GACW,AACZ,EADoC,GACzB,IAAI,CAAC,GAAa,EAAU,EAAE,GAAK,GAGvD,OAAQ,AAAC,IACP,IAAM,EAAa,EAAwB,GACrC,EAA0B,CAC9B,GAAG,CAAS,CACZ,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAW,IAAI,CAAC,GAChB,EAAc,EAAiB,GACxB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAa,EAAwB,GACrC,EAAQ,EAAW,SAAS,CAAC,GAAa,EAAU,EAAE,GAAK,UACjE,AAAc,CAAC,GAAG,CAAd,EAAqB,MAEzB,CAAU,CAAC,EAAM,CAAG,CAClB,GAAG,CAAU,CAAC,EAAM,CACpB,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAiB,GACxB,CAAU,CAAC,EAAM,CAC1B,EAEA,OAAQ,AAAC,IACP,IAAM,EAAa,EAAwB,GACrC,EAAQ,EAAW,SAAS,CAAC,GAAa,EAAU,EAAE,GAAK,UACnD,AAAd,CAAe,GAAG,CAAd,IAEJ,EAAW,AAFc,MAER,CAAC,EAAO,GACzB,EAAc,EAAiB,IACxB,EACT,EAEA,gBAAiB,AAAC,IAEhB,IAAM,EADa,AACQ,EADgB,GACL,MAAM,CAAC,GAAa,EAAU,OAAO,GAAK,GAEhF,OADA,EAAc,EAAiB,IACxB,CACT,CACF,EAGa,EAAW,CACtB,OAAQ,IAAgB,EAAqB,GAE7C,QAAU,AAAD,GACS,AACT,EAD8B,GACtB,IAAI,CAAC,GAAU,EAAO,EAAE,GAAK,GAG9C,OAAQ,AAAC,IACP,IAAM,EAAU,EAAqB,GAC/B,EAAoB,CACxB,GAAG,CAAM,CACT,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAQ,IAAI,CAAC,GACb,EAAc,EAAc,GACrB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAU,EAAqB,GAC/B,EAAQ,EAAQ,SAAS,CAAC,GAAU,EAAO,EAAE,GAAK,UAC1C,AAAd,CAAe,GAAG,CAAd,EAAqB,MAEzB,CAAO,CAAC,EAAM,CAAG,CACf,GAAG,CAAO,CAAC,EAAM,CACjB,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAc,GACrB,CAAO,CAAC,EAAM,CACvB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAU,EAAqB,GAC/B,EAAQ,EAAQ,SAAS,CAAC,GAAU,EAAO,EAAE,GAAK,UACxD,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAEjB,MAAM,CAAC,EAAO,GACtB,EAAc,EAAc,IACrB,EACT,CACF,EAGa,EAAiB,CAC5B,OAAQ,IAAsB,EAA2B,GAEzD,aAAc,AAAC,GAEN,AADU,EAA2B,GAC5B,IAAI,CAAC,GAAW,EAAQ,OAAO,GAAK,GAGtD,QAAS,AAAC,GACS,AACV,EADqC,GAC5B,IAAI,CAAC,GAAW,EAAQ,EAAE,GAAK,GAGjD,OAAQ,AAAC,IACP,IAAM,EAAW,EAA2B,GACtC,EAA2B,CAC/B,GAAG,CAAO,CACV,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAS,IAAI,CAAC,GACd,EAAc,EAAqB,GAC5B,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAW,EAA2B,GACtC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,GAC3D,GAAc,CAAC,GAAG,CAAd,EAQJ,IARyB,GAEzB,CAAQ,CAAC,EAAM,CAAG,CAChB,GAAG,CAAQ,CAAC,EAAM,CAClB,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAqB,GAC5B,CAAQ,CAAC,EAClB,AADwB,EAGxB,OAAQ,AAAC,IACP,IAAM,EAAW,EAA2B,GACtC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,UAC7C,AAAd,CAAe,GAAG,CAAd,IAEJ,EAFyB,AAEhB,MAAM,CAAC,EAAO,GACvB,EAAc,EAAqB,IAC5B,EACT,CACF,EAGa,EAAmB,CAC9B,OAAQ,IAAwB,EAA6B,GAE7D,aAAc,AAAC,GAEN,AADU,EAA6B,GAC9B,MAAM,CAAC,GAAW,EAAQ,OAAO,GAAK,GAGxD,aAAc,CAAC,EAAiB,IACb,AACV,EADuC,GAC9B,IAAI,CAAC,GACnB,EAAQ,OAAO,GAAK,GAAW,EAAQ,aAAa,GAAK,GAI7D,QAAS,AAAC,GACS,AACV,EADuC,GAC9B,IAAI,CAAC,GAAW,EAAQ,EAAE,GAAK,GAGjD,OAAQ,AAAC,IACP,IAAM,EAAW,EAA6B,GACxC,EAA6B,CACjC,GAAG,CAAO,CACV,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAS,IAAI,CAAC,GACd,EAAc,EAAuB,GAC9B,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAW,EAA6B,GACxC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,GAC3D,GAAI,AAAU,CAAC,GAAG,GAQlB,IARyB,GAEzB,CAAQ,CAAC,EAAM,CAAG,CAChB,GAAG,CAAQ,CAAC,EAAM,CAClB,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAuB,GAC9B,CAAQ,CAAC,EAAM,AACxB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAW,EAA6B,GACxC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,UAC3D,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAEhB,MAAM,CAAC,EAAO,GACvB,EAAc,EAAuB,IAC9B,EACT,EAGA,iBAAkB,CAAC,EAAiB,EAAuB,EAAqB,CAAC,IAE/E,IAAM,EADW,AACK,EADwB,GACf,MAAM,CAAC,GAAW,EAAQ,OAAO,GAAK,GAE/D,EAAe,KAAK,GAAG,CAAC,EAAG,EAAgB,GAC3C,EAAa,EAAgB,EAEnC,OAAO,EAAc,MAAM,CAAC,GAC1B,EAAQ,aAAa,EAAI,GAAgB,EAAQ,aAAa,EAAI,GAClE,IAAI,CAAC,CAAC,EAAG,IAAM,EAAE,aAAa,CAAG,EAAE,aAAa,CACpD,CACF,skBC5mBA,IAAM,EAAW,CACf,CACE,IAAK,0CACL,KAAM,mBACN,OAAQ,EACR,aAAc,EACd,SAAU,EACV,cAAe,CACjB,EACA,CACE,IAAK,0CACL,KAAM,UACN,OAAQ,EACR,aAAc,EACd,SAAU,EACV,cAAe,CACjB,EACA,CACE,IAAK,0CACL,KAAM,6BACN,OAAQ,EACR,aAAc,EACd,SAAU,EACV,cAAe,CACjB,EACA,CACE,IAAK,0CACL,KAAM,eACN,OAAQ,EACR,aAAc,EACd,SAAU,EACV,cAAe,CACjB,EACA,CACE,IAAK,0CACL,KAAM,OACN,OAAQ,EACR,aAAc,EACd,SAAU,EACV,cAAe,CACjB,EACD,CAGK,EAAkB,CAAC,EAAgB,uBAAuB,GAC9D,CAAC,wDAAwD,EAAE,EAAM,gBAAgB,CAQnF,AARoF,OAQ9E,EACI,KAAO,IAAI,EAAS,AAAC,CAG7B,qBAAsB,CACpB,IAAM,EAAM,KAAK,GAAG,GAGd,EAAgB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAO,EAAI,aAAa,EAAI,UAEnE,AAAI,AAAyB,GAAG,GAAd,MAAM,CAEf,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAK,IAC5B,EAAI,aAAa,CAAG,EAAI,aAAa,CAAG,EAAM,GAKlC,EAAc,MAAM,CAAC,CAAC,EAAM,IACzB,AAEV,EAFc,MAAM,EAAI,CAAD,AAEZ,CAFiB,YAAY,EAAG,CAAC,CACjC,EAAK,MAAM,EAAI,CAAD,CAAM,YAAY,EAAG,CAAC,CACxB,EAAM,EAIxC,CAGA,YAAY,CAAe,CAAE,CAAgB,CAAE,CAC7C,IAAM,EAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAK,EAAE,IAAI,GAAK,GACvC,IACF,CADO,CACH,YAAY,GAChB,EAAI,QAAQ,CAAG,KAAK,GAAG,GAEnB,AAAC,IAEH,EAAI,GAFQ,UAEK,CAAG,KAAK,GAAG,GA1CV,EA0Ce,CAAA,EAGvC,CAGA,CAhD+B,SAgDpB,CACT,OAAO,CAjDwC,GAiDpC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAQ,CAC3B,CAD0B,IACpB,EAAI,IAAI,CACd,aAAc,EAAI,YAAY,CAC9B,OAAQ,EAAI,MAAM,CAClB,YAAa,EAAI,aAAa,EAAI,KAAK,GAAG,GAC1C,kBAAmB,KAAK,GAAG,CAAC,EAAG,EAAI,aAAa,CAAG,KAAK,GAAG,IAC7D,CAAC,CACH,CACF,CAEA,IAAM,EAAa,IAAI,EAiIhB,eAAe,EAAY,CAAuB,EACvD,IAAM,EAAY,KAAK,GAAG,GACtB,EAAY,GACZ,EAAoB,GAExB,IAAK,IAAI,EAAU,EAAG,IAAuB,IAC3C,EAD8B,CAC1B,CACF,EAFoD,EAsFhD,EApFE,EAAS,EAAW,mBAAmB,GAG7C,GAAI,EAAO,aAAa,CAAG,KAAK,GAAG,GAAI,CACrC,IAAM,EAAe,KAAK,GAAG,CAAC,EAAO,aAAa,CAAG,KAAK,GAAG,IAAI,IAC3D,EAAkB,KAAK,GAAG,CAAC,IAA2B,KAAK,GAAG,CAAC,EAAG,GArM1D,KAsMR,EAD4E,AACjE,AAtMI,KAsMC,GAAG,CAAC,EAAc,EAtMN,CAwMlC,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,SAAS,EAAS,EAAU,EAAE,CAAd,AAAe,EAAE,YAAY,AAAa,EAAO,IAAI,CAAC,CAAC,CAAC,EAAhB,AAC1E,MAAM,IAAI,QAAQ,GAAW,WAAW,EAAS,GACnD,CAEA,IAAM,EApGZ,AAoGqB,SApGZ,AAAY,CAAuB,EAC1C,GAAM,CAAE,cAAY,OAAE,CAAK,cAAE,CAAY,eAAE,CAAa,cAAE,CAAY,gBAAE,CAAc,CAAE,CAAG,EAEvF,EAAS,CAAC;;;AAGhB,EAAE,MAAM;;AAER,EAAE,EAAe,CAAC,KAAK,EAAE,EAAA,CAAc,CAAG,GAAA,CAAI,CAqE5C,OAAO,AAlEH,IACF,GAAU,CAAC,MADK;;;KAIf,EAAE,EAAa,OAAO,CAAC;;;AAG5B,EAAE,EAAa,cAAc,CAAC,GAAG,CAAC,GAC5B,CAAC,EAAE,EAAE,EAAK,IAAI,CAAC,CAAC,EAAE,EAAK,IAAI,CAAC,GAAG,EAAE,EAAK,WAAW,CAAA,EAAG,EAAK,aAAa,CAAG,CAAC,MAAM,EAAE,EAAK,aAAa,CAAA,CAAE,CAAG,GAAA,CAAI,EAC7G,IAAI,CAAC,MAAM;;MAEX,EAAE,EAAa,YAAY,CAAC;;KAE7B,EAAE,EAAa,YAAY,CAAC;;KAE5B,EAAE,EAAa,IAAI,CAAA,CAAA,AAAE,EAIpB,IACF,GAAU,CAAC,QADO;;SAGb,CAAC,CAEF,EAAe,sBAAsB,EAAE,CACzC,GAAU,CAAC;MACX,EAAE,EAAe,sBAAsB,CAAA,CAAA,AAAE,EAGvC,EAAe,SAAS,CAAC,MAAM,CAAG,GAAG,CACvC,GAAU,CAAC;OACV,EAAE,EAAe,SAAS,CAAC,IAAI,CAAC,KAAA,CAAA,AAAM,EAGrC,EAAe,eAAe,CAAC,MAAM,CAAG,GAAG,CAC7C,GAAU,CAAC;;AAEjB,EAAE,EAAe,eAAe,CAAC,GAAG,CAAC,GAC7B,CAAC,EAAE,EAAE,EAAM,IAAI,CAAC,EAAE,EAAE,EAAM,MAAM,CAAC,MAAM,EAAE,EAAM,QAAQ,CAAC,MAAM,EAAE,EAAM,aAAa,CAAA,CAAE,EACrF,IAAI,CAAC,MAAA,CAAA,AAAO,EAGhB,GAAU,CAAC;KACV,EAAE,EAAe,YAAY,CAAA,CAAE,CAE5B,EAAe,eAAe,EAAE,CAClC,GAAU,CAAC;KACZ,EAAE,EAAe,eAAe,CAAA,CAAA,AAAE,GAIrC,GAAU,CAAC;;;AAGb,EAAE,aAAa;;;;;;;;;;wBAUS,CAAC,AAGzB,EAsBiC,GAGrB,EAAa,IAAI,gBACjB,EAAY,WAAW,IAAM,EAAW,KAAK,GAAI,KAEjD,EAAS,CAFgD,CAEhC,EAAQ,KAAK,AAF2B,EAGjE,EAAW,MAAM,MAAM,CAAA,EAAG,EAAO,KAAK,EAAE,EAAO,GAAG,CAAA,CAAE,CAAE,CAC1D,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,SAAU,CAAC,CACT,MAAO,CAAC,CACN,KAAM,CACR,EACF,AADI,EACF,CACF,iBAAkB,CAChB,YAAa,GACb,KAAM,GACN,KAAM,GACN,eAAkB,CAChB,eAAkB,CACpB,CACF,CACF,GACA,OAAQ,EAAW,MAAM,AAC3B,GAEA,aAAa,GACb,IAAM,EAAiB,KAAK,GAAG,GAAK,EAGpC,GAAwB,KAAK,CAAzB,EAAS,MAAM,GACjB,EAAW,WAAW,CAAC,EAAO,IAAI,EAAE,GACpC,EAAY,CAAC,OAAO,EAAE,EAAO,IAAI,CAAC,CAAC,CAAC,CACpC,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,cAAc,EAAE,EAAO,IAAI,CAAC,eAAe,CAAC,CAE5E,EAAU,GAAiB,CAC7B,IAAM,EAAa,IAA2B,AADpB,KACyB,GAAG,CAAC,EAAG,GAC1D,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,EAAW,QAAQ,CAAC,EACzC,MAAM,IAAI,QAAQ,GAAW,WAAW,EAAS,IACjD,QACF,CAIF,GAAI,CAAC,EAAS,EAAE,CAAE,CAChB,IAAM,EAAY,MAAM,EAAS,IAAI,GAMrC,GALA,QAAQ,KAAK,CAAC,oBAAqB,GACnC,EAAW,WAAW,CAAC,EAAO,IAAI,EAAE,GACpC,EAAY,CAAC,SAAS,EAAE,EAAS,MAAM,CAAC,CAAC,EAAE,EAAS,UAAU,CAAA,CAAE,CAChE,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,UAAU,EAAE,EAAS,MAAM,CAAC,CAAC,EAAE,EAAS,UAAU,CAAC,MAAM,EAAE,EAAU,SAAS,CAAC,EAAG,KAAA,CAAM,CAExH,EAAU,EAAiB,CAC7B,IAAM,EAAa,KADO,AACU,GAAU,CAC9C,AAD+C,OACzC,AAD6B,IACzB,QAAQ,GAAW,WAAW,EAAS,IACjD,QACF,CAEA,MAAO,CACL,cAAe,GACf,SAAS,EACT,MAAO,EACP,WAAY,EAAO,IAAI,CACvB,iBACA,cAAe,CACjB,CACF,CAIA,GAAI,CACF,EAAO,MAAM,EAAS,IAAI,EAC5B,CAAE,MAAO,EAAY,CAKnB,GAJA,EAAW,WAAW,CAAC,EAAO,IAAI,EAAE,GACpC,EAAY,WACZ,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,yBAAyB,EAAE,aAAsB,MAAQ,EAAW,OAAO,CAAG,OAAA,CAAQ,CAEtH,EAAU,EAAiB,CAC7B,IAAM,EAAa,IAAiB,CADV,GACoB,CAAC,AAC/C,OADmC,AAC7B,IAAI,QAAQ,GAAW,WAAW,EAAS,IACjD,QACF,CAEA,MAAO,CACL,cAAe,GACf,SAAS,EACT,MAAO,EACP,WAAY,EAAO,IAAI,gBACvB,EACA,cAAe,EACf,WAAY,EAAU,CACxB,CACF,CAMA,GAHA,EAAW,WAAW,CAAC,EAAO,IAAI,EAAE,GAGhC,CAAC,EAAK,UAAU,EAA+B,IAA3B,EAAK,UAAU,CAAC,MAAM,CAAQ,CAIpD,GAHA,EAAY,cACZ,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,sCAAsC,EAAE,KAAK,SAAS,CAAC,GAAM,SAAS,CAAC,EAAG,KAAA,CAAM,CAEhH,EAAU,EAAiB,CAC7B,EAAW,SADe,EACJ,CAAC,EAAO,IAAI,EAAE,GACpC,IAAM,CADsC,CACzB,IAA2B,KAAK,GAAG,AADG,CACF,EAAG,EAC1D,OAAM,IAAI,QAAQ,GAAW,WAAW,EAAS,IACjD,QACF,CAEA,MAAO,CACL,cAAe,GACf,SAAS,EACT,MAAO,EACP,WAAY,EAAO,IAAI,gBACvB,EACA,cAAe,EACf,WAAY,EAAU,CACxB,CACF,CAEA,IAAM,EAAY,EAAK,UAAU,CAAC,EAAE,CAEpC,GAAI,AAA2B,UAAU,GAA3B,YAAY,CACxB,MAAO,CACL,cAAe,GACf,SAAS,EACT,MAAO,0BACP,WAAY,EAAO,IAAI,gBACvB,EACA,cAAe,CAAC,+BAA+B,CAAC,CAChD,WAAY,EAAU,CACxB,EAGF,GAAI,CAAC,EAAU,OAAO,EAAI,CAAC,EAAU,OAAO,CAAC,KAAK,EAAuC,IAAnC,EAAU,OAAO,CAAC,KAAK,CAAC,MAAM,CAAQ,CAI1F,GAHA,EAAY,WACZ,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,iCAAiC,EAAE,KAAK,SAAS,CAAC,GAAW,SAAS,CAAC,EAAG,KAAA,CAAM,CAEhH,EAAU,EAAiB,CAC7B,EAAW,SADe,EACJ,CAAC,EAAO,IAAI,EAAE,GACpC,IAAM,EAAa,KAAiB,GAAU,CAC9C,AAD+C,OACzC,AAD6B,IACzB,QAAQ,GAAW,WAAW,EAAS,IACjD,QACF,CAEA,MAAO,CACL,cAAe,GACf,SAAS,EACT,MAAO,EACP,WAAY,EAAO,IAAI,gBACvB,EACA,cAAe,EACf,WAAY,EAAU,CACxB,CACF,CAEA,IAAM,EAAgB,EAAU,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAGrD,GAAI,CAAC,GAAiB,EAAc,IAAI,GAAG,MAAM,CAAG,GAAI,CAItD,GAHA,EAAY,aACZ,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,cAAc,EAAE,GAAe,QAAU,EAAE,OAAO,EAAE,GAAe,UAAU,EAAG,MAAQ,OAAO,CAAC,CAAC,CAEjI,EAAU,EAAiB,CAC7B,EAAW,SADe,EACJ,CAAC,EAAO,IAAI,EAAE,GACpC,IAAM,EAAa,KAAiB,GAAU,CAAC,AAC/C,OADmC,AAC7B,IAAI,QAAQ,GAAW,WAAW,EAAS,IACjD,QACF,CAEA,MAAO,CACL,cAAe,GACf,QAAS,GACT,MAAO,EACP,WAAY,EAAO,IAAI,gBACvB,EACA,cAAe,EACf,WAAY,EAAU,CACxB,CACF,CAGA,IAAM,EAAa,EAAK,aAAa,EAAE,iBAAmB,EAE1D,MAAO,CACL,cAAe,EAAc,IAAI,GACjC,SAAS,EACT,WAAY,EAAO,IAAI,YACvB,EACA,MAAO,EAAQ,KAAK,EAAI,wBACxB,iBACA,WAAY,EAAU,CACxB,CAEF,CAAE,MAAO,EAAO,CAYd,GAXA,QAAQ,KAAK,CAAC,kBAAmB,GAG7B,aAAiB,OAAwB,cAAc,CAA7B,EAAM,IAAI,EACtC,EAAY,OACZ,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,eAAe,CAAC,GAEpD,EAAY,CAAC,MAAM,EAAE,aAAiB,MAAQ,EAAM,OAAO,CAAG,OAAA,CAAQ,CACtE,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,KAAK,EAAE,aAAiB,MAAQ,EAAM,KAAK,EAAI,EAAM,OAAO,CAAG,SAAA,CAAU,EAG3G,EAAU,EAAiB,CAC7B,IAAM,EA/ZmB,AA+ZN,IAA2B,CADpB,CA9ZK,GA+ZoB,GAAG,CAAC,EAAG,GAC1D,GAha8C,KAgatC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAW,QAAQ,CAAC,EACxC,MAAM,IAAI,QAAQ,GAAW,WAAW,EAAS,GACnD,CACF,CAGF,MAAO,CACL,cAAe,GACf,QAAS,GACT,MAAO,CAAC,EAAE,EAAE,SAAsB,GAAW,AAArB,CACxB,OADgC,QAChB,KAAK,GAAG,GAAK,EAC7B,cAAe,EACf,WA7agB,CA6aJ,AACd,CACF,CA/auB,AAkbhB,WAlb2B,IAkbZ,EACpB,CAAmE,CACnE,CAAa,CACb,CAA8E,CAC9E,CAA+D,CAC/D,EAAsB,CAAC,CACvB,EAAgB,uBAAuB,CACvC,GAAiC,CAAA,CAAK,CAEtC,IAAM,EAAuF,AAAI,IAFhD,EAEsD,EAAS,MAAM,EAClH,EAAY,EACZ,EAAkB,EAChB,EAAY,KAAK,GAAG,GAGpB,EAAY,IAAI,EAAU,GAE1B,EAAiB,MAAO,EAA6D,KACzF,MAAM,EAAU,OAAO,GACvB,IAAM,EAAmB,KAAK,GAAG,GAEjC,GAAI,CAEF,IAAM,EAAS,MAAM,EAAY,CAC/B,aAAc,EAAQ,OAAO,OAC7B,EACA,aAAc,EAAQ,KAAK,CAC3B,cAAe,EAAQ,MAAM,CAC7B,OACF,GAEM,EAAwB,KAAK,GAAG,GAAK,EAEvC,EAAO,UAAU,EAAE,CACrB,GAAmB,EAAO,UAAA,AAAU,EAGtC,IAAM,EAAgB,CACpB,QAAS,EAAO,OAAO,CACvB,QAAS,EAAO,aAAa,CAC7B,MAAO,EAAO,KAAK,CACnB,QAAS,CACP,WAAY,EAAO,UAAU,CAC7B,WAAY,EAAO,UAAU,CAC7B,MAAO,EAAO,KAAK,CACnB,eAAgB,EAChB,cAAe,EAAQ,MAAM,CAC7B,aAAc,EAAQ,KAAK,AAC7B,CACF,EAWA,GATA,CAAO,CAAC,EAAM,CAAG,EACjB,IAGI,GACF,EAAkB,EAAO,GAIvB,EAAY,CACd,IAAM,EANe,AAMG,WACtB,EACA,MAAO,EAAS,MAAM,iBACtB,EACA,UAAW,KAAK,GAAG,GAAK,EACxB,sBAAuB,CAAC,KAAK,GAAG,GAAK,CAAA,CAAS,CAAI,EAClD,YAAa,EAAW,QAAQ,GAChC,eAAgB,CACd,OAAQ,EAAQ,MAAM,CACtB,MAAO,EAAQ,KAAK,CACpB,eAAgB,EAChB,OAAQ,EAAO,UAAU,CACzB,OAAQ,EAAO,UAAU,AAC3B,CACF,EAEA,EAAY,EAAY,EAAS,MAAM,CAAI,IAAK,EAAQ,MAAM,CAAE,EAClE,CAKA,OAFA,MAAM,IAAI,QAAQ,GAAW,WAAW,EArgBxB,MAugBT,AAvgBe,CAqgB2B,AAGnD,CAAE,MAAO,EAAO,CACd,AAzgBiC,IAygB3B,EAAmB,KAAK,GAAG,GAC3B,EAAc,CAClB,SAAS,EACT,QAAS,GACT,MAAO,CAAC,MAAM,EAAE,aAAiB,MAAQ,EAAM,OAAO,CAAG,OAAA,CAAQ,CACjE,QAAS,CACP,cAAe,EAAQ,MAAM,CAC7B,aAAc,EAAQ,KAAK,CAC3B,eAAgB,EAAmB,CACrC,CACF,EASA,GAPA,CAAO,CAAC,EAAM,CAAG,EACjB,IAEI,GACF,EAAkB,EAAO,GAGvB,EAAY,CACd,IAAM,EALe,AAKG,WACtB,EACA,MAAO,EAAS,MAAM,iBACtB,EACA,UAAW,KAAK,GAAG,GAAK,EACxB,sBAAuB,CAAC,KAAK,GAAG,GAAK,CAAA,CAAS,CAAI,EAClD,YAAa,EAAW,QAAQ,GAChC,eAAgB,CACd,OAAQ,EAAQ,MAAM,CACtB,MAAO,EAAQ,KAAK,CACpB,MAAO,aAAiB,MAAQ,EAAM,OAAO,CAAG,MAClD,CACF,EAEA,EAAY,EAAY,EAAS,MAAM,CAAI,IAAK,EAAQ,MAAM,CAAE,EAClE,CAEA,OAAO,IACT,QAAU,CACR,EAAU,OAAO,EACnB,CACF,EAGM,EAAW,EAAS,GAAG,CAAC,CAAC,EAAS,IAAU,EAAe,EAAS,IAI1E,GAHA,MAAM,QAAQ,GAAG,CAAC,GAGd,EAAuB,CACzB,IAAM,EAAiB,EACpB,GAAG,CAAC,CAAC,EAAQ,KAAW,GAAD,KAAG,QAAQ,EAAO,QAAS,CAAQ,CAAC,EAAM,CAAC,CAAC,EACnE,MAAM,CAAC,GAAQ,CAAC,EAAK,MAAM,CAAC,OAAO,EAEtC,GAAI,EAAe,MAAM,CAAG,EAAG,CAC7B,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,EAAe,MAAM,CAAC,UAAU,CAAC,EAGrD,IAAM,EAAoB,IAAI,EAAU,GAExC,CAF4C,GAEvC,GAAM,MAF6C,CAE3C,CAAK,SAAE,CAAO,CAAE,GAAI,EAAgB,CAC/C,MAAM,EAAkB,OAAO,GAE/B,GAAI,CACF,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,EAAQ,MAAM,CAAC,IAAI,EAAE,EAAQ,KAAK,CAAA,CAAE,EAGzD,MAAM,IAAI,QAAQ,GAAW,WAAW,EAAS,MAEjD,IAAM,EAAiB,MAAM,EAAY,CACvC,aAAc,EAAQ,OAAO,CAC7B,QACA,aAAc,EAAQ,KAAK,CAC3B,cAAe,EAAQ,MAAM,OAC7B,CACF,GAEA,GAAI,EAAe,OAAO,CAAE,CAC1B,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,EAAQ,MAAM,CAAC,EAAE,CAAC,EAEvC,IAAM,EAAyB,CAC7B,SAAS,EACT,QAAS,EAAe,aAAa,CACrC,WAAO,EACP,QAAS,CACP,GAAG,CAAc,CACjB,cAAe,EAAQ,MAAM,CAC7B,aAAc,EAAQ,KAAK,CAC3B,aAAa,CACf,CACF,EAWA,GATA,CAAO,CAAC,EAAM,CAAG,EACjB,IAGI,GACF,EAAkB,EAAO,GAIvB,EAAY,CACd,IAAM,EANe,AAMG,WACtB,EACA,MAAO,EAAS,MAAM,CACtB,gBAAiB,EAAmB,GAAe,UAAU,GAA1B,AAA8B,CAAC,CAClE,UAAW,KAAK,GAAG,GAAK,EACxB,sBAAuB,CAAC,KAAK,GAAG,GAAK,CAAA,CAAS,CAAI,EAClD,YAAa,EAAW,QAAQ,GAChC,eAAgB,CACd,OAAQ,EAAQ,MAAM,CACtB,MAAO,EAAQ,KAAK,CACpB,eAAgB,EAAe,cAAc,CAC7C,OAAQ,EAAe,UAAU,CACjC,OAAQ,EAAe,UAAU,CACjC,aAAa,CACf,CACF,EAEA,EAAY,EAAY,EAAS,MAAM,CAAI,IAAK,EAAQ,MAAM,CAAE,EAClE,CACF,MACE,CADK,OACG,GAAG,CAAC,CAAC,EAAE,EAAE,EAAQ,MAAM,CAAC,QAAQ,EAAE,EAAe,KAAK,CAAA,CAAE,EAEhE,CAAO,CAAC,EAAM,CAAG,CACf,GAAG,CAAO,CAAC,EAAM,CACjB,MAAO,CAAC,MAAM,EAAE,CAAO,CAAC,EAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAe,KAAK,CAAA,CAAE,CACrE,QAAS,CACP,GAAG,CAAO,CAAC,EAAM,CAAC,OAAO,CACzB,mBAAmB,EACnB,cAAe,EAAe,KAAK,CACnC,sBAAuB,EAAe,aAAa,AACrD,CACF,CAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE,EAAQ,MAAM,CAAC,QAAQ,CAAC,CAAE,GAC/C,CAAO,CAAC,EAAM,CAAG,CACf,GAAG,CAAO,CAAC,EAAM,CACjB,MAAO,CAAA,EAAG,CAAO,CAAC,EAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,aAAiB,MAAQ,EAAM,OAAO,CAAG,OAAA,CAAQ,CAC1F,QAAS,CACP,GAAG,CAAO,CAAC,EAAM,CAAC,OAAO,CACzB,mBAAmB,EACnB,kBAAmB,aAAiB,MAAQ,EAAM,OAAO,CAAG,MAC9D,CACF,CACF,QAAU,CACR,EAAkB,OAAO,EAC3B,CACF,CACF,CACF,CAEA,OAAO,CACT,CAGA,MAAM,EACI,OAAgB,CAChB,UAA+B,EAAE,AAAC,AAE1C,aAAY,CAAe,CAAE,CAC3B,IAAI,CAAC,OAAO,CAAG,CACjB,CAEA,MAAM,SAAyB,QAC7B,AAAI,IAAI,CAAC,OAAO,CAAG,GAAG,AACpB,IAAI,CAAC,OAAO,GACL,QAAQ,OAAO,IAGjB,IAAI,QAAe,AAAD,IACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EACtB,EACF,CAEA,SAAgB,CAEd,GADA,IAAI,CAAC,OAAO,GACR,IAAI,CAAC,SAAS,CAAC,MAAM,CAAG,EAAG,CAC7B,IAAM,EAAU,IAAI,CAAC,SAAS,CAAC,KAAK,GAChC,IACF,IAAI,CAAC,AADM,OACC,GACZ,IAEJ,CACF,CACF,CAGO,eAAe,IAKpB,GAAI,CACF,IAAM,EAAa,MAAM,EAAY,CACnC,aAAc,YACd,MAAO,QACT,GAEA,MAAO,CACL,QAAS,EAAW,OAAO,CAC3B,MAAO,EAAW,KAAK,CACvB,QAAS,CACP,WAAY,EAAW,UAAU,CACjC,WAAY,EAAW,UAAU,CACjC,MAAO,EAAW,KAAK,CACvB,eAAgB,EAAW,cAAc,CACzC,YAAa,EAAW,QAAQ,EAClC,CACF,CACF,CAAE,MAAO,EAAO,CACd,MAAO,CACL,QAAS,GACT,MAAO,CAAC,QAAQ,EAAE,aAAiB,MAAQ,EAAM,OAAO,CAAG,OAAA,CAAQ,CACnE,QAAS,CACP,YAAa,EAAW,QAAQ,EAClC,CACF,CACF,CACF,CAGO,SAAS,IACd,OAAO,EAAW,QAAQ,EAC5B,CAGO,SAAS,IACd,EAAS,OAAO,CAAC,IACf,EAAI,YAAY,CAAG,EACnB,EAAI,QAAQ,CAAG,EACf,EAAI,aAAa,CAAG,CACtB,EACF,CAGO,IAAI,EAAqF,CAC9F,cAAe,CACb,KAAM,QACN,YAAa,wBACb,MAAO,CAAC;;;;qBAIS,CAAC,AACpB,EAEA,cAAe,CACb,KAAM,OACN,YAAa,cACb,MAAO,CAAC;;;;oBAIQ,CAAC,AACnB,EAEA,sBAAuB,CACrB,KAAM,OACN,YAAa,eACb,MAAO,CAAC;;;;kBAIM,CAAC,AACjB,EAEA,mBAAoB,CAClB,KAAM,OACN,YAAa,gBACb,MAAO,CAAC;;;;eAIG,CAAC,AACd,EAEA,OAAQ,CACN,KAAM,QACN,YAAa,aACb,MAAO,EACT,CACF,EAGO,SAAS,IAOd,GAAI,CACF,GAAM,UAAE,CAAQ,CAAE,CAAA,EAAA,CAAA,CAAA,OACI,AAGtB,EAH+B,MAAM,GAGvB,OAAO,CAAC,AAAC,IACrB,CAAY,CAAC,CAAC,OAAO,EAAE,EAAO,EAAE,CAAA,CAAE,CAAC,CAAG,CACpC,KAAM,EAAO,IAAI,CACjB,YAAa,EAAO,WAAW,CAC/B,MAAO,EAAO,KAAK,AACrB,CACF,EACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,aAAc,EAC9B,CACF,CAGO,SAAS,EAAgB,CAAY,CAAE,CAAmB,CAAE,CAAa,EAC9E,IAAM,EAAM,CAAC,OAAO,EAAE,KAAK,GAAG,GAAA,CAAI,CASlC,OARA,EAAe,CACb,GAAG,CAAY,CACf,CAAC,EAAI,CAAE,MACL,cACA,QACA,CACF,CACF,EACO,CACT,CAGO,eAAe,EACpB,CAAe,CACf,CAAqB,CACrB,CAAoB,CACpB,CAAa,CACb,CAAqB,CACrB,CAAc,EAed,GAAI,CAEF,GAAM,gBAAE,CAAc,kBAAE,CAAgB,CAAE,CAAA,EAAA,CAAA,CAAA,OAGpC,EAAe,EAAe,YAAY,CAAC,GAG3C,EAAiB,EAAiB,YAAY,CAAC,EAAS,GAGxD,EAA0B,cAC9B,QACA,eACA,EACA,gBACA,QACA,aAAc,EAAe,CAC3B,QAAS,EAAa,OAAO,CAC7B,eAAgB,EAAa,cAAc,CAC3C,aAAc,EAAa,YAAY,CACvC,aAAc,EAAa,YAAY,CACvC,KAAM,EAAa,IAAI,AACzB,OAAI,EACJ,eAAgB,EAAiB,CAC/B,uBAAwB,EAAe,sBAAsB,CAC7D,UAAW,EAAe,SAAS,CACnC,gBAAiB,EAAe,eAAe,CAC/C,aAAc,EAAe,YAAY,CACzC,gBAAiB,EAAe,eAAe,AACjD,OAAI,CACN,EAEA,OAAO,MAAM,EAAY,EAC3B,CAAE,MAAO,EAAO,CAGd,OAFA,QAAQ,KAAK,CAAC,YAAa,GAEpB,MAAM,EAAY,cACvB,QACA,eACA,gBACA,QACA,CACF,EACF,CACF,0LE97BA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,+BDfA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAGO,eAAe,IACpB,GAAI,CAGF,MAFA,CAAA,EAAA,EAAA,gBAAA,AAAgB,IAET,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,SAAS,EACT,QAAS,cACX,EACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,aAAc,GACrB,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,WAAY,EACrC,CAAE,OAAQ,GAAI,EAElB,CACF,CCHA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,0BACN,SAAU,oBACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,8CAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAW,AAAX,EAAY,CACf,wCACA,CACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,0BAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACR,AAAiB,OAAO,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,YAAE,CAAU,aAAE,CAAW,mBAAE,CAAiB,qBAAE,CAAmB,sBAAE,CAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,GAAQ,EAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,GAC+B,KAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,CAG/B,GAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,GACgB,IAAtB,EAAY,EAAkB,GAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAa,EAAO,WAVyE,OAUvD,GACtC,EAAU,QACZ,oBACA,EACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAA2E,AAAxD,OAAC,EAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,sBAAkB,EAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAG,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAS,KAA8C,IAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,MAAG,EAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,YACV,SACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAA,AAAO,EAAE,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAmB,AAAnB,EAAoB,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,CAChD,iBACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,mBAAmB,uBACnB,0BACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAK,AAAL,EAAiB,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAoD,AAA3C,GAAJ,IAAK,EAAqB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZM,AAAF,AAAE,CAAD,AAAC,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAD,AAAK,SAAS,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAqB,AAArB,EAAsB,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GACvB,AAD0B,CAE9B,EAAG,GAEf,CAAE,MAAO,EAAK,CAeV,GAbI,AAAC,GAAgB,WAAF,CAAC,CAAgB,EAAA,eAAe,EAC/C,CADkD,KAC5C,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [3]}