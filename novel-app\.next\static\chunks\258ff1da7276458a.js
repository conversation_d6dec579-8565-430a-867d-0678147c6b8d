(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,8153,e=>{"use strict";e.s(["default",()=>a]);var s=e.i(43476),t=e.i(71645);function a(){let[e,a]=(0,t.useState)([]),[l,r]=(0,t.useState)(!0),[c,i]=(0,t.useState)(null),[d,n]=(0,t.useState)([]),o=async()=>{try{let e=await fetch("/api/merge"),s=await e.json();s.success?a(s.data):console.error("获取小说列表失败:",s.error)}catch(e){console.error("获取小说列表失败:",e)}finally{r(!1)}},x=async e=>{i(e);try{let s=await fetch("/api/merge",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({novelId:e})}),t=await s.json();t.success?(n(e=>[...e,t.data]),await o()):alert("合并失败: ".concat(t.error))}catch(e){console.error("合并失败:",e),alert("合并失败")}finally{i(null)}};return((0,t.useEffect)(()=>{o()},[]),l)?(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)("div",{className:"text-lg",children:"加载中..."})}):(0,s.jsxs)("div",{className:"max-w-6xl mx-auto p-6",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"合并改写章节"}),(0,s.jsx)("p",{className:"text-gray-600",children:"将改写完成的章节合并为完整的小说文件"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"可合并的小说"}),0===e.length?(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:"暂无可合并的小说"}):(0,s.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,s.jsxs)("div",{className:"border rounded-lg p-4 flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-lg",children:e.title}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["已改写章节: ",e.rewrittenChaptersCount," / 总章节: ",e.chapterCount||0]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["创建时间: ",new Date(e.createdAt).toLocaleString()]})]}),(0,s.jsx)("button",{onClick:()=>x(e.id),disabled:c===e.id,className:"px-4 py-2 rounded-md font-medium ".concat(c===e.id?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"),children:c===e.id?"合并中...":"合并章节"})]},e.id))})]}),d.length>0&&(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"合并结果"}),(0,s.jsx)("div",{className:"space-y-3",children:d.map((e,t)=>(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsxs)("h3",{className:"text-sm font-medium text-green-800",children:["成功合并《",e.novelTitle,"》"]}),(0,s.jsxs)("p",{className:"text-sm text-green-700 mt-1",children:["文件保存在: ",e.filePath]})]})]})},t))})]})]})}}]);