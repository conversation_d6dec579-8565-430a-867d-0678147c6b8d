module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},22734,(e,t,r)=>{t.exports=e.x("fs",()=>require("fs"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},84168,e=>{"use strict";e.s(["chapterContextDb",()=>C,"chapterDb",()=>y,"characterDb",()=>v,"jobDb",()=>w,"novelContextDb",()=>N,"novelDb",()=>f,"presetDb",()=>I,"ruleDb",()=>S]);var t=e.i(22734),r=e.i(14747),n=e.i(54799);let a=r.default.join(process.cwd(),"data"),i=r.default.join(a,"novels.json"),o=r.default.join(a,"chapters.json"),s=r.default.join(a,"rewrite_rules.json"),l=r.default.join(a,"rewrite_jobs.json"),d=r.default.join(a,"characters.json"),u=r.default.join(a,"presets.json"),c=r.default.join(a,"novel-contexts.json"),p=r.default.join(a,"chapter-contexts.json");function h(){t.default.existsSync(a)||t.default.mkdirSync(a,{recursive:!0})}function m(e){if(h(),!t.default.existsSync(e))return[];try{let r=t.default.readFileSync(e,"utf-8");return JSON.parse(r)}catch(t){return console.error(`Error reading ${e}:`,t),[]}}function g(e,r){h();try{t.default.writeFileSync(e,JSON.stringify(r,null,2),"utf-8")}catch(t){throw console.error(`Error writing ${e}:`,t),t}}function x(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}let f={getAll:()=>m(i),getById:e=>m(i).find(t=>t.id===e),create:e=>{var t;let r=m(i),a=(t=e.title,n.default.createHash("md5").update(t).digest("hex").substring(0,18)),o=r.find(e=>e.id===a);if(o)return o.filename=e.filename,o.chapterCount=e.chapterCount,g(i,r),o;let s={...e,id:a,createdAt:new Date().toISOString()};return r.push(s),g(i,r),s},update:(e,t)=>{let r=m(i),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t},g(i,r),r[n])},delete:e=>{let t=m(i),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(i,t),!0)}},y={getAll:()=>m(o),getByNovelId:e=>m(o).filter(t=>t.novelId===e),getById:e=>m(o).find(t=>t.id===e),create:e=>{let t=m(o),r={...e,id:x(),createdAt:new Date().toISOString()};return t.push(r),g(o,t),r},createBatch:e=>{let t=m(o),r=e.map(e=>({...e,id:x(),createdAt:new Date().toISOString()}));return t.push(...r),g(o,t),r},delete:e=>{let t=m(o),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(o,t),!0)},deleteByNovelId:e=>{let t=m(o).filter(t=>t.novelId!==e);return g(o,t),!0}},S={getAll:()=>m(s),getById:e=>m(s).find(t=>t.id===e),create:e=>{let t=m(s),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(s,t),r},update:(e,t)=>{let r=m(s),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(s,r),r[n])},delete:e=>{let t=m(s),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(s,t),!0)}},w={getAll:()=>m(l),getById:e=>m(l).find(t=>t.id===e),create:e=>{let t=m(l),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(l,t),r},update:(e,t)=>{let r=m(l),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(l,r),r[n])},delete:e=>{let t=m(l),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(l,t),!0)}},v={getAll:()=>m(d),getByNovelId:e=>m(d).filter(t=>t.novelId===e),getById:e=>m(d).find(t=>t.id===e),create:e=>{let t=m(d),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(d,t),r},update:(e,t)=>{let r=m(d),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(d,r),r[n])},delete:e=>{let t=m(d),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(d,t),!0)},deleteByNovelId:e=>{let t=m(d).filter(t=>t.novelId!==e);return g(d,t),!0}},I={getAll:()=>m(u),getById:e=>m(u).find(t=>t.id===e),create:e=>{let t=m(u),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(u,t),r},update:(e,t)=>{let r=m(u),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(u,r),r[n])},delete:e=>{let t=m(u),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(u,t),!0)}},N={getAll:()=>m(c),getByNovelId:e=>m(c).find(t=>t.novelId===e),getById:e=>m(c).find(t=>t.id===e),create:e=>{let t=m(c),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(c,t),r},update:(e,t)=>{let r=m(c),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(c,r),r[n]},delete:e=>{let t=m(c),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(c,t),!0)}},C={getAll:()=>m(p),getByNovelId:e=>m(p).filter(t=>t.novelId===e),getByChapter:(e,t)=>m(p).find(r=>r.novelId===e&&r.chapterNumber===t),getById:e=>m(p).find(t=>t.id===e),create:e=>{let t=m(p),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(p,t),r},update:(e,t)=>{let r=m(p),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(p,r),r[n]},delete:e=>{let t=m(p),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(p,t),!0)},getContextWindow:(e,t,r=2)=>{let n=m(p).filter(t=>t.novelId===e),a=Math.max(1,t-r),i=t+r;return n.filter(e=>e.chapterNumber>=a&&e.chapterNumber<=i).sort((e,t)=>e.chapterNumber-t.chapterNumber)}}},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},27554,(e,t,r)=>{},15702,e=>{"use strict";e.s(["handler",()=>E,"patchFetch",()=>R,"routeModule",()=>I,"serverHooks",()=>A,"workAsyncStorage",()=>N,"workUnitAsyncStorage",()=>C],15702);var t=e.i(47909),r=e.i(74017),n=e.i(96250),a=e.i(59756),i=e.i(61916),o=e.i(69741),s=e.i(16795),l=e.i(87718),d=e.i(95169),u=e.i(47587),c=e.i(66012),p=e.i(70101),h=e.i(26937),m=e.i(10372),g=e.i(93695);e.i(52474);var x=e.i(220);e.s(["POST",()=>w],20336);var f=e.i(89171),y=e.i(84168);class S{async analyzeNovel(t){let{novelDb:r,chapterDb:n,novelContextDb:a}=await e.A(58566),{rewriteText:i}=await e.A(30017),o=r.getById(t);if(!o)throw Error("小说不存在");let s=n.getByNovelId(t);if(0===s.length)throw Error("小说没有章节");let l=s.sort((e,t)=>e.chapterNumber-t.chapterNumber).map(e=>`第${e.chapterNumber}章 ${e.title}

${e.content}`).join("\n\n---\n\n"),d=`
请分析以下小说的整体内容，提取关键信息：

小说标题：${o.title}

小说内容：
${l}

请按照以下JSON格式返回分析结果：
{
  "summary": "小说整体摘要（200-300字）",
  "mainCharacters": [
    {
      "name": "人物姓名",
      "role": "角色类型（男主/女主/配角/反派等）",
      "description": "人物描述",
      "relationships": "人物关系"
    }
  ],
  "worldSetting": "世界观设定描述",
  "writingStyle": "写作风格特征描述",
  "mainPlotlines": ["主要情节线1", "主要情节线2"],
  "themes": ["主题1", "主题2"],
  "tone": "整体语调风格"
}

请确保返回的是有效的JSON格式。
`;try{let e,r=await i({originalText:d,rules:"请严格按照要求的JSON格式返回分析结果，不要添加任何其他内容。",chapterTitle:"小说整体分析",chapterNumber:0,model:"gemini-2.5-flash-lite"});if(!r.success)throw Error(`分析失败: ${r.error}`);try{let t=r.rewrittenText.match(/\{[\s\S]*\}/);if(t)e=JSON.parse(t[0]);else throw Error("无法找到JSON格式的分析结果")}catch(t){console.error("解析分析结果失败:",t),e={summary:"分析结果解析失败，请重新分析",mainCharacters:[],worldSetting:"未知",writingStyle:"未知",mainPlotlines:[],themes:[],tone:"未知"}}let n=a.getByNovelId(t);if(n)return a.update(n.id,{summary:e.summary,mainCharacters:e.mainCharacters,worldSetting:e.worldSetting,writingStyle:e.writingStyle,mainPlotlines:e.mainPlotlines,themes:e.themes,tone:e.tone});return a.create({novelId:t,summary:e.summary,mainCharacters:e.mainCharacters,worldSetting:e.worldSetting,writingStyle:e.writingStyle,mainPlotlines:e.mainPlotlines,themes:e.themes,tone:e.tone})}catch(e){throw console.error("小说分析失败:",e),e}}async analyzeChapter(t,r){let{chapterDb:n,novelContextDb:a,chapterContextDb:i}=await e.A(58566),{rewriteText:o}=await e.A(30017),s=n.getByNovelId(t).find(e=>e.chapterNumber===r);if(!s)throw Error(`第${r}章不存在`);let l=a.getByNovelId(t);if(!l)throw Error("请先分析小说整体上下文");let d=i.getByChapter(t,r-1),u=`
请分析以下章节的内容，提取关键信息：

小说背景：
${l.summary}

主要人物：
${l.mainCharacters.map(e=>`${e.name}(${e.role}): ${e.description}`).join("\n")}

${d?`前一章摘要：
${d.previousChapterSummary||"无"}`:""}

当前章节：
第${s.chapterNumber}章 ${s.title}

${s.content}

请按照以下JSON格式返回分析结果：
{
  "keyEvents": ["关键事件1", "关键事件2"],
  "characterStates": [
    {
      "name": "人物姓名",
      "status": "人物在本章的状态",
      "emotions": "情感状态",
      "relationships": "关系变化"
    }
  ],
  "plotProgress": "情节推进要点",
  "previousChapterSummary": "对前一章的简要总结",
  "nextChapterHints": "对下一章的暗示或铺垫",
  "contextualNotes": "其他重要的上下文注释"
}

请确保返回的是有效的JSON格式。
`;try{let e,n=await o({originalText:u,rules:"请严格按照要求的JSON格式返回分析结果，不要添加任何其他内容。",chapterTitle:`第${r}章分析`,chapterNumber:r,model:"gemini-2.5-flash-lite"});if(!n.success)throw Error(`章节分析失败: ${n.error}`);try{let t=n.rewrittenText.match(/\{[\s\S]*\}/);if(t)e=JSON.parse(t[0]);else throw Error("无法找到JSON格式的分析结果")}catch(t){console.error("解析章节分析结果失败:",t),e={keyEvents:[],characterStates:[],plotProgress:"分析失败",previousChapterSummary:"",nextChapterHints:"",contextualNotes:"分析结果解析失败"}}let a=i.getByChapter(t,r);if(a)return i.update(a.id,{keyEvents:e.keyEvents,characterStates:e.characterStates,plotProgress:e.plotProgress,previousChapterSummary:e.previousChapterSummary,nextChapterHints:e.nextChapterHints,contextualNotes:e.contextualNotes});return i.create({novelId:t,chapterNumber:r,keyEvents:e.keyEvents,characterStates:e.characterStates,plotProgress:e.plotProgress,previousChapterSummary:e.previousChapterSummary,nextChapterHints:e.nextChapterHints,contextualNotes:e.contextualNotes})}catch(e){throw console.error(`第${r}章分析失败:`,e),e}}async analyzeAllChapters(t){let{chapterDb:r}=await e.A(58566),n=r.getByNovelId(t).sort((e,t)=>e.chapterNumber-t.chapterNumber),a=[];for(let e of n)try{console.log(`正在分析第${e.chapterNumber}章: ${e.title}`);let r=await this.analyzeChapter(t,e.chapterNumber);a.push(r),await new Promise(e=>setTimeout(e,2e3))}catch(t){console.error(`第${e.chapterNumber}章分析失败:`,t)}return a}}async function w(e){try{let{novelId:t,analyzeChapters:r=!1}=await e.json();if(!t)return f.NextResponse.json({success:!1,error:"小说ID不能为空"},{status:400});let n=y.novelDb.getById(t);if(!n)return f.NextResponse.json({success:!1,error:"小说不存在"},{status:404});try{let e=new S;console.log(`开始分析小说: ${n.title}`);let a=await e.analyzeNovel(t),i=[];return r&&(console.log("开始分析章节上下文..."),i=await e.analyzeAllChapters(t)),f.NextResponse.json({success:!0,data:{novelContext:a,chapterContexts:i,message:`成功分析小说《${n.title}》${r?`，共分析 ${i.length} 个章节`:""}`}})}catch(e){return console.error("上下文分析失败:",e),f.NextResponse.json({success:!1,error:`上下文分析失败: ${e instanceof Error?e.message:"未知错误"}`},{status:500})}}catch(e){return console.error("分析请求处理失败:",e),f.NextResponse.json({success:!1,error:"分析请求处理失败"},{status:500})}}new S;var v=e.i(20336);let I=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/context/analyze/route",pathname:"/api/context/analyze",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/context/analyze/route.ts",nextConfigOutput:"",userland:v}),{workAsyncStorage:N,workUnitAsyncStorage:C,serverHooks:A}=I;function R(){return(0,n.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:C})}async function E(e,t,n){var f;let y="/api/context/analyze/route";y=y.replace(/\/index$/,"")||"/";let S=await I.prepare(e,t,{srcPage:y,multiZoneDraftMode:!1});if(!S)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:w,params:v,nextConfig:N,isDraftMode:C,prerenderManifest:A,routerServerContext:R,isOnDemandRevalidate:E,revalidateOnlyGenerated:b,resolvedPathname:O}=S,j=(0,o.normalizeAppPath)(y),D=!!(A.dynamicRoutes[j]||A.routes[O]);if(D&&!C){let e=!!A.routes[O],t=A.dynamicRoutes[j];if(t&&!1===t.fallback&&!e)throw new g.NoFallbackError}let $=null;!D||I.isDev||C||($="/index"===($=O)?"/":$);let P=!0===I.isDev||!D,B=D&&!P,T=e.method||"GET",k=(0,i.getTracer)(),_=k.getActiveScopeSpan(),q={params:v,prerenderManifest:A,renderOpts:{experimental:{cacheComponents:!!N.experimental.cacheComponents,authInterrupts:!!N.experimental.authInterrupts},supportsDynamicResponse:P,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=N.experimental)?void 0:f.cacheLife,isRevalidate:B,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>I.onRequestError(e,t,n,R)},sharedContext:{buildId:w}},H=new s.NodeNextRequest(e),M=new s.NodeNextResponse(t),U=l.NextRequestAdapter.fromNodeNextRequest(H,(0,l.signalFromNodeResponse)(t));try{let o=async r=>I.handle(U,q).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=k.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${T} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${T} ${e.url}`)}),s=async i=>{var s,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&E&&b&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let s=await o(i);e.fetchMetrics=q.renderOpts.fetchMetrics;let l=q.renderOpts.pendingWaitUntil;l&&n.waitUntil&&(n.waitUntil(l),l=void 0);let d=q.renderOpts.collectedTags;if(!D)return await (0,c.sendResponse)(H,M,s,q.renderOpts.pendingWaitUntil),null;{let e=await s.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(s.headers);d&&(t[m.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==q.renderOpts.collectedRevalidate&&!(q.renderOpts.collectedRevalidate>=m.INFINITE_CACHE)&&q.renderOpts.collectedRevalidate,n=void 0===q.renderOpts.collectedExpire||q.renderOpts.collectedExpire>=m.INFINITE_CACHE?void 0:q.renderOpts.collectedExpire;return{value:{kind:x.CachedRouteKind.APP_ROUTE,status:s.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await I.onRequestError(e,t,{routerKind:"App Router",routePath:y,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:B,isOnDemandRevalidate:E})},R),t}},g=await I.handleResponse({req:e,nextConfig:N,cacheKey:$,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:A,isRoutePPREnabled:!1,isOnDemandRevalidate:E,revalidateOnlyGenerated:b,responseGenerator:d,waitUntil:n.waitUntil});if(!D)return null;if((null==g||null==(s=g.value)?void 0:s.kind)!==x.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==g||null==(l=g.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",E?"REVALIDATED":g.isMiss?"MISS":g.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,p.fromNodeOutgoingHttpHeaders)(g.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&D||f.delete(m.NEXT_CACHE_TAGS_HEADER),!g.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,h.getCacheControlHeader)(g.cacheControl)),await (0,c.sendResponse)(H,M,new Response(g.value.body,{headers:f,status:g.value.status||200})),null};_?await s(_):await k.withPropagatedContext(e.headers,()=>k.trace(d.BaseServerSpan.handleRequest,{spanName:`${T} ${e.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":T,"http.target":e.url}},s))}catch(t){if(_||t instanceof g.NoFallbackError||await I.onRequestError(e,t,{routerKind:"App Router",routePath:j,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:B,isOnDemandRevalidate:E})}),D)throw t;return await (0,c.sendResponse)(H,M,new Response(null,{status:500})),null}}},58566,e=>{e.v(e=>Promise.resolve().then(()=>e(84168)))},30017,e=>{e.v(t=>Promise.all(["server/chunks/src_lib_gemini_ts_7aaf7081._.js"].map(t=>e.l(t))).then(()=>t(71994)))}];

//# sourceMappingURL=%5Broot-of-the-server%5D__14cf25c2._.js.map