module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},22734,(e,t,r)=>{t.exports=e.x("fs",()=>require("fs"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},84168,e=>{"use strict";e.s(["chapterContextDb",()=>R,"chapterDb",()=>y,"characterDb",()=>w,"jobDb",()=>v,"novelContextDb",()=>I,"novelDb",()=>m,"presetDb",()=>D,"ruleDb",()=>S]);var t=e.i(22734),r=e.i(14747),n=e.i(54799);let i=r.default.join(process.cwd(),"data"),a=r.default.join(i,"novels.json"),s=r.default.join(i,"chapters.json"),l=r.default.join(i,"rewrite_rules.json"),o=r.default.join(i,"rewrite_jobs.json"),d=r.default.join(i,"characters.json"),u=r.default.join(i,"presets.json"),c=r.default.join(i,"novel-contexts.json"),p=r.default.join(i,"chapter-contexts.json");function f(){t.default.existsSync(i)||t.default.mkdirSync(i,{recursive:!0})}function h(e){if(f(),!t.default.existsSync(e))return[];try{let r=t.default.readFileSync(e,"utf-8");return JSON.parse(r)}catch(t){return console.error(`Error reading ${e}:`,t),[]}}function g(e,r){f();try{t.default.writeFileSync(e,JSON.stringify(r,null,2),"utf-8")}catch(t){throw console.error(`Error writing ${e}:`,t),t}}function x(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}let m={getAll:()=>h(a),getById:e=>h(a).find(t=>t.id===e),create:e=>{var t;let r=h(a),i=(t=e.title,n.default.createHash("md5").update(t).digest("hex").substring(0,18)),s=r.find(e=>e.id===i);if(s)return s.filename=e.filename,s.chapterCount=e.chapterCount,g(a,r),s;let l={...e,id:i,createdAt:new Date().toISOString()};return r.push(l),g(a,r),l},update:(e,t)=>{let r=h(a),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t},g(a,r),r[n])},delete:e=>{let t=h(a),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(a,t),!0)}},y={getAll:()=>h(s),getByNovelId:e=>h(s).filter(t=>t.novelId===e),getById:e=>h(s).find(t=>t.id===e),create:e=>{let t=h(s),r={...e,id:x(),createdAt:new Date().toISOString()};return t.push(r),g(s,t),r},createBatch:e=>{let t=h(s),r=e.map(e=>({...e,id:x(),createdAt:new Date().toISOString()}));return t.push(...r),g(s,t),r},delete:e=>{let t=h(s),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(s,t),!0)},deleteByNovelId:e=>{let t=h(s).filter(t=>t.novelId!==e);return g(s,t),!0}},S={getAll:()=>h(l),getById:e=>h(l).find(t=>t.id===e),create:e=>{let t=h(l),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(l,t),r},update:(e,t)=>{let r=h(l),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(l,r),r[n])},delete:e=>{let t=h(l),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(l,t),!0)}},v={getAll:()=>h(o),getById:e=>h(o).find(t=>t.id===e),create:e=>{let t=h(o),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(o,t),r},update:(e,t)=>{let r=h(o),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(o,r),r[n])},delete:e=>{let t=h(o),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(o,t),!0)}},w={getAll:()=>h(d),getByNovelId:e=>h(d).filter(t=>t.novelId===e),getById:e=>h(d).find(t=>t.id===e),create:e=>{let t=h(d),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(d,t),r},update:(e,t)=>{let r=h(d),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(d,r),r[n])},delete:e=>{let t=h(d),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(d,t),!0)},deleteByNovelId:e=>{let t=h(d).filter(t=>t.novelId!==e);return g(d,t),!0}},D={getAll:()=>h(u),getById:e=>h(u).find(t=>t.id===e),create:e=>{let t=h(u),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(u,t),r},update:(e,t)=>{let r=h(u),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(u,r),r[n])},delete:e=>{let t=h(u),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(u,t),!0)}},I={getAll:()=>h(c),getByNovelId:e=>h(c).find(t=>t.novelId===e),getById:e=>h(c).find(t=>t.id===e),create:e=>{let t=h(c),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(c,t),r},update:(e,t)=>{let r=h(c),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(c,r),r[n]},delete:e=>{let t=h(c),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(c,t),!0)}},R={getAll:()=>h(p),getByNovelId:e=>h(p).filter(t=>t.novelId===e),getByChapter:(e,t)=>h(p).find(r=>r.novelId===e&&r.chapterNumber===t),getById:e=>h(p).find(t=>t.id===e),create:e=>{let t=h(p),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(p,t),r},update:(e,t)=>{let r=h(p),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(p,r),r[n]},delete:e=>{let t=h(p),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(p,t),!0)},getContextWindow:(e,t,r=2)=>{let n=h(p).filter(t=>t.novelId===e),i=Math.max(1,t-r),a=t+r;return n.filter(e=>e.chapterNumber>=i&&e.chapterNumber<=a).sort((e,t)=>e.chapterNumber-t.chapterNumber)}}},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},23122,e=>{"use strict";e.s(["fileManager",()=>i]);var t=e.i(22734),r=e.i(14747);class n{static instance;baseDir;constructor(){this.baseDir=process.cwd()}static getInstance(){return n.instance||(n.instance=new n),n.instance}ensureDir(e){t.default.existsSync(e)||t.default.mkdirSync(e,{recursive:!0})}getNovelsDir(){return r.default.join(this.baseDir,"..","novels")}getChaptersDir(){return r.default.join(this.baseDir,"..","chapters")}getDataDir(){let e=r.default.join(this.baseDir,"data");return this.ensureDir(e),e}getRewrittenDir(){let e=r.default.join(this.getDataDir(),"rewritten");return this.ensureDir(e),e}getNovelRewrittenDir(e){let t=r.default.join(this.getRewrittenDir(),this.sanitizeFilename(e));return this.ensureDir(t),t}getDoneNovelsDir(){let e=r.default.join(this.baseDir,"..","done-novels");return this.ensureDir(e),e}getNovelChaptersDir(e){let t=this.getChaptersDir();this.ensureDir(t);let n=r.default.join(t,this.sanitizeFilename(e));return this.ensureDir(n),n}sanitizeFilename(e){return e.replace(/[<>:"/\\|?*]/g,"_").trim()}readFile(e){try{return t.default.readFileSync(e,"utf-8")}catch(t){throw console.error(`读取文件失败: ${e}`,t),t}}writeFile(e,n){try{let i=r.default.dirname(e);this.ensureDir(i),t.default.writeFileSync(e,n,"utf-8")}catch(t){throw console.error(`写入文件失败: ${e}`,t),t}}fileExists(e){return t.default.existsSync(e)}listFiles(e,n){try{if(!t.default.existsSync(e))return[];let i=t.default.readdirSync(e);if(n)return i.filter(e=>{let t=r.default.extname(e).toLowerCase();return n.includes(t)});return i}catch(t){return console.error(`读取目录失败: ${e}`,t),[]}}getFileStats(e){try{return t.default.statSync(e)}catch(t){return console.error(`获取文件信息失败: ${e}`,t),null}}deleteFile(e){try{if(t.default.existsSync(e))return t.default.unlinkSync(e),!0;return!1}catch(t){return console.error(`删除文件失败: ${e}`,t),!1}}deleteDir(e){try{if(t.default.existsSync(e))return t.default.rmSync(e,{recursive:!0,force:!0}),!0;return!1}catch(t){return console.error(`删除目录失败: ${e}`,t),!1}}copyFile(e,n){try{let i=r.default.dirname(n);return this.ensureDir(i),t.default.copyFileSync(e,n),!0}catch(t){return console.error(`复制文件失败: ${e} -> ${n}`,t),!1}}moveFile(e,n){try{let i=r.default.dirname(n);return this.ensureDir(i),t.default.renameSync(e,n),!0}catch(t){return console.error(`移动文件失败: ${e} -> ${n}`,t),!1}}getDirSize(e){let n=0;try{if(!t.default.existsSync(e))return 0;for(let i of t.default.readdirSync(e)){let a=r.default.join(e,i),s=t.default.statSync(a);s.isDirectory()?n+=this.getDirSize(a):n+=s.size}}catch(t){console.error(`计算目录大小失败: ${e}`,t)}return n}formatFileSize(e){if(0===e)return"0 B";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB"][t]}createBackup(e){try{let t=new Date().toISOString().replace(/[:.]/g,"-"),n=r.default.extname(e),i=r.default.basename(e,n),a=r.default.dirname(e),s=r.default.join(a,`${i}_backup_${t}${n}`);if(this.copyFile(e,s))return s;return null}catch(t){return console.error(`创建备份失败: ${e}`,t),null}}mergeRewrittenChapters(e){try{let t=this.getNovelRewrittenDir(e),n=this.getDoneNovelsDir(),i=this.listFiles(t,[".txt"]).filter(e=>e.startsWith("chapter_")&&e.includes("_rewritten")).sort((e,t)=>{let r=parseInt(e.match(/chapter_(\d+)/)?.[1]||"0"),n=parseInt(t.match(/chapter_(\d+)/)?.[1]||"0");return r-n});if(0===i.length)return{success:!1,error:"没有找到改写的章节文件"};let a="";for(let e of i){let n=r.default.join(t,e),i=this.readFile(n);a+=i+"\n\n"}let s=new Date().toISOString().replace(/[:.]/g,"-").substring(0,19),l=`${this.sanitizeFilename(e)}_merged_${s}.txt`,o=r.default.join(n,l);return this.writeFile(o,a.trim()),{success:!0,filePath:o}}catch(t){return console.error(`合并章节失败: ${e}`,t),{success:!1,error:t instanceof Error?t.message:"未知错误"}}}cleanupBackups(e,t=5){try{let n=this.listFiles(e).filter(e=>e.includes("_backup_")).map(t=>({name:t,path:r.default.join(e,t),stats:this.getFileStats(r.default.join(e,t))})).filter(e=>null!==e.stats).sort((e,t)=>t.stats.mtime.getTime()-e.stats.mtime.getTime());if(n.length>t)for(let e of n.slice(t))this.deleteFile(e.path)}catch(t){console.error(`清理备份文件失败: ${e}`,t)}}}let i=n.getInstance()},93129,(e,t,r)=>{},74862,e=>{"use strict";e.s(["handler",()=>N,"patchFetch",()=>b,"routeModule",()=>I,"serverHooks",()=>A,"workAsyncStorage",()=>R,"workUnitAsyncStorage",()=>j],74862);var t=e.i(47909),r=e.i(74017),n=e.i(96250),i=e.i(59756),a=e.i(61916),s=e.i(69741),l=e.i(16795),o=e.i(87718),d=e.i(95169),u=e.i(47587),c=e.i(66012),p=e.i(70101),f=e.i(26937),h=e.i(10372),g=e.i(93695);e.i(52474);var x=e.i(220);e.s(["GET",()=>w,"POST",()=>v],25565);var m=e.i(89171),y=e.i(84168),S=e.i(23122);async function v(e){try{let{novelId:t}=await e.json();if(!t)return m.NextResponse.json({success:!1,error:"小说ID不能为空"},{status:400});let r=y.novelDb.getById(t);if(!r)return m.NextResponse.json({success:!1,error:"小说不存在"},{status:404});let n=S.fileManager.mergeRewrittenChapters(r.title);if(!n.success)return m.NextResponse.json({success:!1,error:n.error},{status:500});return m.NextResponse.json({success:!0,data:{filePath:n.filePath,novelTitle:r.title},message:`成功合并小说《${r.title}》的改写章节`})}catch(e){return console.error("合并章节失败:",e),m.NextResponse.json({success:!1,error:"合并章节失败"},{status:500})}}async function w(){try{let e=y.novelDb.getAll(),t=[];for(let r of e){let e=S.fileManager.getNovelRewrittenDir(r.title),n=S.fileManager.listFiles(e,[".txt"]).filter(e=>e.startsWith("chapter_")&&e.includes("_rewritten"));n.length>0&&t.push({...r,rewrittenChaptersCount:n.length})}return m.NextResponse.json({success:!0,data:t})}catch(e){return console.error("获取可合并小说列表失败:",e),m.NextResponse.json({success:!1,error:"获取可合并小说列表失败"},{status:500})}}var D=e.i(25565);let I=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/merge/route",pathname:"/api/merge",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/merge/route.ts",nextConfigOutput:"",userland:D}),{workAsyncStorage:R,workUnitAsyncStorage:j,serverHooks:A}=I;function b(){return(0,n.patchFetch)({workAsyncStorage:R,workUnitAsyncStorage:j})}async function N(e,t,n){var m;let y="/api/merge/route";y=y.replace(/\/index$/,"")||"/";let S=await I.prepare(e,t,{srcPage:y,multiZoneDraftMode:!1});if(!S)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:v,params:w,nextConfig:D,isDraftMode:R,prerenderManifest:j,routerServerContext:A,isOnDemandRevalidate:b,revalidateOnlyGenerated:N,resolvedPathname:C}=S,O=(0,s.normalizeAppPath)(y),E=!!(j.dynamicRoutes[O]||j.routes[C]);if(E&&!R){let e=!!j.routes[C],t=j.dynamicRoutes[O];if(t&&!1===t.fallback&&!e)throw new g.NoFallbackError}let F=null;!E||I.isDev||R||(F="/index"===(F=C)?"/":F);let _=!0===I.isDev||!E,$=E&&!_,B=e.method||"GET",k=(0,a.getTracer)(),T=k.getActiveScopeSpan(),P={params:w,prerenderManifest:j,renderOpts:{experimental:{cacheComponents:!!D.experimental.cacheComponents,authInterrupts:!!D.experimental.authInterrupts},supportsDynamicResponse:_,incrementalCache:(0,i.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(m=D.experimental)?void 0:m.cacheLife,isRevalidate:$,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>I.onRequestError(e,t,n,A)},sharedContext:{buildId:v}},q=new l.NodeNextRequest(e),M=new l.NodeNextResponse(t),H=o.NextRequestAdapter.fromNodeNextRequest(q,(0,o.signalFromNodeResponse)(t));try{let s=async r=>I.handle(H,P).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=k.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let i=n.get("next.route");if(i){let e=`${B} ${i}`;r.setAttributes({"next.route":i,"http.route":i,"next.span_name":e}),r.updateName(e)}else r.updateName(`${B} ${e.url}`)}),l=async a=>{var l,o;let d=async({previousCacheEntry:r})=>{try{if(!(0,i.getRequestMeta)(e,"minimalMode")&&b&&N&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let l=await s(a);e.fetchMetrics=P.renderOpts.fetchMetrics;let o=P.renderOpts.pendingWaitUntil;o&&n.waitUntil&&(n.waitUntil(o),o=void 0);let d=P.renderOpts.collectedTags;if(!E)return await (0,c.sendResponse)(q,M,l,P.renderOpts.pendingWaitUntil),null;{let e=await l.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(l.headers);d&&(t[h.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==P.renderOpts.collectedRevalidate&&!(P.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&P.renderOpts.collectedRevalidate,n=void 0===P.renderOpts.collectedExpire||P.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:P.renderOpts.collectedExpire;return{value:{kind:x.CachedRouteKind.APP_ROUTE,status:l.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await I.onRequestError(e,t,{routerKind:"App Router",routePath:y,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:$,isOnDemandRevalidate:b})},A),t}},g=await I.handleResponse({req:e,nextConfig:D,cacheKey:F,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:j,isRoutePPREnabled:!1,isOnDemandRevalidate:b,revalidateOnlyGenerated:N,responseGenerator:d,waitUntil:n.waitUntil});if(!E)return null;if((null==g||null==(l=g.value)?void 0:l.kind)!==x.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==g||null==(o=g.value)?void 0:o.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,i.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",b?"REVALIDATED":g.isMiss?"MISS":g.isStale?"STALE":"HIT"),R&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(g.value.headers);return(0,i.getRequestMeta)(e,"minimalMode")&&E||m.delete(h.NEXT_CACHE_TAGS_HEADER),!g.cacheControl||t.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,f.getCacheControlHeader)(g.cacheControl)),await (0,c.sendResponse)(q,M,new Response(g.value.body,{headers:m,status:g.value.status||200})),null};T?await l(T):await k.withPropagatedContext(e.headers,()=>k.trace(d.BaseServerSpan.handleRequest,{spanName:`${B} ${e.url}`,kind:a.SpanKind.SERVER,attributes:{"http.method":B,"http.target":e.url}},l))}catch(t){if(T||t instanceof g.NoFallbackError||await I.onRequestError(e,t,{routerKind:"App Router",routePath:O,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:$,isOnDemandRevalidate:b})}),E)throw t;return await (0,c.sendResponse)(q,M,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__8c35d76d._.js.map