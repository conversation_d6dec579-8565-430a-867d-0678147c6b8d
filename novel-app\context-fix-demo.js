// 演示上下文系统修复
// 这个脚本展示了修复前后的对比

console.log('=== 上下文系统修复演示 ===\n');

// 模拟浏览器环境
global.window = {};

console.log('1. 修复前的问题:');
console.log('在浏览器环境中，contextAnalyzer 被设置为 null');
console.log('typeof window:', typeof window);
console.log('window === undefined:', window === undefined);

// 模拟修复前的逻辑
const oldContextAnalyzer = typeof window === 'undefined' ? 'new ContextAnalyzer()' : null;
console.log('修复前的 contextAnalyzer:', oldContextAnalyzer);
console.log('问题: 在浏览器中无法使用上下文功能 ❌\n');

console.log('2. 修复后的解决方案:');

// 模拟 ClientContextAnalyzer 类
class MockClientContextAnalyzer {
  constructor() {
    this.baseUrl = '';
  }
  
  async analyzeNovel(novelId) {
    return `分析小说 ${novelId} (通过 API 调用)`;
  }
  
  async getNovelContext(novelId) {
    return `获取小说 ${novelId} 的上下文 (通过 API 调用)`;
  }
  
  async analyzeChapter(novelId, chapterNumber) {
    return `分析小说 ${novelId} 第 ${chapterNumber} 章 (通过 API 调用)`;
  }
}

// 模拟 ContextAnalyzer 类
class MockContextAnalyzer {
  async analyzeNovel(novelId) {
    return `分析小说 ${novelId} (服务器端直接调用)`;
  }
  
  async getNovelContext(novelId) {
    return `获取小说 ${novelId} 的上下文 (服务器端直接调用)`;
  }
  
  async analyzeChapter(novelId, chapterNumber) {
    return `分析小说 ${novelId} 第 ${chapterNumber} 章 (服务器端直接调用)`;
  }
}

// 修复后的逻辑
const newContextAnalyzer = typeof window === 'undefined' 
  ? new MockContextAnalyzer() 
  : new MockClientContextAnalyzer();

console.log('修复后的 contextAnalyzer:', newContextAnalyzer.constructor.name);
console.log('可用方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(newContextAnalyzer))
  .filter(name => name !== 'constructor')
  .join(', '));

console.log('\n3. 功能测试:');

async function testContextAnalyzer() {
  try {
    const novelResult = await newContextAnalyzer.analyzeNovel('test-novel-id');
    console.log('✓ analyzeNovel:', novelResult);
    
    const contextResult = await newContextAnalyzer.getNovelContext('test-novel-id');
    console.log('✓ getNovelContext:', contextResult);
    
    const chapterResult = await newContextAnalyzer.analyzeChapter('test-novel-id', 1);
    console.log('✓ analyzeChapter:', chapterResult);
    
    console.log('\n✅ 所有功能都正常工作！');
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testContextAnalyzer();

console.log('\n4. 环境适配:');
console.log('✓ 服务器端: 使用 ContextAnalyzer (直接数据库访问)');
console.log('✓ 浏览器端: 使用 ClientContextAnalyzer (HTTP API 调用)');
console.log('✓ 统一接口: 两种环境使用相同的方法名和参数');
console.log('✓ 自动检测: 根据 window 对象自动选择合适的实现');

console.log('\n=== 修复完成 ===');
console.log('现在上下文系统可以在浏览器和服务器端无缝使用！');

// 清理模拟的 window 对象
delete global.window;
