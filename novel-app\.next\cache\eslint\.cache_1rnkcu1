[{"C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\chapters\\route.ts": "1", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\characters\\route.ts": "2", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\gemini\\reset\\route.ts": "3", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\gemini\\stats\\route.ts": "4", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\gemini\\test\\route.ts": "5", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\jobs\\route.ts": "6", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\novels\\route.ts": "7", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\presets\\route.ts": "8", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\rewrite\\route.ts": "9", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\rules\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\help\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\layout.tsx": "12", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\ApiKeyStats.tsx": "14", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\ChapterSelector.tsx": "15", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\CharacterManager.tsx": "16", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\JobHistory.tsx": "17", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\ModelConfigSelector.tsx": "18", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\NovelSelector.tsx": "19", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\RewriteProgress.tsx": "20", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\RuleEditor.tsx": "21", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\TaskManager.tsx": "22", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\Toast.tsx": "23", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\lib\\database.ts": "24", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\lib\\file-manager.ts": "25", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\lib\\gemini.ts": "26", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\lib\\novel-parser.ts": "27", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\context\\analyze\\route.ts": "28", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\context\\chapter\\route.ts": "29", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\context\\novel\\route.ts": "30", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\context\\window\\route.ts": "31", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\merge\\route.ts": "32", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\rewrite\\diagnostics\\route.ts": "33", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\rewrite\\retry\\route.ts": "34", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\merge\\page.tsx": "35", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\DiagnosticsPanel.tsx": "36", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\FailedChaptersRetry.tsx": "37", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\MergeNovels.tsx": "38", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\lib\\context-analyzer.ts": "39", "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\lib\\context-utils.ts": "40"}, {"size": 790, "mtime": 1758618695814, "results": "41", "hashOfConfig": "42"}, {"size": 3629, "mtime": 1758702919314, "results": "43", "hashOfConfig": "42"}, {"size": 488, "mtime": 1758698937048, "results": "44", "hashOfConfig": "42"}, {"size": 480, "mtime": 1758698923598, "results": "45", "hashOfConfig": "42"}, {"size": 545, "mtime": 1758698930545, "results": "46", "hashOfConfig": "42"}, {"size": 1823, "mtime": 1758698877294, "results": "47", "hashOfConfig": "42"}, {"size": 2653, "mtime": 1758618687579, "results": "48", "hashOfConfig": "42"}, {"size": 1257, "mtime": 1758705699205, "results": "49", "hashOfConfig": "42"}, {"size": 9817, "mtime": 1758772313228, "results": "50", "hashOfConfig": "42"}, {"size": 2930, "mtime": 1758702933586, "results": "51", "hashOfConfig": "42"}, {"size": 17076, "mtime": 1758619416736, "results": "52", "hashOfConfig": "42"}, {"size": 689, "mtime": 1758618130440, "results": "53", "hashOfConfig": "42"}, {"size": 9399, "mtime": 1758717649108, "results": "54", "hashOfConfig": "42"}, {"size": 8990, "mtime": 1758698915567, "results": "55", "hashOfConfig": "42"}, {"size": 8338, "mtime": 1758684022148, "results": "56", "hashOfConfig": "42"}, {"size": 7252, "mtime": 1758705825344, "results": "57", "hashOfConfig": "42"}, {"size": 7942, "mtime": 1758705609322, "results": "58", "hashOfConfig": "42"}, {"size": 7776, "mtime": 1758716666544, "results": "59", "hashOfConfig": "42"}, {"size": 6668, "mtime": 1758683979881, "results": "60", "hashOfConfig": "42"}, {"size": 15114, "mtime": 1758770153256, "results": "61", "hashOfConfig": "42"}, {"size": 4753, "mtime": 1758705741939, "results": "62", "hashOfConfig": "42"}, {"size": 3155, "mtime": 1758699003183, "results": "63", "hashOfConfig": "42"}, {"size": 1600, "mtime": 1758683730993, "results": "64", "hashOfConfig": "42"}, {"size": 18837, "mtime": 1758770600679, "results": "65", "hashOfConfig": "42"}, {"size": 9323, "mtime": 1758717582831, "results": "66", "hashOfConfig": "42"}, {"size": 29725, "mtime": 1758772052810, "results": "67", "hashOfConfig": "42"}, {"size": 9233, "mtime": 1758716264433, "results": "68", "hashOfConfig": "42"}, {"size": 2025, "mtime": 1758772341372, "results": "69", "hashOfConfig": "42"}, {"size": 3804, "mtime": 1758770825168, "results": "70", "hashOfConfig": "42"}, {"size": 3222, "mtime": 1758770808846, "results": "71", "hashOfConfig": "42"}, {"size": 1521, "mtime": 1758770836853, "results": "72", "hashOfConfig": "42"}, {"size": 2240, "mtime": 1758717601398, "results": "73", "hashOfConfig": "42"}, {"size": 7511, "mtime": 1758770046206, "results": "74", "hashOfConfig": "42"}, {"size": 7339, "mtime": 1758772095576, "results": "75", "hashOfConfig": "42"}, {"size": 119, "mtime": 1758717631956, "results": "76", "hashOfConfig": "42"}, {"size": 11049, "mtime": 1758770085074, "results": "77", "hashOfConfig": "42"}, {"size": 6295, "mtime": 1758769970340, "results": "78", "hashOfConfig": "42"}, {"size": 5192, "mtime": 1758717623654, "results": "79", "hashOfConfig": "42"}, {"size": 9603, "mtime": 1758772173389, "results": "80", "hashOfConfig": "42"}, {"size": 9046, "mtime": 1758772265874, "results": "81", "hashOfConfig": "42"}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8vz6cz", {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\chapters\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\characters\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\gemini\\reset\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\gemini\\stats\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\gemini\\test\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\jobs\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\novels\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\presets\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\rewrite\\route.ts", ["202", "203"], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\rules\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\help\\page.tsx", ["204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217"], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\page.tsx", ["218", "219"], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\ApiKeyStats.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\ChapterSelector.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\CharacterManager.tsx", ["220", "221"], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\JobHistory.tsx", ["222"], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\ModelConfigSelector.tsx", ["223"], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\NovelSelector.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\RewriteProgress.tsx", ["224", "225"], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\RuleEditor.tsx", ["226"], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\TaskManager.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\Toast.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\lib\\file-manager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\lib\\gemini.ts", ["227", "228", "229", "230", "231", "232", "233", "234", "235"], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\lib\\novel-parser.ts", ["236", "237", "238"], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\context\\analyze\\route.ts", ["239"], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\context\\chapter\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\context\\novel\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\context\\window\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\merge\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\rewrite\\diagnostics\\route.ts", ["240", "241", "242", "243", "244"], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\api\\rewrite\\retry\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\app\\merge\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\DiagnosticsPanel.tsx", ["245", "246"], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\FailedChaptersRetry.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\components\\MergeNovels.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\lib\\context-analyzer.ts", ["247"], [], "C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\src\\lib\\context-utils.ts", ["248", "249", "250", "251"], [], {"ruleId": "252", "severity": 2, "message": "253", "line": 160, "column": 60, "nodeType": "254", "messageId": "255", "endLine": 160, "endColumn": 63, "suggestions": "256"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 181, "column": 38, "nodeType": "254", "messageId": "255", "endLine": 181, "endColumn": 41, "suggestions": "257"}, {"ruleId": "258", "severity": 2, "message": "259", "line": 123, "column": 60, "nodeType": "260", "messageId": "261", "suggestions": "262"}, {"ruleId": "258", "severity": 2, "message": "259", "line": 123, "column": 63, "nodeType": "260", "messageId": "261", "suggestions": "263"}, {"ruleId": "258", "severity": 2, "message": "259", "line": 123, "column": 65, "nodeType": "260", "messageId": "261", "suggestions": "264"}, {"ruleId": "258", "severity": 2, "message": "259", "line": 123, "column": 68, "nodeType": "260", "messageId": "261", "suggestions": "265"}, {"ruleId": "258", "severity": 2, "message": "259", "line": 123, "column": 70, "nodeType": "260", "messageId": "261", "suggestions": "266"}, {"ruleId": "258", "severity": 2, "message": "259", "line": 123, "column": 73, "nodeType": "260", "messageId": "261", "suggestions": "267"}, {"ruleId": "258", "severity": 2, "message": "259", "line": 123, "column": 83, "nodeType": "260", "messageId": "261", "suggestions": "268"}, {"ruleId": "258", "severity": 2, "message": "259", "line": 123, "column": 86, "nodeType": "260", "messageId": "261", "suggestions": "269"}, {"ruleId": "258", "severity": 2, "message": "259", "line": 123, "column": 88, "nodeType": "260", "messageId": "261", "suggestions": "270"}, {"ruleId": "258", "severity": 2, "message": "259", "line": 123, "column": 91, "nodeType": "260", "messageId": "261", "suggestions": "271"}, {"ruleId": "258", "severity": 2, "message": "259", "line": 151, "column": 25, "nodeType": "260", "messageId": "261", "suggestions": "272"}, {"ruleId": "258", "severity": 2, "message": "259", "line": 151, "column": 29, "nodeType": "260", "messageId": "261", "suggestions": "273"}, {"ruleId": "258", "severity": 2, "message": "259", "line": 151, "column": 42, "nodeType": "260", "messageId": "261", "suggestions": "274"}, {"ruleId": "258", "severity": 2, "message": "259", "line": 151, "column": 53, "nodeType": "260", "messageId": "261", "suggestions": "275"}, {"ruleId": "276", "severity": 1, "message": "277", "line": 3, "column": 20, "nodeType": null, "messageId": "278", "endLine": 3, "endColumn": 29}, {"ruleId": "276", "severity": 1, "message": "279", "line": 13, "column": 17, "nodeType": null, "messageId": "278", "endLine": 13, "endColumn": 24}, {"ruleId": "276", "severity": 1, "message": "280", "line": 26, "column": 10, "nodeType": null, "messageId": "278", "endLine": 26, "endColumn": 17}, {"ruleId": "281", "severity": 1, "message": "282", "line": 40, "column": 6, "nodeType": "283", "endLine": 40, "endColumn": 15, "suggestions": "284"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 67, "column": 34, "nodeType": "254", "messageId": "255", "endLine": 67, "endColumn": 37, "suggestions": "285"}, {"ruleId": "276", "severity": 1, "message": "286", "line": 57, "column": 9, "nodeType": null, "messageId": "278", "endLine": 57, "endColumn": 32}, {"ruleId": "281", "severity": 1, "message": "287", "line": 67, "column": 6, "nodeType": "283", "endLine": 67, "endColumn": 13, "suggestions": "288"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 381, "column": 41, "nodeType": "254", "messageId": "255", "endLine": 381, "endColumn": 44, "suggestions": "289"}, {"ruleId": "276", "severity": 1, "message": "290", "line": 23, "column": 10, "nodeType": null, "messageId": "278", "endLine": 23, "endColumn": 23}, {"ruleId": "276", "severity": 1, "message": "291", "line": 157, "column": 46, "nodeType": null, "messageId": "278", "endLine": 157, "endColumn": 59}, {"ruleId": "252", "severity": 2, "message": "253", "line": 487, "column": 69, "nodeType": "254", "messageId": "255", "endLine": 487, "endColumn": 72, "suggestions": "292"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 488, "column": 54, "nodeType": "254", "messageId": "255", "endLine": 488, "endColumn": 57, "suggestions": "293"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 492, "column": 81, "nodeType": "254", "messageId": "255", "endLine": 492, "endColumn": 84, "suggestions": "294"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 493, "column": 87, "nodeType": "254", "messageId": "255", "endLine": 493, "endColumn": 90, "suggestions": "295"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 760, "column": 13, "nodeType": "254", "messageId": "255", "endLine": 760, "endColumn": 16, "suggestions": "296"}, {"ruleId": "297", "severity": 2, "message": "298", "line": 862, "column": 26, "nodeType": "299", "messageId": "300", "endLine": 862, "endColumn": 51}, {"ruleId": "252", "severity": 2, "message": "253", "line": 866, "column": 36, "nodeType": "254", "messageId": "255", "endLine": 866, "endColumn": 39, "suggestions": "301"}, {"ruleId": "297", "severity": 2, "message": "298", "line": 916, "column": 50, "nodeType": "299", "messageId": "300", "endLine": 916, "endColumn": 71}, {"ruleId": "276", "severity": 1, "message": "302", "line": 7, "column": 11, "nodeType": null, "messageId": "278", "endLine": 7, "endColumn": 22}, {"ruleId": "303", "severity": 2, "message": "304", "line": 156, "column": 7, "nodeType": "305", "messageId": "306", "endLine": 156, "endColumn": 21, "fix": "307"}, {"ruleId": "276", "severity": 1, "message": "308", "line": 156, "column": 7, "nodeType": null, "messageId": "278", "endLine": 156, "endColumn": 21}, {"ruleId": "252", "severity": 2, "message": "253", "line": 34, "column": 28, "nodeType": "254", "messageId": "255", "endLine": 34, "endColumn": 31, "suggestions": "309"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 79, "column": 40, "nodeType": "254", "messageId": "255", "endLine": 79, "endColumn": 43, "suggestions": "310"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 130, "column": 53, "nodeType": "254", "messageId": "255", "endLine": 130, "endColumn": 56, "suggestions": "311"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 163, "column": 39, "nodeType": "254", "messageId": "255", "endLine": 163, "endColumn": 42, "suggestions": "312"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 163, "column": 59, "nodeType": "254", "messageId": "255", "endLine": 163, "endColumn": 62, "suggestions": "313"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 163, "column": 77, "nodeType": "254", "messageId": "255", "endLine": 163, "endColumn": 80, "suggestions": "314"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 28, "column": 17, "nodeType": "254", "messageId": "255", "endLine": 28, "endColumn": 20, "suggestions": "315"}, {"ruleId": "281", "severity": 1, "message": "316", "line": 97, "column": 6, "nodeType": "283", "endLine": 97, "endColumn": 21, "suggestions": "317"}, {"ruleId": "276", "severity": 1, "message": "279", "line": 1, "column": 45, "nodeType": null, "messageId": "278", "endLine": 1, "endColumn": 52}, {"ruleId": "252", "severity": 2, "message": "253", "line": 110, "column": 69, "nodeType": "254", "messageId": "255", "endLine": 110, "endColumn": 72, "suggestions": "318"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 111, "column": 54, "nodeType": "254", "messageId": "255", "endLine": 111, "endColumn": 57, "suggestions": "319"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 115, "column": 81, "nodeType": "254", "messageId": "255", "endLine": 115, "endColumn": 84, "suggestions": "320"}, {"ruleId": "252", "severity": 2, "message": "253", "line": 128, "column": 89, "nodeType": "254", "messageId": "255", "endLine": 128, "endColumn": 92, "suggestions": "321"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["322", "323"], ["324", "325"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["326", "327", "328", "329"], ["330", "331", "332", "333"], ["334", "335", "336", "337"], ["338", "339", "340", "341"], ["342", "343", "344", "345"], ["346", "347", "348", "349"], ["350", "351", "352", "353"], ["354", "355", "356", "357"], ["358", "359", "360", "361"], ["362", "363", "364", "365"], ["366", "367", "368", "369"], ["370", "371", "372", "373"], ["374", "375", "376", "377"], ["378", "379", "380", "381"], "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "unusedVar", "'Chapter' is defined but never used.", "'loading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadCharacters' and 'onCharactersChange'. Either include them or remove the dependency array. If 'onCharactersChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", "ArrayExpression", ["382"], ["383", "384"], "'selectedConcurrencyInfo' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'checkJobStatus'. Either include it or remove the dependency array.", ["385"], ["386", "387"], "'customPresets' is assigned a value but never used.", "'chapterNumber' is assigned a value but never used.", ["388", "389"], ["390", "391"], ["392", "393"], ["394", "395"], ["396", "397"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["398", "399"], "'ParseConfig' is defined but never used.", "prefer-const", "'currentContent' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "400", "text": "401"}, "'currentContent' is assigned a value but never used.", ["402", "403"], ["404", "405"], ["406", "407"], ["408", "409"], ["410", "411"], ["412", "413"], ["414", "415"], "React Hook useEffect has a missing dependency: 'fetchDiagnostics'. Either include it or remove the dependency array.", ["416"], ["417", "418"], ["419", "420"], ["421", "422"], ["423", "424"], {"messageId": "425", "fix": "426", "desc": "427"}, {"messageId": "428", "fix": "429", "desc": "430"}, {"messageId": "425", "fix": "431", "desc": "427"}, {"messageId": "428", "fix": "432", "desc": "430"}, {"messageId": "433", "data": "434", "fix": "435", "desc": "436"}, {"messageId": "433", "data": "437", "fix": "438", "desc": "439"}, {"messageId": "433", "data": "440", "fix": "441", "desc": "442"}, {"messageId": "433", "data": "443", "fix": "444", "desc": "445"}, {"messageId": "433", "data": "446", "fix": "447", "desc": "436"}, {"messageId": "433", "data": "448", "fix": "449", "desc": "439"}, {"messageId": "433", "data": "450", "fix": "451", "desc": "442"}, {"messageId": "433", "data": "452", "fix": "453", "desc": "445"}, {"messageId": "433", "data": "454", "fix": "455", "desc": "436"}, {"messageId": "433", "data": "456", "fix": "457", "desc": "439"}, {"messageId": "433", "data": "458", "fix": "459", "desc": "442"}, {"messageId": "433", "data": "460", "fix": "461", "desc": "445"}, {"messageId": "433", "data": "462", "fix": "463", "desc": "436"}, {"messageId": "433", "data": "464", "fix": "465", "desc": "439"}, {"messageId": "433", "data": "466", "fix": "467", "desc": "442"}, {"messageId": "433", "data": "468", "fix": "469", "desc": "445"}, {"messageId": "433", "data": "470", "fix": "471", "desc": "436"}, {"messageId": "433", "data": "472", "fix": "473", "desc": "439"}, {"messageId": "433", "data": "474", "fix": "475", "desc": "442"}, {"messageId": "433", "data": "476", "fix": "477", "desc": "445"}, {"messageId": "433", "data": "478", "fix": "479", "desc": "436"}, {"messageId": "433", "data": "480", "fix": "481", "desc": "439"}, {"messageId": "433", "data": "482", "fix": "483", "desc": "442"}, {"messageId": "433", "data": "484", "fix": "485", "desc": "445"}, {"messageId": "433", "data": "486", "fix": "487", "desc": "436"}, {"messageId": "433", "data": "488", "fix": "489", "desc": "439"}, {"messageId": "433", "data": "490", "fix": "491", "desc": "442"}, {"messageId": "433", "data": "492", "fix": "493", "desc": "445"}, {"messageId": "433", "data": "494", "fix": "495", "desc": "436"}, {"messageId": "433", "data": "496", "fix": "497", "desc": "439"}, {"messageId": "433", "data": "498", "fix": "499", "desc": "442"}, {"messageId": "433", "data": "500", "fix": "501", "desc": "445"}, {"messageId": "433", "data": "502", "fix": "503", "desc": "436"}, {"messageId": "433", "data": "504", "fix": "505", "desc": "439"}, {"messageId": "433", "data": "506", "fix": "507", "desc": "442"}, {"messageId": "433", "data": "508", "fix": "509", "desc": "445"}, {"messageId": "433", "data": "510", "fix": "511", "desc": "436"}, {"messageId": "433", "data": "512", "fix": "513", "desc": "439"}, {"messageId": "433", "data": "514", "fix": "515", "desc": "442"}, {"messageId": "433", "data": "516", "fix": "517", "desc": "445"}, {"messageId": "433", "data": "518", "fix": "519", "desc": "436"}, {"messageId": "433", "data": "520", "fix": "521", "desc": "439"}, {"messageId": "433", "data": "522", "fix": "523", "desc": "442"}, {"messageId": "433", "data": "524", "fix": "525", "desc": "445"}, {"messageId": "433", "data": "526", "fix": "527", "desc": "436"}, {"messageId": "433", "data": "528", "fix": "529", "desc": "439"}, {"messageId": "433", "data": "530", "fix": "531", "desc": "442"}, {"messageId": "433", "data": "532", "fix": "533", "desc": "445"}, {"messageId": "433", "data": "534", "fix": "535", "desc": "436"}, {"messageId": "433", "data": "536", "fix": "537", "desc": "439"}, {"messageId": "433", "data": "538", "fix": "539", "desc": "442"}, {"messageId": "433", "data": "540", "fix": "541", "desc": "445"}, {"messageId": "433", "data": "542", "fix": "543", "desc": "436"}, {"messageId": "433", "data": "544", "fix": "545", "desc": "439"}, {"messageId": "433", "data": "546", "fix": "547", "desc": "442"}, {"messageId": "433", "data": "548", "fix": "549", "desc": "445"}, {"desc": "550", "fix": "551"}, {"messageId": "425", "fix": "552", "desc": "427"}, {"messageId": "428", "fix": "553", "desc": "430"}, {"desc": "554", "fix": "555"}, {"messageId": "425", "fix": "556", "desc": "427"}, {"messageId": "428", "fix": "557", "desc": "430"}, {"messageId": "425", "fix": "558", "desc": "427"}, {"messageId": "428", "fix": "559", "desc": "430"}, {"messageId": "425", "fix": "560", "desc": "427"}, {"messageId": "428", "fix": "561", "desc": "430"}, {"messageId": "425", "fix": "562", "desc": "427"}, {"messageId": "428", "fix": "563", "desc": "430"}, {"messageId": "425", "fix": "564", "desc": "427"}, {"messageId": "428", "fix": "565", "desc": "430"}, {"messageId": "425", "fix": "566", "desc": "427"}, {"messageId": "428", "fix": "567", "desc": "430"}, {"messageId": "425", "fix": "568", "desc": "427"}, {"messageId": "428", "fix": "569", "desc": "430"}, [4238, 4262], "const currentContent = '';", {"messageId": "425", "fix": "570", "desc": "427"}, {"messageId": "428", "fix": "571", "desc": "430"}, {"messageId": "425", "fix": "572", "desc": "427"}, {"messageId": "428", "fix": "573", "desc": "430"}, {"messageId": "425", "fix": "574", "desc": "427"}, {"messageId": "428", "fix": "575", "desc": "430"}, {"messageId": "425", "fix": "576", "desc": "427"}, {"messageId": "428", "fix": "577", "desc": "430"}, {"messageId": "425", "fix": "578", "desc": "427"}, {"messageId": "428", "fix": "579", "desc": "430"}, {"messageId": "425", "fix": "580", "desc": "427"}, {"messageId": "428", "fix": "581", "desc": "430"}, {"messageId": "425", "fix": "582", "desc": "427"}, {"messageId": "428", "fix": "583", "desc": "430"}, {"desc": "584", "fix": "585"}, {"messageId": "425", "fix": "586", "desc": "427"}, {"messageId": "428", "fix": "587", "desc": "430"}, {"messageId": "425", "fix": "588", "desc": "427"}, {"messageId": "428", "fix": "589", "desc": "430"}, {"messageId": "425", "fix": "590", "desc": "427"}, {"messageId": "428", "fix": "591", "desc": "430"}, {"messageId": "425", "fix": "592", "desc": "427"}, {"messageId": "428", "fix": "593", "desc": "430"}, "suggestUnknown", {"range": "594", "text": "595"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "596", "text": "597"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "598", "text": "595"}, {"range": "599", "text": "597"}, "replaceWithAlt", {"alt": "600"}, {"range": "601", "text": "602"}, "Replace with `&quot;`.", {"alt": "603"}, {"range": "604", "text": "605"}, "Replace with `&ldquo;`.", {"alt": "606"}, {"range": "607", "text": "608"}, "Replace with `&#34;`.", {"alt": "609"}, {"range": "610", "text": "611"}, "Replace with `&rdquo;`.", {"alt": "600"}, {"range": "612", "text": "613"}, {"alt": "603"}, {"range": "614", "text": "615"}, {"alt": "606"}, {"range": "616", "text": "617"}, {"alt": "609"}, {"range": "618", "text": "619"}, {"alt": "600"}, {"range": "620", "text": "621"}, {"alt": "603"}, {"range": "622", "text": "623"}, {"alt": "606"}, {"range": "624", "text": "625"}, {"alt": "609"}, {"range": "626", "text": "627"}, {"alt": "600"}, {"range": "628", "text": "629"}, {"alt": "603"}, {"range": "630", "text": "631"}, {"alt": "606"}, {"range": "632", "text": "633"}, {"alt": "609"}, {"range": "634", "text": "635"}, {"alt": "600"}, {"range": "636", "text": "637"}, {"alt": "603"}, {"range": "638", "text": "639"}, {"alt": "606"}, {"range": "640", "text": "641"}, {"alt": "609"}, {"range": "642", "text": "643"}, {"alt": "600"}, {"range": "644", "text": "645"}, {"alt": "603"}, {"range": "646", "text": "647"}, {"alt": "606"}, {"range": "648", "text": "649"}, {"alt": "609"}, {"range": "650", "text": "651"}, {"alt": "600"}, {"range": "652", "text": "653"}, {"alt": "603"}, {"range": "654", "text": "655"}, {"alt": "606"}, {"range": "656", "text": "657"}, {"alt": "609"}, {"range": "658", "text": "659"}, {"alt": "600"}, {"range": "660", "text": "661"}, {"alt": "603"}, {"range": "662", "text": "663"}, {"alt": "606"}, {"range": "664", "text": "665"}, {"alt": "609"}, {"range": "666", "text": "667"}, {"alt": "600"}, {"range": "668", "text": "669"}, {"alt": "603"}, {"range": "670", "text": "671"}, {"alt": "606"}, {"range": "672", "text": "673"}, {"alt": "609"}, {"range": "674", "text": "675"}, {"alt": "600"}, {"range": "676", "text": "677"}, {"alt": "603"}, {"range": "678", "text": "679"}, {"alt": "606"}, {"range": "680", "text": "681"}, {"alt": "609"}, {"range": "682", "text": "683"}, {"alt": "600"}, {"range": "684", "text": "685"}, {"alt": "603"}, {"range": "686", "text": "687"}, {"alt": "606"}, {"range": "688", "text": "689"}, {"alt": "609"}, {"range": "690", "text": "691"}, {"alt": "600"}, {"range": "692", "text": "693"}, {"alt": "603"}, {"range": "694", "text": "695"}, {"alt": "606"}, {"range": "696", "text": "697"}, {"alt": "609"}, {"range": "698", "text": "699"}, {"alt": "600"}, {"range": "700", "text": "701"}, {"alt": "603"}, {"range": "702", "text": "703"}, {"alt": "606"}, {"range": "704", "text": "705"}, {"alt": "609"}, {"range": "706", "text": "707"}, {"alt": "600"}, {"range": "708", "text": "709"}, {"alt": "603"}, {"range": "710", "text": "711"}, {"alt": "606"}, {"range": "712", "text": "713"}, {"alt": "609"}, {"range": "714", "text": "715"}, "Update the dependencies array to be: [loadCharacters, novelId, onCharactersChange]", {"range": "716", "text": "717"}, {"range": "718", "text": "595"}, {"range": "719", "text": "597"}, "Update the dependencies array to be: [checkJobStatus, jobId]", {"range": "720", "text": "721"}, {"range": "722", "text": "595"}, {"range": "723", "text": "597"}, {"range": "724", "text": "595"}, {"range": "725", "text": "597"}, {"range": "726", "text": "595"}, {"range": "727", "text": "597"}, {"range": "728", "text": "595"}, {"range": "729", "text": "597"}, {"range": "730", "text": "595"}, {"range": "731", "text": "597"}, {"range": "732", "text": "595"}, {"range": "733", "text": "597"}, {"range": "734", "text": "595"}, {"range": "735", "text": "597"}, {"range": "736", "text": "595"}, {"range": "737", "text": "597"}, {"range": "738", "text": "595"}, {"range": "739", "text": "597"}, {"range": "740", "text": "595"}, {"range": "741", "text": "597"}, {"range": "742", "text": "595"}, {"range": "743", "text": "597"}, {"range": "744", "text": "595"}, {"range": "745", "text": "597"}, {"range": "746", "text": "595"}, {"range": "747", "text": "597"}, {"range": "748", "text": "595"}, {"range": "749", "text": "597"}, "Update the dependencies array to be: [fetchDiagnostics, isOpen, jobId]", {"range": "750", "text": "751"}, {"range": "752", "text": "595"}, {"range": "753", "text": "597"}, {"range": "754", "text": "595"}, {"range": "755", "text": "597"}, {"range": "756", "text": "595"}, {"range": "757", "text": "597"}, {"range": "758", "text": "595"}, {"range": "759", "text": "597"}, [4311, 4314], "unknown", [4311, 4314], "never", [5185, 5188], [5185, 5188], "&quot;", [4364, 4403], "用&quot;扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", "&ldquo;", [4364, 4403], "用&ldquo;扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", "&#34;", [4364, 4403], "用&#34;扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", "&rdquo;", [4364, 4403], "用&rdquo;扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写&quot;、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写&ldquo;、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写&#34;、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写&rdquo;、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、&quot;删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、&ldquo;删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、&#34;删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、&rdquo;删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除&quot;、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除&ldquo;、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除&#34;、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除&rdquo;、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、&quot;修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、&ldquo;修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、&#34;修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、&rdquo;修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改&quot;等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改&ldquo;等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改&#34;等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改&rdquo;等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是&quot;优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是&ldquo;优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是&#34;优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是&rdquo;优化\"、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化&quot;、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化&ldquo;、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化&#34;、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化&rdquo;、\"改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、&quot;改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、&ldquo;改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、&#34;改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、&rdquo;改善\"等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善&quot;等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善&ldquo;等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善&#34;等模糊词汇。", [4364, 4403], "用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善&rdquo;等模糊词汇。", [5735, 5808], "\n                    例如：将&quot;他很强\"改写为具体的实力展现，如\"他一剑斩断了千年古树\"\n                  ", [5735, 5808], "\n                    例如：将&ldquo;他很强\"改写为具体的实力展现，如\"他一剑斩断了千年古树\"\n                  ", [5735, 5808], "\n                    例如：将&#34;他很强\"改写为具体的实力展现，如\"他一剑斩断了千年古树\"\n                  ", [5735, 5808], "\n                    例如：将&rdquo;他很强\"改写为具体的实力展现，如\"他一剑斩断了千年古树\"\n                  ", [5735, 5808], "\n                    例如：将\"他很强&quot;改写为具体的实力展现，如\"他一剑斩断了千年古树\"\n                  ", [5735, 5808], "\n                    例如：将\"他很强&ldquo;改写为具体的实力展现，如\"他一剑斩断了千年古树\"\n                  ", [5735, 5808], "\n                    例如：将\"他很强&#34;改写为具体的实力展现，如\"他一剑斩断了千年古树\"\n                  ", [5735, 5808], "\n                    例如：将\"他很强&rdquo;改写为具体的实力展现，如\"他一剑斩断了千年古树\"\n                  ", [5735, 5808], "\n                    例如：将\"他很强\"改写为具体的实力展现，如&quot;他一剑斩断了千年古树\"\n                  ", [5735, 5808], "\n                    例如：将\"他很强\"改写为具体的实力展现，如&ldquo;他一剑斩断了千年古树\"\n                  ", [5735, 5808], "\n                    例如：将\"他很强\"改写为具体的实力展现，如&#34;他一剑斩断了千年古树\"\n                  ", [5735, 5808], "\n                    例如：将\"他很强\"改写为具体的实力展现，如&rdquo;他一剑斩断了千年古树\"\n                  ", [5735, 5808], "\n                    例如：将\"他很强\"改写为具体的实力展现，如\"他一剑斩断了千年古树&quot;\n                  ", [5735, 5808], "\n                    例如：将\"他很强\"改写为具体的实力展现，如\"他一剑斩断了千年古树&ldquo;\n                  ", [5735, 5808], "\n                    例如：将\"他很强\"改写为具体的实力展现，如\"他一剑斩断了千年古树&#34;\n                  ", [5735, 5808], "\n                    例如：将\"他很强\"改写为具体的实力展现，如\"他一剑斩断了千年古树&rdquo;\n                  ", [935, 944], "[loadCharacters, novelId, onCharactersChange]", [1848, 1851], [1848, 1851], [1893, 1900], "[check<PERSON><PERSON><PERSON><PERSON><PERSON>, jobId]", [13655, 13658], [13655, 13658], [13586, 13589], [13586, 13589], [13653, 13656], [13653, 13656], [13894, 13897], [13894, 13897], [13990, 13993], [13990, 13993], [21773, 21776], [21773, 21776], [24050, 24053], [24050, 24053], [888, 891], [888, 891], [2208, 2211], [2208, 2211], [3991, 3994], [3991, 3994], [4813, 4816], [4813, 4816], [4833, 4836], [4833, 4836], [4851, 4854], [4851, 4854], [650, 653], [650, 653], [2267, 2282], "[fetchDiagno<PERSON>, isOpen, jobId]", [3171, 3174], [3171, 3174], [3238, 3241], [3238, 3241], [3442, 3445], [3442, 3445], [3875, 3878], [3875, 3878]]