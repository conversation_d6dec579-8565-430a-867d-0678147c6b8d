module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},22734,(e,t,r)=>{t.exports=e.x("fs",()=>require("fs"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},84168,e=>{"use strict";e.s(["chapterContextDb",()=>C,"chapterDb",()=>y,"characterDb",()=>S,"jobDb",()=>w,"novelContextDb",()=>b,"novelDb",()=>x,"presetDb",()=>D,"ruleDb",()=>v]);var t=e.i(22734),r=e.i(14747),n=e.i(54799);let a=r.default.join(process.cwd(),"data"),i=r.default.join(a,"novels.json"),s=r.default.join(a,"chapters.json"),l=r.default.join(a,"rewrite_rules.json"),o=r.default.join(a,"rewrite_jobs.json"),u=r.default.join(a,"characters.json"),d=r.default.join(a,"presets.json"),c=r.default.join(a,"novel-contexts.json"),p=r.default.join(a,"chapter-contexts.json");function h(){t.default.existsSync(a)||t.default.mkdirSync(a,{recursive:!0})}function f(e){if(h(),!t.default.existsSync(e))return[];try{let r=t.default.readFileSync(e,"utf-8");return JSON.parse(r)}catch(t){return console.error(`Error reading ${e}:`,t),[]}}function g(e,r){h();try{t.default.writeFileSync(e,JSON.stringify(r,null,2),"utf-8")}catch(t){throw console.error(`Error writing ${e}:`,t),t}}function m(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}let x={getAll:()=>f(i),getById:e=>f(i).find(t=>t.id===e),create:e=>{var t;let r=f(i),a=(t=e.title,n.default.createHash("md5").update(t).digest("hex").substring(0,18)),s=r.find(e=>e.id===a);if(s)return s.filename=e.filename,s.chapterCount=e.chapterCount,g(i,r),s;let l={...e,id:a,createdAt:new Date().toISOString()};return r.push(l),g(i,r),l},update:(e,t)=>{let r=f(i),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t},g(i,r),r[n])},delete:e=>{let t=f(i),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(i,t),!0)}},y={getAll:()=>f(s),getByNovelId:e=>f(s).filter(t=>t.novelId===e),getById:e=>f(s).find(t=>t.id===e),create:e=>{let t=f(s),r={...e,id:m(),createdAt:new Date().toISOString()};return t.push(r),g(s,t),r},createBatch:e=>{let t=f(s),r=e.map(e=>({...e,id:m(),createdAt:new Date().toISOString()}));return t.push(...r),g(s,t),r},delete:e=>{let t=f(s),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(s,t),!0)},deleteByNovelId:e=>{let t=f(s).filter(t=>t.novelId!==e);return g(s,t),!0}},v={getAll:()=>f(l),getById:e=>f(l).find(t=>t.id===e),create:e=>{let t=f(l),r={...e,id:m(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(l,t),r},update:(e,t)=>{let r=f(l),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(l,r),r[n])},delete:e=>{let t=f(l),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(l,t),!0)}},w={getAll:()=>f(o),getById:e=>f(o).find(t=>t.id===e),create:e=>{let t=f(o),r={...e,id:m(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(o,t),r},update:(e,t)=>{let r=f(o),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(o,r),r[n])},delete:e=>{let t=f(o),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(o,t),!0)}},S={getAll:()=>f(u),getByNovelId:e=>f(u).filter(t=>t.novelId===e),getById:e=>f(u).find(t=>t.id===e),create:e=>{let t=f(u),r={...e,id:m(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(u,t),r},update:(e,t)=>{let r=f(u),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(u,r),r[n])},delete:e=>{let t=f(u),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(u,t),!0)},deleteByNovelId:e=>{let t=f(u).filter(t=>t.novelId!==e);return g(u,t),!0}},D={getAll:()=>f(d),getById:e=>f(d).find(t=>t.id===e),create:e=>{let t=f(d),r={...e,id:m(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(d,t),r},update:(e,t)=>{let r=f(d),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(d,r),r[n])},delete:e=>{let t=f(d),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(d,t),!0)}},b={getAll:()=>f(c),getByNovelId:e=>f(c).find(t=>t.novelId===e),getById:e=>f(c).find(t=>t.id===e),create:e=>{let t=f(c),r={...e,id:m(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(c,t),r},update:(e,t)=>{let r=f(c),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(c,r),r[n]},delete:e=>{let t=f(c),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(c,t),!0)}},C={getAll:()=>f(p),getByNovelId:e=>f(p).filter(t=>t.novelId===e),getByChapter:(e,t)=>f(p).find(r=>r.novelId===e&&r.chapterNumber===t),getById:e=>f(p).find(t=>t.id===e),create:e=>{let t=f(p),r={...e,id:m(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(p,t),r},update:(e,t)=>{let r=f(p),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(p,r),r[n]},delete:e=>{let t=f(p),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(p,t),!0)},getContextWindow:(e,t,r=2)=>{let n=f(p).filter(t=>t.novelId===e),a=Math.max(1,t-r),i=t+r;return n.filter(e=>e.chapterNumber>=a&&e.chapterNumber<=i).sort((e,t)=>e.chapterNumber-t.chapterNumber)}}},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},23122,e=>{"use strict";e.s(["fileManager",()=>a]);var t=e.i(22734),r=e.i(14747);class n{static instance;baseDir;constructor(){this.baseDir=process.cwd()}static getInstance(){return n.instance||(n.instance=new n),n.instance}ensureDir(e){t.default.existsSync(e)||t.default.mkdirSync(e,{recursive:!0})}getNovelsDir(){return r.default.join(this.baseDir,"..","novels")}getChaptersDir(){return r.default.join(this.baseDir,"..","chapters")}getDataDir(){let e=r.default.join(this.baseDir,"data");return this.ensureDir(e),e}getRewrittenDir(){let e=r.default.join(this.getDataDir(),"rewritten");return this.ensureDir(e),e}getNovelRewrittenDir(e){let t=r.default.join(this.getRewrittenDir(),this.sanitizeFilename(e));return this.ensureDir(t),t}getDoneNovelsDir(){let e=r.default.join(this.baseDir,"..","done-novels");return this.ensureDir(e),e}getNovelChaptersDir(e){let t=this.getChaptersDir();this.ensureDir(t);let n=r.default.join(t,this.sanitizeFilename(e));return this.ensureDir(n),n}sanitizeFilename(e){return e.replace(/[<>:"/\\|?*]/g,"_").trim()}readFile(e){try{return t.default.readFileSync(e,"utf-8")}catch(t){throw console.error(`读取文件失败: ${e}`,t),t}}writeFile(e,n){try{let a=r.default.dirname(e);this.ensureDir(a),t.default.writeFileSync(e,n,"utf-8")}catch(t){throw console.error(`写入文件失败: ${e}`,t),t}}fileExists(e){return t.default.existsSync(e)}listFiles(e,n){try{if(!t.default.existsSync(e))return[];let a=t.default.readdirSync(e);if(n)return a.filter(e=>{let t=r.default.extname(e).toLowerCase();return n.includes(t)});return a}catch(t){return console.error(`读取目录失败: ${e}`,t),[]}}getFileStats(e){try{return t.default.statSync(e)}catch(t){return console.error(`获取文件信息失败: ${e}`,t),null}}deleteFile(e){try{if(t.default.existsSync(e))return t.default.unlinkSync(e),!0;return!1}catch(t){return console.error(`删除文件失败: ${e}`,t),!1}}deleteDir(e){try{if(t.default.existsSync(e))return t.default.rmSync(e,{recursive:!0,force:!0}),!0;return!1}catch(t){return console.error(`删除目录失败: ${e}`,t),!1}}copyFile(e,n){try{let a=r.default.dirname(n);return this.ensureDir(a),t.default.copyFileSync(e,n),!0}catch(t){return console.error(`复制文件失败: ${e} -> ${n}`,t),!1}}moveFile(e,n){try{let a=r.default.dirname(n);return this.ensureDir(a),t.default.renameSync(e,n),!0}catch(t){return console.error(`移动文件失败: ${e} -> ${n}`,t),!1}}getDirSize(e){let n=0;try{if(!t.default.existsSync(e))return 0;for(let a of t.default.readdirSync(e)){let i=r.default.join(e,a),s=t.default.statSync(i);s.isDirectory()?n+=this.getDirSize(i):n+=s.size}}catch(t){console.error(`计算目录大小失败: ${e}`,t)}return n}formatFileSize(e){if(0===e)return"0 B";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB"][t]}createBackup(e){try{let t=new Date().toISOString().replace(/[:.]/g,"-"),n=r.default.extname(e),a=r.default.basename(e,n),i=r.default.dirname(e),s=r.default.join(i,`${a}_backup_${t}${n}`);if(this.copyFile(e,s))return s;return null}catch(t){return console.error(`创建备份失败: ${e}`,t),null}}mergeRewrittenChapters(e){try{let t=this.getNovelRewrittenDir(e),n=this.getDoneNovelsDir(),a=this.listFiles(t,[".txt"]).filter(e=>e.startsWith("chapter_")&&e.includes("_rewritten")).sort((e,t)=>{let r=parseInt(e.match(/chapter_(\d+)/)?.[1]||"0"),n=parseInt(t.match(/chapter_(\d+)/)?.[1]||"0");return r-n});if(0===a.length)return{success:!1,error:"没有找到改写的章节文件"};let i="";for(let e of a){let n=r.default.join(t,e),a=this.readFile(n);i+=a+"\n\n"}let s=new Date().toISOString().replace(/[:.]/g,"-").substring(0,19),l=`${this.sanitizeFilename(e)}_merged_${s}.txt`,o=r.default.join(n,l);return this.writeFile(o,i.trim()),{success:!0,filePath:o}}catch(t){return console.error(`合并章节失败: ${e}`,t),{success:!1,error:t instanceof Error?t.message:"未知错误"}}}cleanupBackups(e,t=5){try{let n=this.listFiles(e).filter(e=>e.includes("_backup_")).map(t=>({name:t,path:r.default.join(e,t),stats:this.getFileStats(r.default.join(e,t))})).filter(e=>null!==e.stats).sort((e,t)=>t.stats.mtime.getTime()-e.stats.mtime.getTime());if(n.length>t)for(let e of n.slice(t))this.deleteFile(e.path)}catch(t){console.error(`清理备份文件失败: ${e}`,t)}}}let a=n.getInstance()},96550,e=>{"use strict";async function t(t,r,n,a,i,s){try{let{rewriteText:l}=await e.A(30017),{novelContextDb:o,chapterContextDb:u}=await e.A(58566),d=o.getByNovelId(t),c=u.getByChapter(t,r),p={originalText:n,rules:a,chapterTitle:i,chapterNumber:r,model:s,novelContext:d?{summary:d.summary,mainCharacters:d.mainCharacters,worldSetting:d.worldSetting,writingStyle:d.writingStyle,tone:d.tone}:void 0,chapterContext:c?{previousChapterSummary:c.previousChapterSummary,keyEvents:c.keyEvents,characterStates:c.characterStates,plotProgress:c.plotProgress,contextualNotes:c.contextualNotes}:void 0};return await l(p)}catch(l){console.error("带上下文重写失败:",l);let{rewriteText:t}=await e.A(30017);return await t({originalText:n,rules:a,chapterTitle:i,chapterNumber:r,model:s})}}async function r(t,n,a,i,s,l=3,o="gemini-2.5-flash-lite",u=!0){try{let{novelContextDb:r,chapterContextDb:u}=await e.A(58566),{rewriteText:d}=await e.A(30017),c=r.getByNovelId(t),p=Array(n.length),h=0,f=0,g=Date.now(),m={count:l,waiting:[]},x=async(e,r)=>{await new Promise(e=>{m.count>0?(m.count--,e()):m.waiting.push(e)});let l=Date.now();try{let m=u.getByChapter(t,e.number),x={originalText:e.content,rules:a,chapterTitle:e.title,chapterNumber:e.number,model:o,novelContext:c?{summary:c.summary,mainCharacters:c.mainCharacters,worldSetting:c.worldSetting,writingStyle:c.writingStyle,tone:c.tone}:void 0,chapterContext:m?{previousChapterSummary:m.previousChapterSummary,keyEvents:m.keyEvents,characterStates:m.characterStates,plotProgress:m.plotProgress,contextualNotes:m.contextualNotes}:void 0},y=await d(x),v=Date.now()-l;y.tokensUsed&&(f+=y.tokensUsed);let w={success:y.success,content:y.rewrittenText,error:y.error,details:{apiKeyUsed:y.apiKeyUsed,tokensUsed:y.tokensUsed,model:y.model,processingTime:v,chapterNumber:e.number,chapterTitle:e.title,hasContext:!!(c||m)}};p[r]=w,h++,s&&s(r,w);let S=h/n.length*100,D=Date.now()-g,b=D/h;i&&i(S,e.number,{completed:h,totalTokensUsed:f,totalTime:D,averageTimePerChapter:b,hasContext:!!(c||m)}),console.log(`第 ${e.number} 章重写${y.success?"成功":"失败"}${c||m?"（使用上下文）":""}: ${y.error||"完成"}`)}catch(a){let t=Date.now()-l,n={success:!1,content:"",error:`重写异常: ${a instanceof Error?a.message:"未知错误"}`,details:{processingTime:t,chapterNumber:e.number,chapterTitle:e.title,hasContext:!!(c||chapterContext)}};p[r]=n,h++,s&&s(r,n),console.error(`第 ${e.number} 章重写异常:`,a)}finally{if(m.count++,m.waiting.length>0){let e=m.waiting.shift();e&&(m.count--,e())}}},y=n.map((e,t)=>x(e,t));return await Promise.all(y),p}catch(r){console.error("批量重写失败，回退到普通重写:",r);let{rewriteChapters:t}=await e.A(30017);return await t(n,a,i,s,l,o,u)}}e.s(["rewriteChaptersWithContext",()=>r,"rewriteTextWithContextServer",()=>t])},73665,e=>{"use strict";e.s(["getAvailableNovels",()=>c,"isNovelParsed",()=>p,"parseChapterRange",()=>f,"parseNovelFile",()=>l,"reparseNovel",()=>h]);var t=e.i(22734),r=e.i(14747),n=e.i(84168),a=e.i(23122);let i=[/^\s*(?:第[一二三四五六七八九十百千万\d]+[卷集部])\s*.*$/gmi,/^\s*(?:[卷集部][一二三四五六七八九十百千万\d]+)\s*.*$/gmi],s=[/^\s*(?:第[一二三四五六七八九十百千万\d]+[章节回])\s*.*$/gmi,/^\s*(?:Chapter\s+\d+)\s*.*$/gmi];async function l(e){let a=r.default.basename(e),s=a.replace(/\.(txt|md)$/i,""),l=t.default.readFileSync(e,"utf-8"),c=n.novelDb.create({title:s,filename:a}),p=function(e,t){let r=[],n=function(e){let t=[];for(let r of i)for(let n of Array.from(e.matchAll(r)))t.push({index:n.index,title:u(n[0])});return t.sort((e,t)=>e.index-t.index)}(e);if(n.length>0){console.log(`Found ${n.length} volumes`);let a=1;for(let i=0;i<n.length;i++){let s=n[i].index,l=i+1<n.length?n[i+1].index:e.length,u=o(e.slice(s,l),t,a,n[i].title);r.push(...u),a+=u.length}}else{let n=o(e,t,1);r.push(...n)}return console.log(`Successfully parsed ${r.length} chapters`),r}(l,c.id),h=n.chapterDb.createBatch(p);return n.novelDb.update(c.id,{chapterCount:h.length}),await d(h),{novel:{...c,chapterCount:h.length},chapters:h}}function o(e,t,r,n){let a=[],i=r,l=null,o=-1;for(let t of s){let r=e.match(t),n=r?r.length:0;n>o&&(o=n,l=t)}if(!l||0===o){let r=e.trim();return r.length>100&&a.push({novelId:t,chapterNumber:i,title:n||"全文",content:r,filename:`chapter_${i}.txt`}),a}let d=RegExp(`(${l.source})`,"gmi"),c=e.split(d),p=c[0]?.trim();p&&p.length>100&&(a.push({novelId:t,chapterNumber:i,title:"序章",content:p,filename:`chapter_${i}.txt`}),i++),console.log(c.map((e,t)=>t+e));for(let e=1;e<c.length;e+=2){let r=c[e],n=c[e+1]||"";if(!r)continue;let s=(r+n).trim();s.length>100&&(a.push({novelId:t,chapterNumber:i,title:u(r),content:s,filename:`chapter_${i}.txt`}),i++)}return a}function u(e){let t=e.trim().split("\n"),r=t[0].trim();if(r.length<100&&r.length>0)return r;for(let e=0;e<Math.min(3,t.length);e++){let r=t[e].trim();if(r.length>0&&r.length<100)return r}return"未命名章节"}async function d(e){for(let t of[...new Set(e.map(e=>e.novelId))]){let i=n.novelDb.getById(t);if(!i)continue;let s=a.fileManager.getNovelChaptersDir(i.title);for(let n of e.filter(e=>e.novelId===t)){let e=r.default.join(s,n.filename);a.fileManager.writeFile(e,n.content)}}}function c(){let e=a.fileManager.getNovelsDir();return a.fileManager.listFiles(e,[".txt",".md"])}function p(e){return n.novelDb.getAll().some(t=>t.filename===e)}async function h(e){let t=a.fileManager.getNovelsDir(),i=r.default.join(t,e);if(!a.fileManager.fileExists(i))return null;let s=n.novelDb.getAll().find(t=>t.filename===e);return s&&(n.chapterDb.deleteByNovelId(s.id),n.novelDb.delete(s.id)),await l(i)}function f(e,t){let r=[];for(let n of e.split(",").map(e=>e.trim()))if(n.includes("-")){let[e,a]=n.split("-").map(e=>parseInt(e.trim()));if(!isNaN(e)&&!isNaN(a)&&e<=a)for(let n=e;n<=Math.min(a,t);n++)n>0&&!r.includes(n)&&r.push(n)}else{let e=parseInt(n);!isNaN(e)&&e>0&&e<=t&&!r.includes(e)&&r.push(e)}return r.sort((e,t)=>e-t)}},90172,(e,t,r)=>{},66365,e=>{"use strict";e.s(["handler",()=>$,"patchFetch",()=>T,"routeModule",()=>N,"serverHooks",()=>A,"workAsyncStorage",()=>R,"workUnitAsyncStorage",()=>j],66365);var t=e.i(47909),r=e.i(74017),n=e.i(96250),a=e.i(59756),i=e.i(61916),s=e.i(69741),l=e.i(16795),o=e.i(87718),u=e.i(95169),d=e.i(47587),c=e.i(66012),p=e.i(70101),h=e.i(26937),f=e.i(10372),g=e.i(93695);e.i(52474);var m=e.i(220);e.s(["POST",()=>b],85603);var x=e.i(89171),y=e.i(84168),v=e.i(96550),w=e.i(73665),S=e.i(23122),D=e.i(14747);async function b(e){try{let{novelId:t,chapterRange:r,rules:n,model:a,concurrency:i}=await e.json();if(!t||!r||!n)return x.NextResponse.json({success:!1,error:"参数不完整"},{status:400});let s=a&&["gemini-2.5-flash-lite","gemini-2.5-flash","gemini-1.5-pro"].includes(a)?a:"gemini-2.5-flash-lite",l=i&&i>=1&&i<=10?i:3,o=y.novelDb.getById(t);if(!o)return x.NextResponse.json({success:!1,error:"小说不存在"},{status:404});let u=y.chapterDb.getByNovelId(t);if(0===u.length)return x.NextResponse.json({success:!1,error:"该小说没有章节"},{status:404});let d=(0,w.parseChapterRange)(r,u.length);if(0===d.length)return x.NextResponse.json({success:!1,error:"无效的章节范围"},{status:400});let c=u.filter(e=>d.includes(e.chapterNumber));if(0===c.length)return x.NextResponse.json({success:!1,error:"没有找到指定的章节"},{status:404});let p=y.jobDb.create({novelId:t,chapters:d,ruleId:"custom",status:"pending",progress:0,details:{totalChapters:c.length,completedChapters:0,failedChapters:0,totalTokensUsed:0,totalProcessingTime:0,averageTimePerChapter:0,apiKeyStats:[],chapterResults:[],model:s,concurrency:l}});return C(p.id,c,n,o.title,s,l),x.NextResponse.json({success:!0,data:{jobId:p.id,chaptersCount:c.length,message:"改写任务已创建，正在处理中..."}})}catch(e){return console.error("创建改写任务失败:",e),x.NextResponse.json({success:!1,error:"创建改写任务失败"},{status:500})}}async function C(e,t,r,n,a="gemini-2.5-flash-lite",i=3){let s=Date.now();try{y.jobDb.update(e,{status:"processing",progress:0,details:{totalChapters:t.length,completedChapters:0,failedChapters:0,totalTokensUsed:0,totalProcessingTime:0,averageTimePerChapter:0,apiKeyStats:y.jobDb.getById(e)?.details?.apiKeyStats||[],chapterResults:[],model:a,concurrency:i}});let l=t.map(e=>({content:e.content,title:e.title,number:e.chapterNumber})),o=S.fileManager.getNovelRewrittenDir(n),u=await (0,v.rewriteChaptersWithContext)(t[0].novelId,l,r,(r,n,s)=>{let l=y.jobDb.getById(e);l&&s&&y.jobDb.update(e,{progress:Math.round(r),details:{totalChapters:l.details?.totalChapters||t.length,completedChapters:s.completed,failedChapters:l.details?.failedChapters||0,totalTokensUsed:s.totalTokensUsed,totalProcessingTime:s.totalTime,averageTimePerChapter:s.averageTimePerChapter,apiKeyStats:s.apiKeyStats,chapterResults:l.details?.chapterResults||[],model:a,concurrency:i}})},(r,n)=>{if(n.success){let e=t[r],a=`chapter_${e.chapterNumber}_rewritten.txt`,i=D.default.join(o,a);S.fileManager.writeFile(i,n.content)}let a=y.jobDb.getById(e);if(a?.details&&n){let t=[...a.details.chapterResults||[]];t[r]={chapterNumber:n.details?.chapterNumber||r+1,chapterTitle:n.details?.chapterTitle||`第${r+1}章`,success:n.success,error:n.error,apiKeyUsed:n.details?.apiKeyUsed,tokensUsed:n.details?.tokensUsed,processingTime:n.details?.processingTime,completedAt:new Date().toISOString()},y.jobDb.update(e,{details:{...a.details,chapterResults:t,failedChapters:t.filter(e=>e&&!e.success).length}})}},i,a,!0),d=0,c=0,p=[];for(let e=0;e<u.length;e++){let r=u[e],n=t[e];r&&r.success&&d++,r?.details?.tokensUsed&&(c+=r.details.tokensUsed),p.push({chapterNumber:n.chapterNumber,chapterTitle:n.title,success:!!r&&r.success,error:r?.error||"处理失败",apiKeyUsed:r?.details?.apiKeyUsed,tokensUsed:r?.details?.tokensUsed,processingTime:r?.details?.processingTime})}let h=D.default.join(o,"rewrite_summary.json"),f=Date.now()-s,g=JSON.stringify({jobId:e,novelTitle:n,rules:r,totalChapters:t.length,successCount:d,failedCount:t.length-d,totalTokensUsed:c,totalProcessingTime:f,averageTimePerChapter:f/t.length,results:p,completedAt:new Date().toISOString(),model:a,concurrency:i},null,2);S.fileManager.writeFile(h,g);let m=y.jobDb.getById(e),x=d>0?"completed":"failed";y.jobDb.update(e,{status:x,progress:100,result:`成功改写 ${d}/${t.length} 章节，结果保存在: ${o}`,details:{totalChapters:t.length,completedChapters:d,failedChapters:t.length-d,totalTokensUsed:c,totalProcessingTime:f,averageTimePerChapter:t.length>0?f/t.length:0,apiKeyStats:m?.details?.apiKeyStats||[],chapterResults:p,model:a,concurrency:i}})}catch(t){console.error("执行改写任务失败:",t),y.jobDb.update(e,{status:"failed",result:`改写失败: ${t instanceof Error?t.message:"未知错误"}`})}}var I=e.i(85603);let N=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/rewrite/route",pathname:"/api/rewrite",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/rewrite/route.ts",nextConfigOutput:"",userland:I}),{workAsyncStorage:R,workUnitAsyncStorage:j,serverHooks:A}=N;function T(){return(0,n.patchFetch)({workAsyncStorage:R,workUnitAsyncStorage:j})}async function $(e,t,n){var x;let y="/api/rewrite/route";y=y.replace(/\/index$/,"")||"/";let v=await N.prepare(e,t,{srcPage:y,multiZoneDraftMode:!1});if(!v)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:w,params:S,nextConfig:D,isDraftMode:b,prerenderManifest:C,routerServerContext:I,isOnDemandRevalidate:R,revalidateOnlyGenerated:j,resolvedPathname:A}=v,T=(0,s.normalizeAppPath)(y),$=!!(C.dynamicRoutes[T]||C.routes[A]);if($&&!b){let e=!!C.routes[A],t=C.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new g.NoFallbackError}let E=null;!$||N.isDev||b||(E="/index"===(E=A)?"/":E);let O=!0===N.isDev||!$,k=$&&!O,_=e.method||"GET",P=(0,i.getTracer)(),B=P.getActiveScopeSpan(),F={params:S,prerenderManifest:C,renderOpts:{experimental:{cacheComponents:!!D.experimental.cacheComponents,authInterrupts:!!D.experimental.authInterrupts},supportsDynamicResponse:O,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(x=D.experimental)?void 0:x.cacheLife,isRevalidate:k,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>N.onRequestError(e,t,n,I)},sharedContext:{buildId:w}},U=new l.NodeNextRequest(e),M=new l.NodeNextResponse(t),q=o.NextRequestAdapter.fromNodeNextRequest(U,(0,o.signalFromNodeResponse)(t));try{let s=async r=>N.handle(q,F).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=P.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==u.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${_} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${_} ${e.url}`)}),l=async i=>{var l,o;let u=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&R&&j&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let l=await s(i);e.fetchMetrics=F.renderOpts.fetchMetrics;let o=F.renderOpts.pendingWaitUntil;o&&n.waitUntil&&(n.waitUntil(o),o=void 0);let u=F.renderOpts.collectedTags;if(!$)return await (0,c.sendResponse)(U,M,l,F.renderOpts.pendingWaitUntil),null;{let e=await l.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(l.headers);u&&(t[f.NEXT_CACHE_TAGS_HEADER]=u),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==F.renderOpts.collectedRevalidate&&!(F.renderOpts.collectedRevalidate>=f.INFINITE_CACHE)&&F.renderOpts.collectedRevalidate,n=void 0===F.renderOpts.collectedExpire||F.renderOpts.collectedExpire>=f.INFINITE_CACHE?void 0:F.renderOpts.collectedExpire;return{value:{kind:m.CachedRouteKind.APP_ROUTE,status:l.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await N.onRequestError(e,t,{routerKind:"App Router",routePath:y,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:k,isOnDemandRevalidate:R})},I),t}},g=await N.handleResponse({req:e,nextConfig:D,cacheKey:E,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:C,isRoutePPREnabled:!1,isOnDemandRevalidate:R,revalidateOnlyGenerated:j,responseGenerator:u,waitUntil:n.waitUntil});if(!$)return null;if((null==g||null==(l=g.value)?void 0:l.kind)!==m.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==g||null==(o=g.value)?void 0:o.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",R?"REVALIDATED":g.isMiss?"MISS":g.isStale?"STALE":"HIT"),b&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let x=(0,p.fromNodeOutgoingHttpHeaders)(g.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&$||x.delete(f.NEXT_CACHE_TAGS_HEADER),!g.cacheControl||t.getHeader("Cache-Control")||x.get("Cache-Control")||x.set("Cache-Control",(0,h.getCacheControlHeader)(g.cacheControl)),await (0,c.sendResponse)(U,M,new Response(g.value.body,{headers:x,status:g.value.status||200})),null};B?await l(B):await P.withPropagatedContext(e.headers,()=>P.trace(u.BaseServerSpan.handleRequest,{spanName:`${_} ${e.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":_,"http.target":e.url}},l))}catch(t){if(B||t instanceof g.NoFallbackError||await N.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:k,isOnDemandRevalidate:R})}),$)throw t;return await (0,c.sendResponse)(U,M,new Response(null,{status:500})),null}}},30017,e=>{e.v(t=>Promise.all(["server/chunks/src_lib_gemini_ts_7aaf7081._.js"].map(t=>e.l(t))).then(()=>t(71994)))},58566,e=>{e.v(e=>Promise.resolve().then(()=>e(84168)))}];

//# sourceMappingURL=%5Broot-of-the-server%5D__7bd1171f._.js.map