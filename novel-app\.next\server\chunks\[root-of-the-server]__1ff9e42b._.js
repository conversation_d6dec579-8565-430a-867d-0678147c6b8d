module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},22734,(e,t,r)=>{t.exports=e.x("fs",()=>require("fs"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},84168,e=>{"use strict";e.s(["chapterContextDb",()=>C,"chapterDb",()=>f,"characterDb",()=>S,"jobDb",()=>v,"novelContextDb",()=>I,"novelDb",()=>y,"presetDb",()=>A,"ruleDb",()=>x]);var t=e.i(22734),r=e.i(14747),n=e.i(54799);let a=r.default.join(process.cwd(),"data"),s=r.default.join(a,"novels.json"),i=r.default.join(a,"chapters.json"),o=r.default.join(a,"rewrite_rules.json"),l=r.default.join(a,"rewrite_jobs.json"),d=r.default.join(a,"characters.json"),u=r.default.join(a,"presets.json"),c=r.default.join(a,"novel-contexts.json"),p=r.default.join(a,"chapter-contexts.json");function m(){t.default.existsSync(a)||t.default.mkdirSync(a,{recursive:!0})}function g(e){if(m(),!t.default.existsSync(e))return[];try{let r=t.default.readFileSync(e,"utf-8");return JSON.parse(r)}catch(t){return console.error(`Error reading ${e}:`,t),[]}}function h(e,r){m();try{t.default.writeFileSync(e,JSON.stringify(r,null,2),"utf-8")}catch(t){throw console.error(`Error writing ${e}:`,t),t}}function w(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}let y={getAll:()=>g(s),getById:e=>g(s).find(t=>t.id===e),create:e=>{var t;let r=g(s),a=(t=e.title,n.default.createHash("md5").update(t).digest("hex").substring(0,18)),i=r.find(e=>e.id===a);if(i)return i.filename=e.filename,i.chapterCount=e.chapterCount,h(s,r),i;let o={...e,id:a,createdAt:new Date().toISOString()};return r.push(o),h(s,r),o},update:(e,t)=>{let r=g(s),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t},h(s,r),r[n])},delete:e=>{let t=g(s),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(s,t),!0)}},f={getAll:()=>g(i),getByNovelId:e=>g(i).filter(t=>t.novelId===e),getById:e=>g(i).find(t=>t.id===e),create:e=>{let t=g(i),r={...e,id:w(),createdAt:new Date().toISOString()};return t.push(r),h(i,t),r},createBatch:e=>{let t=g(i),r=e.map(e=>({...e,id:w(),createdAt:new Date().toISOString()}));return t.push(...r),h(i,t),r},delete:e=>{let t=g(i),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(i,t),!0)},deleteByNovelId:e=>{let t=g(i).filter(t=>t.novelId!==e);return h(i,t),!0}},x={getAll:()=>g(o),getById:e=>g(o).find(t=>t.id===e),create:e=>{let t=g(o),r={...e,id:w(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(o,t),r},update:(e,t)=>{let r=g(o),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(o,r),r[n])},delete:e=>{let t=g(o),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(o,t),!0)}},v={getAll:()=>g(l),getById:e=>g(l).find(t=>t.id===e),create:e=>{let t=g(l),r={...e,id:w(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(l,t),r},update:(e,t)=>{let r=g(l),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(l,r),r[n])},delete:e=>{let t=g(l),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(l,t),!0)}},S={getAll:()=>g(d),getByNovelId:e=>g(d).filter(t=>t.novelId===e),getById:e=>g(d).find(t=>t.id===e),create:e=>{let t=g(d),r={...e,id:w(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(d,t),r},update:(e,t)=>{let r=g(d),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(d,r),r[n])},delete:e=>{let t=g(d),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(d,t),!0)},deleteByNovelId:e=>{let t=g(d).filter(t=>t.novelId!==e);return h(d,t),!0}},A={getAll:()=>g(u),getById:e=>g(u).find(t=>t.id===e),create:e=>{let t=g(u),r={...e,id:w(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(u,t),r},update:(e,t)=>{let r=g(u),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(u,r),r[n])},delete:e=>{let t=g(u),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(u,t),!0)}},I={getAll:()=>g(c),getByNovelId:e=>g(c).find(t=>t.novelId===e),getById:e=>g(c).find(t=>t.id===e),create:e=>{let t=g(c),r={...e,id:w(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(c,t),r},update:(e,t)=>{let r=g(c),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(c,r),r[n]},delete:e=>{let t=g(c),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(c,t),!0)}},C={getAll:()=>g(p),getByNovelId:e=>g(p).filter(t=>t.novelId===e),getByChapter:(e,t)=>g(p).find(r=>r.novelId===e&&r.chapterNumber===t),getById:e=>g(p).find(t=>t.id===e),create:e=>{let t=g(p),r={...e,id:w(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(p,t),r},update:(e,t)=>{let r=g(p),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(p,r),r[n]},delete:e=>{let t=g(p),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(p,t),!0)},getContextWindow:(e,t,r=2)=>{let n=g(p).filter(t=>t.novelId===e),a=Math.max(1,t-r),s=t+r;return n.filter(e=>e.chapterNumber>=a&&e.chapterNumber<=s).sort((e,t)=>e.chapterNumber-t.chapterNumber)}}},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},71994,e=>{"use strict";e.s(["PRESET_RULES",()=>c,"addCustomPreset",()=>m,"getApiKeyStats",()=>d,"loadCustomPresets",()=>p,"resetApiKeyStats",()=>u,"rewriteChapters",()=>i,"rewriteText",()=>s,"rewriteTextWithContext",()=>g,"testGeminiConnection",()=>l]);let t=[{key:"AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw",name:"My First Project",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y",name:"ankibot",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY",name:"Generative Language Client",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc",name:"In The Novel",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk",name:"chat",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0}],r=(e="gemini-2.5-flash-lite")=>`https://generativelanguage.googleapis.com/v1beta/models/${e}:generateContent`;class n{keys=[...t];getBestAvailableKey(){let e=Date.now(),t=this.keys.filter(t=>t.cooldownUntil<=e);return 0===t.length?this.keys.reduce((e,t)=>t.cooldownUntil<e.cooldownUntil?t:e):t.reduce((e,t)=>t.weight/(t.requestCount+1)>e.weight/(e.requestCount+1)?t:e)}recordUsage(e,t){let r=this.keys.find(t=>t.name===e);r&&(r.requestCount++,r.lastUsed=Date.now(),t||(r.cooldownUntil=Date.now()+6e4))}getStats(){return this.keys.map(e=>({name:e.name,requestCount:e.requestCount,weight:e.weight,isAvailable:e.cooldownUntil<=Date.now(),cooldownRemaining:Math.max(0,e.cooldownUntil-Date.now())}))}}let a=new n;async function s(e){let t=Date.now(),n="",s="";for(let i=0;i<5;i++)try{let o,l=a.getBestAvailableKey();if(l.cooldownUntil>Date.now()){let e=Math.min(l.cooldownUntil-Date.now(),3e4),t=Math.min(2e3*Math.pow(2,i),3e4),r=Math.max(e,t);console.log(`等待 ${r}ms (尝试 ${i+1}/5, API Key: ${l.name})`),await new Promise(e=>setTimeout(e,r))}let d=function(e){let{originalText:t,rules:r,chapterTitle:n,chapterNumber:a,novelContext:s,chapterContext:i}=e,o=`你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：

改写规则：
${r}

${n?`当前章节：${n}`:""}`;return s&&(o+=`

【小说背景信息】
小说摘要：${s.summary}

主要人物：
${s.mainCharacters.map(e=>`- ${e.name}(${e.role}): ${e.description}${e.relationships?` | 关系：${e.relationships}`:""}`).join("\n")}

世界观设定：${s.worldSetting}

写作风格：${s.writingStyle}

整体语调：${s.tone}`),i&&(o+=`

【章节上下文信息】`,i.previousChapterSummary&&(o+=`
前一章摘要：${i.previousChapterSummary}`),i.keyEvents.length>0&&(o+=`
本章关键事件：${i.keyEvents.join("、")}`),i.characterStates.length>0&&(o+=`
人物状态：
${i.characterStates.map(e=>`- ${e.name}: ${e.status} | 情感：${e.emotions} | 关系：${e.relationships}`).join("\n")}`),o+=`
情节推进：${i.plotProgress}`,i.contextualNotes&&(o+=`
重要注释：${i.contextualNotes}`)),o+=`

原文内容：
${t}

请严格按照改写规则进行改写，同时保持与小说整体背景和上下文的连贯性。改写后的内容应该：
1. 遵循所有指定的改写规则
2. 保持与小说整体背景的一致性
3. 确保人物性格和关系的连贯性
4. 保持情节发展的逻辑性
5. 维持原有的写作风格和语调
6. 确保文字流畅自然

请直接输出改写后的内容，不要添加任何解释或说明：`}(e),u=new AbortController,c=setTimeout(()=>u.abort(),6e4),p=r(e.model),m=await fetch(`${p}?key=${l.key}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:d}]}],generationConfig:{temperature:.6,topK:10,topP:.8,thinkingConfig:{thinkingBudget:0}}}),signal:u.signal});clearTimeout(c);let g=Date.now()-t;if(429===m.status&&(a.recordUsage(l.name,!1),n=`API限流 (${l.name})`,s=`第${i+1}次尝试: API Key "${l.name}" 遇到限流，状态码: 429`,i<4)){let e=2e3*Math.pow(2,i);console.log(`API限流，${e}ms后重试...`),await new Promise(t=>setTimeout(t,e));continue}if(!m.ok){let e=await m.text();if(console.error("Gemini API error:",e),a.recordUsage(l.name,!1),n=`API请求失败: ${m.status} ${m.statusText}`,s=`第${i+1}次尝试: HTTP ${m.status} ${m.statusText}, 响应: ${e.substring(0,200)}`,i<4){let e=1e3*(i+1);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:g,detailedError:s}}try{o=await m.json()}catch(e){if(a.recordUsage(l.name,!1),n="JSON解析失败",s=`第${i+1}次尝试: 无法解析API响应为JSON, 错误: ${e instanceof Error?e.message:"未知错误"}`,i<4){let e=1e3*(i+1);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:g,detailedError:s,retryCount:i+1}}if(a.recordUsage(l.name,!0),!o.candidates||0===o.candidates.length){if(n="没有收到有效的响应内容",s=`第${i+1}次尝试: API响应中没有candidates字段或为空数组, 完整响应: ${JSON.stringify(o).substring(0,500)}`,i<4){a.recordUsage(l.name,!1);let e=2e3*Math.pow(2,i);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:g,detailedError:s,retryCount:i+1}}let h=o.candidates[0];if("SAFETY"===h.finishReason)return{rewrittenText:"",success:!1,error:"内容被安全过滤器拦截，请调整改写规则或原文内容",apiKeyUsed:l.name,processingTime:g,detailedError:`内容被安全过滤器拦截，finishReason: SAFETY`,retryCount:i+1};if(!h.content||!h.content.parts||0===h.content.parts.length){if(n="响应内容格式错误",s=`第${i+1}次尝试: candidate内容格式错误, candidate: ${JSON.stringify(h).substring(0,300)}`,i<4){a.recordUsage(l.name,!1);let e=1e3*(i+1);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:g,detailedError:s,retryCount:i+1}}let w=h.content.parts[0].text;if(!w||w.trim().length<10){if(n="生成的内容过短或为空",s=`第${i+1}次尝试: 生成的内容长度: ${w?.length||0}, 内容: "${w?.substring(0,100)||"null"}"`,i<4){a.recordUsage(l.name,!1);let e=1e3*(i+1);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:g,detailedError:s,retryCount:i+1}}let y=o.usageMetadata?.totalTokenCount||0;return{rewrittenText:w.trim(),success:!0,apiKeyUsed:l.name,tokensUsed:y,model:e.model||"gemini-2.5-flash-lite",processingTime:g,retryCount:i+1}}catch(e){if(console.error("Gemini API调用错误:",e),e instanceof Error&&"AbortError"===e.name?(n="请求超时",s=`第${i+1}次尝试: 请求超时 (60秒)`):(n=`网络错误: ${e instanceof Error?e.message:"未知错误"}`,s=`第${i+1}次尝试: ${e instanceof Error?e.stack||e.message:"未知网络错误"}`),i<4){let e=2e3*Math.pow(2,i);console.log(`网络错误，${e}ms后重试...`),await new Promise(t=>setTimeout(t,e))}}return{rewrittenText:"",success:!1,error:`重试5次后仍然失败: ${n}`,processingTime:Date.now()-t,detailedError:s,retryCount:5}}async function i(e,t,r,n,l=3,d="gemini-2.5-flash-lite",u=!0){let c=Array(e.length),p=0,m=0,g=Date.now(),h=new o(l),w=async(i,o)=>{await h.acquire();let l=Date.now();try{let u=await s({originalText:i.content,rules:t,chapterTitle:i.title,chapterNumber:i.number,model:d}),h=Date.now()-l;u.tokensUsed&&(m+=u.tokensUsed);let w={success:u.success,content:u.rewrittenText,error:u.error,details:{apiKeyUsed:u.apiKeyUsed,tokensUsed:u.tokensUsed,model:u.model,processingTime:h,chapterNumber:i.number,chapterTitle:i.title}};if(c[o]=w,p++,n&&n(o,w),r){let t={completed:p,total:e.length,totalTokensUsed:m,totalTime:Date.now()-g,averageTimePerChapter:(Date.now()-g)/p,apiKeyStats:a.getStats(),currentChapter:{number:i.number,title:i.title,processingTime:h,apiKey:u.apiKeyUsed,tokens:u.tokensUsed}};r(p/e.length*100,i.number,t)}return await new Promise(e=>setTimeout(e,1e3)),u}catch(d){let t=Date.now(),s={success:!1,content:"",error:`处理失败: ${d instanceof Error?d.message:"未知错误"}`,details:{chapterNumber:i.number,chapterTitle:i.title,processingTime:t-l}};if(c[o]=s,p++,n&&n(o,s),r){let t={completed:p,total:e.length,totalTokensUsed:m,totalTime:Date.now()-g,averageTimePerChapter:(Date.now()-g)/p,apiKeyStats:a.getStats(),currentChapter:{number:i.number,title:i.title,error:d instanceof Error?d.message:"未知错误"}};r(p/e.length*100,i.number,t)}return null}finally{h.release()}},y=e.map((e,t)=>w(e,t));if(await Promise.all(y),u){let i=c.map((t,r)=>({result:t,index:r,chapter:e[r]})).filter(e=>!e.result.success);if(i.length>0){console.log(`开始恢复 ${i.length} 个失败的章节...`);let l=new o(1);for(let{index:o,chapter:u}of i){await l.acquire();try{console.log(`正在恢复第 ${u.number} 章: ${u.title}`),await new Promise(e=>setTimeout(e,5e3));let i=await s({originalText:u.content,rules:t,chapterTitle:u.title,chapterNumber:u.number,model:d});if(i.success){console.log(`成功恢复第 ${u.number} 章`);let t={success:!0,content:i.rewrittenText,error:void 0,details:{...i,chapterNumber:u.number,chapterTitle:u.title,isRecovered:!0}};if(c[o]=t,p++,n&&n(o,t),r){let t={completed:p,total:e.length,totalTokensUsed:m+(i.tokensUsed||0),totalTime:Date.now()-g,averageTimePerChapter:(Date.now()-g)/p,apiKeyStats:a.getStats(),currentChapter:{number:u.number,title:u.title,processingTime:i.processingTime,apiKey:i.apiKeyUsed,tokens:i.tokensUsed,isRecovered:!0}};r(p/e.length*100,u.number,t)}}else console.log(`第 ${u.number} 章恢复失败: ${i.error}`),c[o]={...c[o],error:`原始失败: ${c[o].error}; 恢复失败: ${i.error}`,details:{...c[o].details,recoveryAttempted:!0,recoveryError:i.error,recoveryDetailedError:i.detailedError}}}catch(e){console.error(`恢复第 ${u.number} 章时发生异常:`,e),c[o]={...c[o],error:`${c[o].error}; 恢复异常: ${e instanceof Error?e.message:"未知错误"}`,details:{...c[o].details,recoveryAttempted:!0,recoveryException:e instanceof Error?e.message:"未知错误"}}}finally{l.release()}}}}return c}class o{permits;waitQueue=[];constructor(e){this.permits=e}async acquire(){return this.permits>0?(this.permits--,Promise.resolve()):new Promise(e=>{this.waitQueue.push(e)})}release(){if(this.permits++,this.waitQueue.length>0){let e=this.waitQueue.shift();e&&(this.permits--,e())}}}async function l(){try{let e=await s({originalText:"这是一个测试文本。",rules:"保持原文不变"});return{success:e.success,error:e.error,details:{apiKeyUsed:e.apiKeyUsed,tokensUsed:e.tokensUsed,model:e.model,processingTime:e.processingTime,apiKeyStats:a.getStats()}}}catch(e){return{success:!1,error:`连接测试失败: ${e instanceof Error?e.message:"未知错误"}`,details:{apiKeyStats:a.getStats()}}}}function d(){return a.getStats()}function u(){t.forEach(e=>{e.requestCount=0,e.lastUsed=0,e.cooldownUntil=0})}let c={romance_focus:{name:"感情戏增强",description:"扩写男女主互动内容，对非感情戏部分一笔带过",rules:`请按照以下规则改写：
1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节
2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过
3. 增加角色间的情感张力和暧昧氛围
4. 保持故事主线不变，但重点突出感情发展`},character_fix:{name:"人设修正",description:"修正主角人设和对话风格",rules:`请按照以下规则改写：
1. 修正主角的性格设定，使其更加立体和讨喜
2. 改善对话风格，使其更加自然流畅
3. 去除过于中二或不合理的行为描写
4. 保持角色的核心特征，但优化表现方式`},toxic_content_removal:{name:"毒点清除",description:"移除送女、绿帽等毒点情节",rules:`请按照以下规则改写：
1. 完全移除或修改送女、绿帽、圣母等毒点情节
2. 删除或改写让读者不适的桥段
3. 保持故事逻辑的完整性
4. 用更合理的情节替代被删除的内容`},pacing_improvement:{name:"节奏优化",description:"优化故事节奏，删除拖沓内容",rules:`请按照以下规则改写：
1. 删除重复和拖沓的描写
2. 加快故事节奏，突出重点情节
3. 简化过于冗长的对话和心理描写
4. 保持故事的紧凑性和可读性`},custom:{name:"自定义规则",description:"用户自定义的改写规则",rules:""}};function p(){try{let{presetDb:t}=e.r(84168);t.getAll().forEach(e=>{c[`custom_${e.id}`]={name:e.name,description:e.description,rules:e.rules}})}catch(e){console.error("加载自定义预设失败:",e)}}function m(e,t,r){let n=`custom_${Date.now()}`;return c={...c,[n]:{name:e,description:t,rules:r}},n}async function g(t,r,n,a,i,o){try{let{novelContextDb:l,chapterContextDb:d}=e.r(84168),u=l.getByNovelId(t),c=d.getByChapter(t,r),p={originalText:n,rules:a,chapterTitle:i,chapterNumber:r,model:o,novelContext:u?{summary:u.summary,mainCharacters:u.mainCharacters,worldSetting:u.worldSetting,writingStyle:u.writingStyle,tone:u.tone}:void 0,chapterContext:c?{previousChapterSummary:c.previousChapterSummary,keyEvents:c.keyEvents,characterStates:c.characterStates,plotProgress:c.plotProgress,contextualNotes:c.contextualNotes}:void 0};return await s(p)}catch(e){return console.error("带上下文重写失败:",e),await s({originalText:n,rules:a,chapterTitle:i,chapterNumber:r,model:o})}}},85917,(e,t,r)=>{},41171,e=>{"use strict";e.s(["handler",()=>T,"patchFetch",()=>E,"routeModule",()=>A,"serverHooks",()=>b,"workAsyncStorage",()=>I,"workUnitAsyncStorage",()=>C],41171);var t=e.i(47909),r=e.i(74017),n=e.i(96250),a=e.i(59756),s=e.i(61916),i=e.i(69741),o=e.i(16795),l=e.i(87718),d=e.i(95169),u=e.i(47587),c=e.i(66012),p=e.i(70101),m=e.i(26937),g=e.i(10372),h=e.i(93695);e.i(52474);var w=e.i(220);e.s(["GET",()=>v],37718);var y=e.i(89171),f=e.i(84168),x=e.i(71994);async function v(e){try{let{searchParams:t}=new URL(e.url),r=t.get("jobId");if(!r)return y.NextResponse.json({success:!1,error:"缺少任务ID"},{status:400});let n=f.jobDb.getById(r);if(!n)return y.NextResponse.json({success:!1,error:"任务不存在"},{status:404});let a=(0,x.getApiKeyStats)(),s=await (0,x.testGeminiConnection)(),i=n.details?.chapterResults?.filter(e=>e&&!e.success)||[],o=function(e){let t={},r={},n=[],a=[],s=[];for(let i of e){if(!i.error)continue;let e=i.error.toLowerCase();e.includes("限流")||e.includes("429")?t["API限流"]=(t["API限流"]||0)+1:e.includes("超时")||e.includes("timeout")?(t["请求超时"]=(t["请求超时"]||0)+1,n.push(i)):e.includes("没有收到有效的响应")||e.includes("响应内容格式错误")?(t["响应格式错误"]=(t["响应格式错误"]||0)+1,a.push(i)):e.includes("网络错误")||e.includes("fetch")?(t["网络错误"]=(t["网络错误"]||0)+1,s.push(i)):e.includes("安全过滤")?t["内容过滤"]=(t["内容过滤"]||0)+1:t["其他错误"]=(t["其他错误"]||0)+1,i.apiKeyUsed&&(r[i.apiKeyUsed]=(r[i.apiKeyUsed]||0)+1)}return{totalFailures:e.length,errorTypes:t,apiKeyErrors:r,patterns:{timeoutErrors:n.length,contentErrors:a.length,networkErrors:s.length},mostCommonError:Object.entries(t).sort(([,e],[,t])=>t-e)[0]?.[0]||"unknown",problematicApiKey:Object.entries(r).sort(([,e],[,t])=>t-e)[0]?.[0]||"none"}}(i),l={jobInfo:{id:n.id,status:n.status,progress:n.progress,totalChapters:n.details?.totalChapters||0,completedChapters:n.details?.completedChapters||0,failedChapters:n.details?.failedChapters||0,model:n.details?.model,concurrency:n.details?.concurrency},apiKeyStatus:{stats:a,connectionTest:s,recommendations:function(e){let t=[],r=e.filter(e=>!e.isAvailable);r.length>0&&t.push({type:"warning",message:`${r.length} 个API Key当前不可用，可能处于冷却期`,action:"等待冷却期结束或检查API配额"});let n=e.filter(e=>e.requestCount>100);return n.length>0&&t.push({type:"info",message:`${n.length} 个API Key使用频率较高`,action:"考虑添加更多API Key以分散负载"}),e.every(e=>!e.isAvailable)&&t.push({type:"error",message:"所有API Key都不可用",action:"检查API配额或等待冷却期结束"}),t}(a)},errorAnalysis:o,recommendations:function(e,t,r){let n=[];"API限流"===t.mostCommonError&&n.push({type:"warning",category:"限流问题",message:"API限流是主要问题",actions:["降低并发数量（建议设为1-2）","增加请求间隔时间","使用重试功能处理失败章节","考虑在API使用量较低的时间段进行处理"]}),t.patterns.contentErrors>0&&n.push({type:"error",category:"响应问题",message:"检测到响应内容格式错误",actions:["检查改写规则是否过于复杂","尝试简化提示词","使用重试功能重新处理失败章节","考虑切换到更稳定的模型"]}),t.patterns.networkErrors>0&&n.push({type:"warning",category:"网络问题",message:"检测到网络连接问题",actions:["检查网络连接稳定性","增加请求超时时间","使用重试功能处理失败章节"]});let a=e.details?.failedChapters/e.details?.totalChapters||0;return a>.1&&n.push({type:"warning",category:"高失败率",message:`失败率较高 (${Math.round(100*a)}%)`,actions:["使用失败章节重试功能","检查API Key配额状态","考虑调整改写策略","分批处理章节以降低负载"]}),r.filter(e=>e.isAvailable).length<2&&n.push({type:"error",category:"API Key不足",message:"可用API Key数量不足",actions:["等待API Key冷却期结束","检查API配额是否用尽","考虑添加更多API Key","降低处理并发数"]}),n}(n,o,a),systemHealth:{timestamp:new Date().toISOString(),totalApiCalls:a.reduce((e,t)=>e+t.requestCount,0),availableKeys:a.filter(e=>e.isAvailable).length,totalKeys:a.length}};return y.NextResponse.json({success:!0,data:l})}catch(e){return console.error("获取诊断信息失败:",e),y.NextResponse.json({success:!1,error:"获取诊断信息失败"},{status:500})}}var S=e.i(37718);let A=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/rewrite/diagnostics/route",pathname:"/api/rewrite/diagnostics",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/rewrite/diagnostics/route.ts",nextConfigOutput:"",userland:S}),{workAsyncStorage:I,workUnitAsyncStorage:C,serverHooks:b}=A;function E(){return(0,n.patchFetch)({workAsyncStorage:I,workUnitAsyncStorage:C})}async function T(e,t,n){var y;let f="/api/rewrite/diagnostics/route";f=f.replace(/\/index$/,"")||"/";let x=await A.prepare(e,t,{srcPage:f,multiZoneDraftMode:!1});if(!x)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:v,params:S,nextConfig:I,isDraftMode:C,prerenderManifest:b,routerServerContext:E,isOnDemandRevalidate:T,revalidateOnlyGenerated:$,resolvedPathname:U}=x,P=(0,i.normalizeAppPath)(f),D=!!(b.dynamicRoutes[P]||b.routes[U]);if(D&&!C){let e=!!b.routes[U],t=b.dynamicRoutes[P];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let R=null;!D||A.isDev||C||(R="/index"===(R=U)?"/":R);let k=!0===A.isDev||!D,N=D&&!k,O=e.method||"GET",j=(0,s.getTracer)(),K=j.getActiveScopeSpan(),q={params:S,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!I.experimental.cacheComponents,authInterrupts:!!I.experimental.authInterrupts},supportsDynamicResponse:k,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(y=I.experimental)?void 0:y.cacheLife,isRevalidate:N,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>A.onRequestError(e,t,n,E)},sharedContext:{buildId:v}},_=new o.NodeNextRequest(e),B=new o.NodeNextResponse(t),M=l.NextRequestAdapter.fromNodeNextRequest(_,(0,l.signalFromNodeResponse)(t));try{let i=async r=>A.handle(M,q).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=j.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${O} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${O} ${e.url}`)}),o=async s=>{var o,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&T&&$&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=q.renderOpts.fetchMetrics;let l=q.renderOpts.pendingWaitUntil;l&&n.waitUntil&&(n.waitUntil(l),l=void 0);let d=q.renderOpts.collectedTags;if(!D)return await (0,c.sendResponse)(_,B,o,q.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(o.headers);d&&(t[g.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==q.renderOpts.collectedRevalidate&&!(q.renderOpts.collectedRevalidate>=g.INFINITE_CACHE)&&q.renderOpts.collectedRevalidate,n=void 0===q.renderOpts.collectedExpire||q.renderOpts.collectedExpire>=g.INFINITE_CACHE?void 0:q.renderOpts.collectedExpire;return{value:{kind:w.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await A.onRequestError(e,t,{routerKind:"App Router",routePath:f,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:N,isOnDemandRevalidate:T})},E),t}},h=await A.handleResponse({req:e,nextConfig:I,cacheKey:R,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:T,revalidateOnlyGenerated:$,responseGenerator:d,waitUntil:n.waitUntil});if(!D)return null;if((null==h||null==(o=h.value)?void 0:o.kind)!==w.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(l=h.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",T?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let y=(0,p.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&D||y.delete(g.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||y.get("Cache-Control")||y.set("Cache-Control",(0,m.getCacheControlHeader)(h.cacheControl)),await (0,c.sendResponse)(_,B,new Response(h.value.body,{headers:y,status:h.value.status||200})),null};K?await o(K):await j.withPropagatedContext(e.headers,()=>j.trace(d.BaseServerSpan.handleRequest,{spanName:`${O} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":O,"http.target":e.url}},o))}catch(t){if(K||t instanceof h.NoFallbackError||await A.onRequestError(e,t,{routerKind:"App Router",routePath:P,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:N,isOnDemandRevalidate:T})}),D)throw t;return await (0,c.sendResponse)(_,B,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__1ff9e42b._.js.map