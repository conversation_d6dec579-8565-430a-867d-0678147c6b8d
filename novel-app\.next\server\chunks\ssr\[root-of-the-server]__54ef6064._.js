module.exports=[18622,(a,b,c)=>{b.exports=a.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},42602,(a,b,c)=>{"use strict";b.exports=a.r(18622)},87924,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactJsxRuntime},72131,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].React},5257,a=>{"use strict";a.s(["default",()=>d]);var b=a.i(87924),c=a.i(72131);function d(){let[a,d]=(0,c.useState)([]),[e,f]=(0,c.useState)(!0),[g,h]=(0,c.useState)(null),[i,j]=(0,c.useState)([]),k=async()=>{try{let a=await fetch("/api/merge"),b=await a.json();b.success?d(b.data):console.error("获取小说列表失败:",b.error)}catch(a){console.error("获取小说列表失败:",a)}finally{f(!1)}},l=async a=>{h(a);try{let b=await fetch("/api/merge",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({novelId:a})}),c=await b.json();c.success?(j(a=>[...a,c.data]),await k()):alert(`合并失败: ${c.error}`)}catch(a){console.error("合并失败:",a),alert("合并失败")}finally{h(null)}};return((0,c.useEffect)(()=>{k()},[]),e)?(0,b.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,b.jsx)("div",{className:"text-lg",children:"加载中..."})}):(0,b.jsxs)("div",{className:"max-w-6xl mx-auto p-6",children:[(0,b.jsxs)("div",{className:"mb-8",children:[(0,b.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"合并改写章节"}),(0,b.jsx)("p",{className:"text-gray-600",children:"将改写完成的章节合并为完整的小说文件"})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[(0,b.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"可合并的小说"}),0===a.length?(0,b.jsx)("div",{className:"text-center py-8 text-gray-500",children:"暂无可合并的小说"}):(0,b.jsx)("div",{className:"space-y-4",children:a.map(a=>(0,b.jsxs)("div",{className:"border rounded-lg p-4 flex justify-between items-center",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"font-medium text-lg",children:a.title}),(0,b.jsxs)("p",{className:"text-sm text-gray-600",children:["已改写章节: ",a.rewrittenChaptersCount," / 总章节: ",a.chapterCount||0]}),(0,b.jsxs)("p",{className:"text-xs text-gray-500",children:["创建时间: ",new Date(a.createdAt).toLocaleString()]})]}),(0,b.jsx)("button",{onClick:()=>l(a.id),disabled:g===a.id,className:`px-4 py-2 rounded-md font-medium ${g===a.id?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}`,children:g===a.id?"合并中...":"合并章节"})]},a.id))})]}),i.length>0&&(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,b.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"合并结果"}),(0,b.jsx)("div",{className:"space-y-3",children:i.map((a,c)=>(0,b.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("div",{className:"flex-shrink-0",children:(0,b.jsx)("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,b.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,b.jsxs)("div",{className:"ml-3",children:[(0,b.jsxs)("h3",{className:"text-sm font-medium text-green-800",children:["成功合并《",a.novelTitle,"》"]}),(0,b.jsxs)("p",{className:"text-sm text-green-700 mt-1",children:["文件保存在: ",a.filePath]})]})]})},c))})]})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__54ef6064._.js.map