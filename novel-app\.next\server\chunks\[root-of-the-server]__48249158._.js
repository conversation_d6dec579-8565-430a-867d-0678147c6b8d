module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},22734,(e,t,r)=>{t.exports=e.x("fs",()=>require("fs"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},84168,e=>{"use strict";e.s(["chapterContextDb",()=>N,"chapterDb",()=>v,"characterDb",()=>w,"jobDb",()=>S,"novelContextDb",()=>I,"novelDb",()=>m,"presetDb",()=>D,"ruleDb",()=>y]);var t=e.i(22734),r=e.i(14747),n=e.i(54799);let i=r.default.join(process.cwd(),"data"),a=r.default.join(i,"novels.json"),l=r.default.join(i,"chapters.json"),s=r.default.join(i,"rewrite_rules.json"),o=r.default.join(i,"rewrite_jobs.json"),u=r.default.join(i,"characters.json"),d=r.default.join(i,"presets.json"),c=r.default.join(i,"novel-contexts.json"),p=r.default.join(i,"chapter-contexts.json");function f(){t.default.existsSync(i)||t.default.mkdirSync(i,{recursive:!0})}function h(e){if(f(),!t.default.existsSync(e))return[];try{let r=t.default.readFileSync(e,"utf-8");return JSON.parse(r)}catch(t){return console.error(`Error reading ${e}:`,t),[]}}function g(e,r){f();try{t.default.writeFileSync(e,JSON.stringify(r,null,2),"utf-8")}catch(t){throw console.error(`Error writing ${e}:`,t),t}}function x(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}let m={getAll:()=>h(a),getById:e=>h(a).find(t=>t.id===e),create:e=>{var t;let r=h(a),i=(t=e.title,n.default.createHash("md5").update(t).digest("hex").substring(0,18)),l=r.find(e=>e.id===i);if(l)return l.filename=e.filename,l.chapterCount=e.chapterCount,g(a,r),l;let s={...e,id:i,createdAt:new Date().toISOString()};return r.push(s),g(a,r),s},update:(e,t)=>{let r=h(a),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t},g(a,r),r[n])},delete:e=>{let t=h(a),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(a,t),!0)}},v={getAll:()=>h(l),getByNovelId:e=>h(l).filter(t=>t.novelId===e),getById:e=>h(l).find(t=>t.id===e),create:e=>{let t=h(l),r={...e,id:x(),createdAt:new Date().toISOString()};return t.push(r),g(l,t),r},createBatch:e=>{let t=h(l),r=e.map(e=>({...e,id:x(),createdAt:new Date().toISOString()}));return t.push(...r),g(l,t),r},delete:e=>{let t=h(l),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(l,t),!0)},deleteByNovelId:e=>{let t=h(l).filter(t=>t.novelId!==e);return g(l,t),!0}},y={getAll:()=>h(s),getById:e=>h(s).find(t=>t.id===e),create:e=>{let t=h(s),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(s,t),r},update:(e,t)=>{let r=h(s),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(s,r),r[n])},delete:e=>{let t=h(s),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(s,t),!0)}},S={getAll:()=>h(o),getById:e=>h(o).find(t=>t.id===e),create:e=>{let t=h(o),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(o,t),r},update:(e,t)=>{let r=h(o),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(o,r),r[n])},delete:e=>{let t=h(o),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(o,t),!0)}},w={getAll:()=>h(u),getByNovelId:e=>h(u).filter(t=>t.novelId===e),getById:e=>h(u).find(t=>t.id===e),create:e=>{let t=h(u),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(u,t),r},update:(e,t)=>{let r=h(u),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(u,r),r[n])},delete:e=>{let t=h(u),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(u,t),!0)},deleteByNovelId:e=>{let t=h(u).filter(t=>t.novelId!==e);return g(u,t),!0}},D={getAll:()=>h(d),getById:e=>h(d).find(t=>t.id===e),create:e=>{let t=h(d),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(d,t),r},update:(e,t)=>{let r=h(d),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(d,r),r[n])},delete:e=>{let t=h(d),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(d,t),!0)}},I={getAll:()=>h(c),getByNovelId:e=>h(c).find(t=>t.novelId===e),getById:e=>h(c).find(t=>t.id===e),create:e=>{let t=h(c),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(c,t),r},update:(e,t)=>{let r=h(c),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(c,r),r[n]},delete:e=>{let t=h(c),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(c,t),!0)}},N={getAll:()=>h(p),getByNovelId:e=>h(p).filter(t=>t.novelId===e),getByChapter:(e,t)=>h(p).find(r=>r.novelId===e&&r.chapterNumber===t),getById:e=>h(p).find(t=>t.id===e),create:e=>{let t=h(p),r={...e,id:x(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(p,t),r},update:(e,t)=>{let r=h(p),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(p,r),r[n]},delete:e=>{let t=h(p),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(p,t),!0)},getContextWindow:(e,t,r=2)=>{let n=h(p).filter(t=>t.novelId===e),i=Math.max(1,t-r),a=t+r;return n.filter(e=>e.chapterNumber>=i&&e.chapterNumber<=a).sort((e,t)=>e.chapterNumber-t.chapterNumber)}}},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},23122,e=>{"use strict";e.s(["fileManager",()=>i]);var t=e.i(22734),r=e.i(14747);class n{static instance;baseDir;constructor(){this.baseDir=process.cwd()}static getInstance(){return n.instance||(n.instance=new n),n.instance}ensureDir(e){t.default.existsSync(e)||t.default.mkdirSync(e,{recursive:!0})}getNovelsDir(){return r.default.join(this.baseDir,"..","novels")}getChaptersDir(){return r.default.join(this.baseDir,"..","chapters")}getDataDir(){let e=r.default.join(this.baseDir,"data");return this.ensureDir(e),e}getRewrittenDir(){let e=r.default.join(this.getDataDir(),"rewritten");return this.ensureDir(e),e}getNovelRewrittenDir(e){let t=r.default.join(this.getRewrittenDir(),this.sanitizeFilename(e));return this.ensureDir(t),t}getDoneNovelsDir(){let e=r.default.join(this.baseDir,"..","done-novels");return this.ensureDir(e),e}getNovelChaptersDir(e){let t=this.getChaptersDir();this.ensureDir(t);let n=r.default.join(t,this.sanitizeFilename(e));return this.ensureDir(n),n}sanitizeFilename(e){return e.replace(/[<>:"/\\|?*]/g,"_").trim()}readFile(e){try{return t.default.readFileSync(e,"utf-8")}catch(t){throw console.error(`读取文件失败: ${e}`,t),t}}writeFile(e,n){try{let i=r.default.dirname(e);this.ensureDir(i),t.default.writeFileSync(e,n,"utf-8")}catch(t){throw console.error(`写入文件失败: ${e}`,t),t}}fileExists(e){return t.default.existsSync(e)}listFiles(e,n){try{if(!t.default.existsSync(e))return[];let i=t.default.readdirSync(e);if(n)return i.filter(e=>{let t=r.default.extname(e).toLowerCase();return n.includes(t)});return i}catch(t){return console.error(`读取目录失败: ${e}`,t),[]}}getFileStats(e){try{return t.default.statSync(e)}catch(t){return console.error(`获取文件信息失败: ${e}`,t),null}}deleteFile(e){try{if(t.default.existsSync(e))return t.default.unlinkSync(e),!0;return!1}catch(t){return console.error(`删除文件失败: ${e}`,t),!1}}deleteDir(e){try{if(t.default.existsSync(e))return t.default.rmSync(e,{recursive:!0,force:!0}),!0;return!1}catch(t){return console.error(`删除目录失败: ${e}`,t),!1}}copyFile(e,n){try{let i=r.default.dirname(n);return this.ensureDir(i),t.default.copyFileSync(e,n),!0}catch(t){return console.error(`复制文件失败: ${e} -> ${n}`,t),!1}}moveFile(e,n){try{let i=r.default.dirname(n);return this.ensureDir(i),t.default.renameSync(e,n),!0}catch(t){return console.error(`移动文件失败: ${e} -> ${n}`,t),!1}}getDirSize(e){let n=0;try{if(!t.default.existsSync(e))return 0;for(let i of t.default.readdirSync(e)){let a=r.default.join(e,i),l=t.default.statSync(a);l.isDirectory()?n+=this.getDirSize(a):n+=l.size}}catch(t){console.error(`计算目录大小失败: ${e}`,t)}return n}formatFileSize(e){if(0===e)return"0 B";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB"][t]}createBackup(e){try{let t=new Date().toISOString().replace(/[:.]/g,"-"),n=r.default.extname(e),i=r.default.basename(e,n),a=r.default.dirname(e),l=r.default.join(a,`${i}_backup_${t}${n}`);if(this.copyFile(e,l))return l;return null}catch(t){return console.error(`创建备份失败: ${e}`,t),null}}mergeRewrittenChapters(e){try{let t=this.getNovelRewrittenDir(e),n=this.getDoneNovelsDir(),i=this.listFiles(t,[".txt"]).filter(e=>e.startsWith("chapter_")&&e.includes("_rewritten")).sort((e,t)=>{let r=parseInt(e.match(/chapter_(\d+)/)?.[1]||"0"),n=parseInt(t.match(/chapter_(\d+)/)?.[1]||"0");return r-n});if(0===i.length)return{success:!1,error:"没有找到改写的章节文件"};let a="";for(let e of i){let n=r.default.join(t,e),i=this.readFile(n);a+=i+"\n\n"}let l=new Date().toISOString().replace(/[:.]/g,"-").substring(0,19),s=`${this.sanitizeFilename(e)}_merged_${l}.txt`,o=r.default.join(n,s);return this.writeFile(o,a.trim()),{success:!0,filePath:o}}catch(t){return console.error(`合并章节失败: ${e}`,t),{success:!1,error:t instanceof Error?t.message:"未知错误"}}}cleanupBackups(e,t=5){try{let n=this.listFiles(e).filter(e=>e.includes("_backup_")).map(t=>({name:t,path:r.default.join(e,t),stats:this.getFileStats(r.default.join(e,t))})).filter(e=>null!==e.stats).sort((e,t)=>t.stats.mtime.getTime()-e.stats.mtime.getTime());if(n.length>t)for(let e of n.slice(t))this.deleteFile(e.path)}catch(t){console.error(`清理备份文件失败: ${e}`,t)}}}let i=n.getInstance()},73665,e=>{"use strict";e.s(["getAvailableNovels",()=>c,"isNovelParsed",()=>p,"parseChapterRange",()=>h,"parseNovelFile",()=>s,"reparseNovel",()=>f]);var t=e.i(22734),r=e.i(14747),n=e.i(84168),i=e.i(23122);let a=[/^\s*(?:第[一二三四五六七八九十百千万\d]+[卷集部])\s*.*$/gmi,/^\s*(?:[卷集部][一二三四五六七八九十百千万\d]+)\s*.*$/gmi],l=[/^\s*(?:第[一二三四五六七八九十百千万\d]+[章节回])\s*.*$/gmi,/^\s*(?:Chapter\s+\d+)\s*.*$/gmi];async function s(e){let i=r.default.basename(e),l=i.replace(/\.(txt|md)$/i,""),s=t.default.readFileSync(e,"utf-8"),c=n.novelDb.create({title:l,filename:i}),p=function(e,t){let r=[],n=function(e){let t=[];for(let r of a)for(let n of Array.from(e.matchAll(r)))t.push({index:n.index,title:u(n[0])});return t.sort((e,t)=>e.index-t.index)}(e);if(n.length>0){console.log(`Found ${n.length} volumes`);let i=1;for(let a=0;a<n.length;a++){let l=n[a].index,s=a+1<n.length?n[a+1].index:e.length,u=o(e.slice(l,s),t,i,n[a].title);r.push(...u),i+=u.length}}else{let n=o(e,t,1);r.push(...n)}return console.log(`Successfully parsed ${r.length} chapters`),r}(s,c.id),f=n.chapterDb.createBatch(p);return n.novelDb.update(c.id,{chapterCount:f.length}),await d(f),{novel:{...c,chapterCount:f.length},chapters:f}}function o(e,t,r,n){let i=[],a=r,s=null,o=-1;for(let t of l){let r=e.match(t),n=r?r.length:0;n>o&&(o=n,s=t)}if(!s||0===o){let r=e.trim();return r.length>100&&i.push({novelId:t,chapterNumber:a,title:n||"全文",content:r,filename:`chapter_${a}.txt`}),i}let d=RegExp(`(${s.source})`,"gmi"),c=e.split(d),p=c[0]?.trim();p&&p.length>100&&(i.push({novelId:t,chapterNumber:a,title:"序章",content:p,filename:`chapter_${a}.txt`}),a++),console.log(c.map((e,t)=>t+e));for(let e=1;e<c.length;e+=2){let r=c[e],n=c[e+1]||"";if(!r)continue;let l=(r+n).trim();l.length>100&&(i.push({novelId:t,chapterNumber:a,title:u(r),content:l,filename:`chapter_${a}.txt`}),a++)}return i}function u(e){let t=e.trim().split("\n"),r=t[0].trim();if(r.length<100&&r.length>0)return r;for(let e=0;e<Math.min(3,t.length);e++){let r=t[e].trim();if(r.length>0&&r.length<100)return r}return"未命名章节"}async function d(e){for(let t of[...new Set(e.map(e=>e.novelId))]){let a=n.novelDb.getById(t);if(!a)continue;let l=i.fileManager.getNovelChaptersDir(a.title);for(let n of e.filter(e=>e.novelId===t)){let e=r.default.join(l,n.filename);i.fileManager.writeFile(e,n.content)}}}function c(){let e=i.fileManager.getNovelsDir();return i.fileManager.listFiles(e,[".txt",".md"])}function p(e){return n.novelDb.getAll().some(t=>t.filename===e)}async function f(e){let t=i.fileManager.getNovelsDir(),a=r.default.join(t,e);if(!i.fileManager.fileExists(a))return null;let l=n.novelDb.getAll().find(t=>t.filename===e);return l&&(n.chapterDb.deleteByNovelId(l.id),n.novelDb.delete(l.id)),await s(a)}function h(e,t){let r=[];for(let n of e.split(",").map(e=>e.trim()))if(n.includes("-")){let[e,i]=n.split("-").map(e=>parseInt(e.trim()));if(!isNaN(e)&&!isNaN(i)&&e<=i)for(let n=e;n<=Math.min(i,t);n++)n>0&&!r.includes(n)&&r.push(n)}else{let e=parseInt(n);!isNaN(e)&&e>0&&e<=t&&!r.includes(e)&&r.push(e)}return r.sort((e,t)=>e-t)}},78504,(e,t,r)=>{},97656,e=>{"use strict";e.s(["handler",()=>C,"patchFetch",()=>A,"routeModule",()=>N,"serverHooks",()=>j,"workAsyncStorage",()=>R,"workUnitAsyncStorage",()=>b],97656);var t=e.i(47909),r=e.i(74017),n=e.i(96250),i=e.i(59756),a=e.i(61916),l=e.i(69741),s=e.i(16795),o=e.i(87718),u=e.i(95169),d=e.i(47587),c=e.i(66012),p=e.i(70101),f=e.i(26937),h=e.i(10372),g=e.i(93695);e.i(52474);var x=e.i(220);e.s(["GET",()=>w,"POST",()=>D],1003);var m=e.i(89171),v=e.i(84168),y=e.i(73665),S=e.i(14747);async function w(){try{let e=v.novelDb.getAll(),t=(0,y.getAvailableNovels)().map(t=>{let r=e.find(e=>e.filename===t);return{filename:t,parsed:!!r,novel:r||null}});return m.NextResponse.json({success:!0,data:{novels:e,availableFiles:t}})}catch(e){return console.error("获取小说列表失败:",e),m.NextResponse.json({success:!1,error:"获取小说列表失败"},{status:500})}}async function D(e){try{let t,{filename:r,reparse:n=!1}=await e.json();if(!r)return m.NextResponse.json({success:!1,error:"文件名不能为空"},{status:400});if(!(0,y.getAvailableNovels)().includes(r))return m.NextResponse.json({success:!1,error:"文件不存在"},{status:404});if(!n&&(0,y.isNovelParsed)(r))return m.NextResponse.json({success:!1,error:"该小说已经解析过，如需重新解析请设置reparse=true"},{status:409});let i=S.default.join(process.cwd(),"..","novels"),a=S.default.join(i,r);if(!(t=n?await (0,y.reparseNovel)(r):await (0,y.parseNovelFile)(a)))return m.NextResponse.json({success:!1,error:"解析失败"},{status:500});return m.NextResponse.json({success:!0,data:t,message:`成功解析小说《${t.novel.title}》，共${t.chapters.length}章`})}catch(e){return console.error("解析小说失败:",e),m.NextResponse.json({success:!1,error:"解析小说失败"},{status:500})}}var I=e.i(1003);let N=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/novels/route",pathname:"/api/novels",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/novels/route.ts",nextConfigOutput:"",userland:I}),{workAsyncStorage:R,workUnitAsyncStorage:b,serverHooks:j}=N;function A(){return(0,n.patchFetch)({workAsyncStorage:R,workUnitAsyncStorage:b})}async function C(e,t,n){var m;let v="/api/novels/route";v=v.replace(/\/index$/,"")||"/";let y=await N.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!y)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:S,params:w,nextConfig:D,isDraftMode:I,prerenderManifest:R,routerServerContext:b,isOnDemandRevalidate:j,revalidateOnlyGenerated:A,resolvedPathname:C}=y,E=(0,l.normalizeAppPath)(v),O=!!(R.dynamicRoutes[E]||R.routes[C]);if(O&&!I){let e=!!R.routes[C],t=R.dynamicRoutes[E];if(t&&!1===t.fallback&&!e)throw new g.NoFallbackError}let $=null;!O||N.isDev||I||($="/index"===($=C)?"/":$);let F=!0===N.isDev||!O,_=O&&!F,B=e.method||"GET",M=(0,a.getTracer)(),k=M.getActiveScopeSpan(),P={params:w,prerenderManifest:R,renderOpts:{experimental:{cacheComponents:!!D.experimental.cacheComponents,authInterrupts:!!D.experimental.authInterrupts},supportsDynamicResponse:F,incrementalCache:(0,i.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(m=D.experimental)?void 0:m.cacheLife,isRevalidate:_,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>N.onRequestError(e,t,n,b)},sharedContext:{buildId:S}},T=new s.NodeNextRequest(e),q=new s.NodeNextResponse(t),H=o.NextRequestAdapter.fromNodeNextRequest(T,(0,o.signalFromNodeResponse)(t));try{let l=async r=>N.handle(H,P).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=M.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==u.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let i=n.get("next.route");if(i){let e=`${B} ${i}`;r.setAttributes({"next.route":i,"http.route":i,"next.span_name":e}),r.updateName(e)}else r.updateName(`${B} ${e.url}`)}),s=async a=>{var s,o;let u=async({previousCacheEntry:r})=>{try{if(!(0,i.getRequestMeta)(e,"minimalMode")&&j&&A&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let s=await l(a);e.fetchMetrics=P.renderOpts.fetchMetrics;let o=P.renderOpts.pendingWaitUntil;o&&n.waitUntil&&(n.waitUntil(o),o=void 0);let u=P.renderOpts.collectedTags;if(!O)return await (0,c.sendResponse)(T,q,s,P.renderOpts.pendingWaitUntil),null;{let e=await s.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(s.headers);u&&(t[h.NEXT_CACHE_TAGS_HEADER]=u),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==P.renderOpts.collectedRevalidate&&!(P.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&P.renderOpts.collectedRevalidate,n=void 0===P.renderOpts.collectedExpire||P.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:P.renderOpts.collectedExpire;return{value:{kind:x.CachedRouteKind.APP_ROUTE,status:s.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await N.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:j})},b),t}},g=await N.handleResponse({req:e,nextConfig:D,cacheKey:$,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:R,isRoutePPREnabled:!1,isOnDemandRevalidate:j,revalidateOnlyGenerated:A,responseGenerator:u,waitUntil:n.waitUntil});if(!O)return null;if((null==g||null==(s=g.value)?void 0:s.kind)!==x.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==g||null==(o=g.value)?void 0:o.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,i.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",j?"REVALIDATED":g.isMiss?"MISS":g.isStale?"STALE":"HIT"),I&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(g.value.headers);return(0,i.getRequestMeta)(e,"minimalMode")&&O||m.delete(h.NEXT_CACHE_TAGS_HEADER),!g.cacheControl||t.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,f.getCacheControlHeader)(g.cacheControl)),await (0,c.sendResponse)(T,q,new Response(g.value.body,{headers:m,status:g.value.status||200})),null};k?await s(k):await M.withPropagatedContext(e.headers,()=>M.trace(u.BaseServerSpan.handleRequest,{spanName:`${B} ${e.url}`,kind:a.SpanKind.SERVER,attributes:{"http.method":B,"http.target":e.url}},s))}catch(t){if(k||t instanceof g.NoFallbackError||await N.onRequestError(e,t,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:j})}),O)throw t;return await (0,c.sendResponse)(T,q,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__48249158._.js.map