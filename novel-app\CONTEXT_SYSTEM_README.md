# 小说上下文记忆系统

## 概述

这个上下文记忆系统解决了小说重写过程中每章单独处理导致的上下文丢失问题。通过AI预分析和上下文注入，确保重写内容的连贯性和一致性。

**🎉 新功能：现在支持浏览器端使用！** 上下文系统不再局限于服务器端，可以在浏览器中直接使用相同的API。

## 核心功能

### 1. 小说整体上下文分析
- 提取小说摘要
- 识别主要人物和关系
- 分析世界观设定
- 识别写作风格和语调
- 提取主要情节线和主题

### 2. 章节上下文分析
- 识别章节关键事件
- 跟踪人物状态变化
- 分析情节推进要点
- 建立章节间的关联

### 3. 上下文注入重写
- 在重写时自动注入相关上下文
- 保持人物性格一致性
- 维护情节逻辑连贯性
- 保持写作风格统一

## 使用流程

### 服务器端使用

#### 1. 上传和解析小说
```bash
POST /api/novels/parse
{
  "filePath": "path/to/novel.txt"
}
```

#### 2. 分析小说上下文
```bash
POST /api/context/analyze
{
  "novelId": "novel-id",
  "analyzeChapters": true
}
```

### 浏览器端使用

#### 1. 导入上下文分析器
```javascript
import { contextAnalyzer } from '@/lib/context-analyzer';
// 现在在浏览器中 contextAnalyzer 不再是 null！
```

#### 2. 使用上下文功能
```javascript
// 分析小说
const novelContext = await contextAnalyzer.analyzeNovel(novelId);

// 获取上下文
const context = await contextAnalyzer.getNovelContext(novelId);

// 分析章节
const chapterContext = await contextAnalyzer.analyzeChapter(novelId, 1);
```

### 3. 查看上下文信息
```bash
# 获取小说整体上下文
GET /api/context/novel?novelId=novel-id

# 获取章节上下文
GET /api/context/chapter?novelId=novel-id&chapterNumber=1

# 获取章节上下文窗口
GET /api/context/window?novelId=novel-id&chapterNumber=2&windowSize=2
```

### 4. 使用上下文进行重写
重写API会自动使用上下文信息，无需额外配置。

## API 端点

### 上下文分析
- `POST /api/context/analyze` - 分析小说并生成上下文
- `GET /api/context/novel` - 获取小说上下文
- `PUT /api/context/novel` - 更新小说上下文
- `DELETE /api/context/novel` - 删除小说上下文

### 章节上下文
- `GET /api/context/chapter` - 获取章节上下文
- `PUT /api/context/chapter` - 更新章节上下文
- `DELETE /api/context/chapter` - 删除章节上下文
- `GET /api/context/window` - 获取章节上下文窗口

## 数据结构

### NovelContext（小说上下文）
```typescript
{
  id: string;
  novelId: string;
  summary: string;                    // 小说摘要
  mainCharacters: Array<{             // 主要人物
    name: string;
    role: string;
    description: string;
    relationships?: string;
  }>;
  worldSetting: string;               // 世界观设定
  writingStyle: string;               // 写作风格
  mainPlotlines: string[];            // 主要情节线
  themes: string[];                   // 主题
  tone: string;                       // 语调
  createdAt: string;
  updatedAt: string;
}
```

### ChapterContext（章节上下文）
```typescript
{
  id: string;
  novelId: string;
  chapterNumber: number;
  keyEvents: string[];                // 关键事件
  characterStates: Array<{            // 人物状态
    name: string;
    status: string;
    emotions: string;
    relationships: string;
  }>;
  plotProgress: string;               // 情节推进
  previousChapterSummary?: string;    // 前章摘要
  nextChapterHints?: string;          // 下章暗示
  contextualNotes: string;            // 上下文注释
  createdAt: string;
  updatedAt: string;
}
```

## 优势

1. **连贯性保证**：确保重写内容与整体背景一致
2. **人物一致性**：维护人物性格和关系的连贯性
3. **情节逻辑**：保持故事发展的逻辑性
4. **风格统一**：维持原有的写作风格和语调
5. **智能优化**：根据上下文智能调整重写策略

## 性能考虑

- 上下文分析是一次性操作，后续重写直接使用
- 支持增量更新，只分析新增或修改的章节
- 上下文窗口机制避免过多信息干扰
- 自动回退机制，确保系统稳定性

## 测试

运行测试脚本验证系统功能：
```bash
node test-context-system.js
```

## 客户端支持

### ClientContextAnalyzer

客户端上下文分析器通过 HTTP API 提供与服务器端相同的功能：

```javascript
import { contextAnalyzer } from '@/lib/context-analyzer';

// 在浏览器中，contextAnalyzer 是 ClientContextAnalyzer 的实例
// 在服务器中，contextAnalyzer 是 ContextAnalyzer 的实例
// 两者提供相同的接口

// 分析小说
await contextAnalyzer.analyzeNovel(novelId);

// 获取上下文
const novelContext = await contextAnalyzer.getNovelContext(novelId);
const chapterContext = await contextAnalyzer.getChapterContext(novelId, chapterNumber);
const contextWindow = await contextAnalyzer.getChapterContextWindow(novelId, chapterNumber, 2);
```

### 通用工具函数

```javascript
import {
  getNovelContext,
  getChapterContext,
  analyzeNovelClient
} from '@/lib/context-client';

// 这些函数自动检测环境并选择合适的实现
const context = await getNovelContext(novelId);
```

### 测试页面

访问 `/test-client-context.html` 来测试浏览器端的上下文功能。

## 注意事项

### 通用注意事项
1. 首次使用需要先进行上下文分析
2. 分析过程可能需要较长时间，请耐心等待
3. 建议在重写前检查上下文信息的准确性
4. 可以手动编辑上下文信息以获得更好的重写效果

### 客户端特定注意事项
1. 客户端版本通过 HTTP API 调用实现，需要网络连接
2. 客户端调用会有网络延迟，但接口保持一致
3. 确保开发服务器正在运行
4. 检查浏览器控制台的错误信息进行故障排除
