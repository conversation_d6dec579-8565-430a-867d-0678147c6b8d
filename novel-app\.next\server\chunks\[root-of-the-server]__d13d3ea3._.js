module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},22734,(e,t,r)=>{t.exports=e.x("fs",()=>require("fs"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},84168,e=>{"use strict";e.s(["chapterContextDb",()=>A,"chapterDb",()=>I,"characterDb",()=>y,"jobDb",()=>w,"novelContextDb",()=>R,"novelDb",()=>v,"presetDb",()=>m,"ruleDb",()=>S]);var t=e.i(22734),r=e.i(14747),n=e.i(54799);let a=r.default.join(process.cwd(),"data"),i=r.default.join(a,"novels.json"),d=r.default.join(a,"chapters.json"),o=r.default.join(a,"rewrite_rules.json"),s=r.default.join(a,"rewrite_jobs.json"),l=r.default.join(a,"characters.json"),u=r.default.join(a,"presets.json"),p=r.default.join(a,"novel-contexts.json"),c=r.default.join(a,"chapter-contexts.json");function x(){t.default.existsSync(a)||t.default.mkdirSync(a,{recursive:!0})}function f(e){if(x(),!t.default.existsSync(e))return[];try{let r=t.default.readFileSync(e,"utf-8");return JSON.parse(r)}catch(t){return console.error(`Error reading ${e}:`,t),[]}}function h(e,r){x();try{t.default.writeFileSync(e,JSON.stringify(r,null,2),"utf-8")}catch(t){throw console.error(`Error writing ${e}:`,t),t}}function g(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}let v={getAll:()=>f(i),getById:e=>f(i).find(t=>t.id===e),create:e=>{var t;let r=f(i),a=(t=e.title,n.default.createHash("md5").update(t).digest("hex").substring(0,18)),d=r.find(e=>e.id===a);if(d)return d.filename=e.filename,d.chapterCount=e.chapterCount,h(i,r),d;let o={...e,id:a,createdAt:new Date().toISOString()};return r.push(o),h(i,r),o},update:(e,t)=>{let r=f(i),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t},h(i,r),r[n])},delete:e=>{let t=f(i),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(i,t),!0)}},I={getAll:()=>f(d),getByNovelId:e=>f(d).filter(t=>t.novelId===e),getById:e=>f(d).find(t=>t.id===e),create:e=>{let t=f(d),r={...e,id:g(),createdAt:new Date().toISOString()};return t.push(r),h(d,t),r},createBatch:e=>{let t=f(d),r=e.map(e=>({...e,id:g(),createdAt:new Date().toISOString()}));return t.push(...r),h(d,t),r},delete:e=>{let t=f(d),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(d,t),!0)},deleteByNovelId:e=>{let t=f(d).filter(t=>t.novelId!==e);return h(d,t),!0}},S={getAll:()=>f(o),getById:e=>f(o).find(t=>t.id===e),create:e=>{let t=f(o),r={...e,id:g(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(o,t),r},update:(e,t)=>{let r=f(o),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(o,r),r[n])},delete:e=>{let t=f(o),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(o,t),!0)}},w={getAll:()=>f(s),getById:e=>f(s).find(t=>t.id===e),create:e=>{let t=f(s),r={...e,id:g(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(s,t),r},update:(e,t)=>{let r=f(s),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(s,r),r[n])},delete:e=>{let t=f(s),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(s,t),!0)}},y={getAll:()=>f(l),getByNovelId:e=>f(l).filter(t=>t.novelId===e),getById:e=>f(l).find(t=>t.id===e),create:e=>{let t=f(l),r={...e,id:g(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(l,t),r},update:(e,t)=>{let r=f(l),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(l,r),r[n])},delete:e=>{let t=f(l),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(l,t),!0)},deleteByNovelId:e=>{let t=f(l).filter(t=>t.novelId!==e);return h(l,t),!0}},m={getAll:()=>f(u),getById:e=>f(u).find(t=>t.id===e),create:e=>{let t=f(u),r={...e,id:g(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(u,t),r},update:(e,t)=>{let r=f(u),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(u,r),r[n])},delete:e=>{let t=f(u),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(u,t),!0)}},R={getAll:()=>f(p),getByNovelId:e=>f(p).find(t=>t.novelId===e),getById:e=>f(p).find(t=>t.id===e),create:e=>{let t=f(p),r={...e,id:g(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(p,t),r},update:(e,t)=>{let r=f(p),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(p,r),r[n]},delete:e=>{let t=f(p),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(p,t),!0)}},A={getAll:()=>f(c),getByNovelId:e=>f(c).filter(t=>t.novelId===e),getByChapter:(e,t)=>f(c).find(r=>r.novelId===e&&r.chapterNumber===t),getById:e=>f(c).find(t=>t.id===e),create:e=>{let t=f(c),r={...e,id:g(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(c,t),r},update:(e,t)=>{let r=f(c),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(c,r),r[n]},delete:e=>{let t=f(c),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(c,t),!0)},getContextWindow:(e,t,r=2)=>{let n=f(c).filter(t=>t.novelId===e),a=Math.max(1,t-r),i=t+r;return n.filter(e=>e.chapterNumber>=a&&e.chapterNumber<=i).sort((e,t)=>e.chapterNumber-t.chapterNumber)}}},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},79126,(e,t,r)=>{},69804,e=>{"use strict";e.s(["handler",()=>D,"patchFetch",()=>b,"routeModule",()=>y,"serverHooks",()=>A,"workAsyncStorage",()=>m,"workUnitAsyncStorage",()=>R],69804);var t=e.i(47909),r=e.i(74017),n=e.i(96250),a=e.i(59756),i=e.i(61916),d=e.i(69741),o=e.i(16795),s=e.i(87718),l=e.i(95169),u=e.i(47587),p=e.i(66012),c=e.i(70101),x=e.i(26937),f=e.i(10372),h=e.i(93695);e.i(52474);var g=e.i(220);e.s(["GET",()=>S],82523);var v=e.i(89171),I=e.i(84168);async function S(e){try{let{searchParams:t}=new URL(e.url),r=t.get("novelId");if(!r)return v.NextResponse.json({success:!1,error:"小说ID不能为空"},{status:400});let n=I.chapterDb.getByNovelId(r);return v.NextResponse.json({success:!0,data:n})}catch(e){return console.error("获取章节列表失败:",e),v.NextResponse.json({success:!1,error:"获取章节列表失败"},{status:500})}}var w=e.i(82523);let y=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/chapters/route",pathname:"/api/chapters",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/chapters/route.ts",nextConfigOutput:"",userland:w}),{workAsyncStorage:m,workUnitAsyncStorage:R,serverHooks:A}=y;function b(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:R})}async function D(e,t,n){var v;let I="/api/chapters/route";I=I.replace(/\/index$/,"")||"/";let S=await y.prepare(e,t,{srcPage:I,multiZoneDraftMode:!1});if(!S)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:w,params:m,nextConfig:R,isDraftMode:A,prerenderManifest:b,routerServerContext:D,isOnDemandRevalidate:O,revalidateOnlyGenerated:j,resolvedPathname:E}=S,C=(0,d.normalizeAppPath)(I),N=!!(b.dynamicRoutes[C]||b.routes[E]);if(N&&!A){let e=!!b.routes[E],t=b.dynamicRoutes[C];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let q=null;!N||y.isDev||A||(q="/index"===(q=E)?"/":q);let T=!0===y.isDev||!N,k=N&&!T,B=e.method||"GET",P=(0,i.getTracer)(),_=P.getActiveScopeSpan(),H={params:m,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!R.experimental.cacheComponents,authInterrupts:!!R.experimental.authInterrupts},supportsDynamicResponse:T,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=R.experimental)?void 0:v.cacheLife,isRevalidate:k,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>y.onRequestError(e,t,n,D)},sharedContext:{buildId:w}},U=new o.NodeNextRequest(e),M=new o.NodeNextResponse(t),$=s.NextRequestAdapter.fromNodeNextRequest(U,(0,s.signalFromNodeResponse)(t));try{let d=async r=>y.handle($,H).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=P.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${B} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${B} ${e.url}`)}),o=async i=>{var o,s;let l=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&O&&j&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await d(i);e.fetchMetrics=H.renderOpts.fetchMetrics;let s=H.renderOpts.pendingWaitUntil;s&&n.waitUntil&&(n.waitUntil(s),s=void 0);let l=H.renderOpts.collectedTags;if(!N)return await (0,p.sendResponse)(U,M,o,H.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[f.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==H.renderOpts.collectedRevalidate&&!(H.renderOpts.collectedRevalidate>=f.INFINITE_CACHE)&&H.renderOpts.collectedRevalidate,n=void 0===H.renderOpts.collectedExpire||H.renderOpts.collectedExpire>=f.INFINITE_CACHE?void 0:H.renderOpts.collectedExpire;return{value:{kind:g.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await y.onRequestError(e,t,{routerKind:"App Router",routePath:I,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:k,isOnDemandRevalidate:O})},D),t}},h=await y.handleResponse({req:e,nextConfig:R,cacheKey:q,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:O,revalidateOnlyGenerated:j,responseGenerator:l,waitUntil:n.waitUntil});if(!N)return null;if((null==h||null==(o=h.value)?void 0:o.kind)!==g.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(s=h.value)?void 0:s.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",O?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,c.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&N||v.delete(f.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,x.getCacheControlHeader)(h.cacheControl)),await (0,p.sendResponse)(U,M,new Response(h.value.body,{headers:v,status:h.value.status||200})),null};_?await o(_):await P.withPropagatedContext(e.headers,()=>P.trace(l.BaseServerSpan.handleRequest,{spanName:`${B} ${e.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":B,"http.target":e.url}},o))}catch(t){if(_||t instanceof h.NoFallbackError||await y.onRequestError(e,t,{routerKind:"App Router",routePath:C,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:k,isOnDemandRevalidate:O})}),N)throw t;return await (0,p.sendResponse)(U,M,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__d13d3ea3._.js.map