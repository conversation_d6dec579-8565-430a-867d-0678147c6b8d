// Gemini API 集成 - 多Key池管理
const API_KEYS = [
  {
    key: 'AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw', // My First Project - 4倍强度
    name: 'My First Project',
    weight: 1, // 权重，表示相对强度
    requestCount: 0,
    lastUsed: 0,
    cooldownUntil: 0, // 冷却时间
  },
  {
    key: 'AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y', // ankibot
    name: 'ankibot',
    weight: 1,
    requestCount: 0,
    lastUsed: 0,
    cooldownUntil: 0,
  },
  {
    key: 'AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY', // Generative Language Client
    name: 'Generative Language Client',
    weight: 1,
    requestCount: 0,
    lastUsed: 0,
    cooldownUntil: 0,
  },
  {
    key: 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc', // In The Novel
    name: 'In The Novel',
    weight: 1,
    requestCount: 0,
    lastUsed: 0,
    cooldownUntil: 0,
  },
  {
    key: 'AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk', // chat
    name: 'chat',
    weight: 1,
    requestCount: 0,
    lastUsed: 0,
    cooldownUntil: 0,
  }
];

// API配置
const getGeminiApiUrl = (model: string = 'gemini-2.5-flash-lite') =>
  `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;
const REQUEST_DELAY = 1000; // 请求间隔（毫秒）
const COOLDOWN_DURATION = 60000; // 429错误后的冷却时间（毫秒）
const MAX_RETRIES = 5; // 增加最大重试次数
const EXPONENTIAL_BACKOFF_BASE = 2000; // 指数退避基础时间（毫秒）
const MAX_WAIT_TIME = 30000; // 最大等待时间（毫秒）

// API Key管理类
class ApiKeyManager {
  private keys = [...API_KEYS];

  // 获取最佳可用的API Key
  getBestAvailableKey() {
    const now = Date.now();

    // 过滤掉冷却中的key
    const availableKeys = this.keys.filter(key => key.cooldownUntil <= now);

    if (availableKeys.length === 0) {
      // 如果所有key都在冷却中，返回冷却时间最短的
      return this.keys.reduce((min, key) =>
        key.cooldownUntil < min.cooldownUntil ? key : min
      );
    }

    // 根据权重和使用频率选择最佳key
    const bestKey = availableKeys.reduce((best, key) => {
      const keyScore = key.weight / (key.requestCount + 1);
      const bestScore = best.weight / (best.requestCount + 1);
      return keyScore > bestScore ? key : best;
    });

    return bestKey;
  }

  // 记录API使用
  recordUsage(keyName: string, success: boolean) {
    const key = this.keys.find(k => k.name === keyName);
    if (key) {
      key.requestCount++;
      key.lastUsed = Date.now();

      if (!success) {
        // 如果失败，设置冷却时间
        key.cooldownUntil = Date.now() + COOLDOWN_DURATION;
      }
    }
  }

  // 获取统计信息
  getStats() {
    return this.keys.map(key => ({
      name: key.name,
      requestCount: key.requestCount,
      weight: key.weight,
      isAvailable: key.cooldownUntil <= Date.now(),
      cooldownRemaining: Math.max(0, key.cooldownUntil - Date.now()),
    }));
  }
}

const keyManager = new ApiKeyManager();

export interface RewriteRequest {
  originalText: string;
  rules: string;
  chapterTitle?: string;
  chapterNumber?: number;
  model?: string;
  // 上下文信息
  novelContext?: {
    summary: string;
    mainCharacters: Array<{
      name: string;
      role: string;
      description: string;
      relationships?: string;
    }>;
    worldSetting: string;
    writingStyle: string;
    tone: string;
  };
  chapterContext?: {
    previousChapterSummary?: string;
    keyEvents: string[];
    characterStates: Array<{
      name: string;
      status: string;
      emotions: string;
      relationships: string;
    }>;
    plotProgress: string;
    contextualNotes: string;
  };
}

export interface RewriteResponse {
  rewrittenText: string;
  success: boolean;
  error?: string;
  apiKeyUsed?: string;
  tokensUsed?: number;
  model?: string;
  processingTime?: number;
  detailedError?: string; // 新增详细错误信息
  retryCount?: number; // 新增重试次数记录
}

// 构建改写提示词
function buildPrompt(request: RewriteRequest): string {
  const { originalText, rules, chapterTitle, chapterNumber, novelContext, chapterContext } = request;

  let prompt = `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：

改写规则：
${rules}

${chapterTitle ? `当前章节：${chapterTitle}` : ''}`;

  // 添加小说整体上下文
  if (novelContext) {
    prompt += `

【小说背景信息】
小说摘要：${novelContext.summary}

主要人物：
${novelContext.mainCharacters.map(char =>
      `- ${char.name}(${char.role}): ${char.description}${char.relationships ? ` | 关系：${char.relationships}` : ''}`
    ).join('\n')}

世界观设定：${novelContext.worldSetting}

写作风格：${novelContext.writingStyle}

整体语调：${novelContext.tone}`;
  }

  // 添加章节上下文
  if (chapterContext) {
    prompt += `

【章节上下文信息】`;

    if (chapterContext.previousChapterSummary) {
      prompt += `
前一章摘要：${chapterContext.previousChapterSummary}`;
    }

    if (chapterContext.keyEvents.length > 0) {
      prompt += `
本章关键事件：${chapterContext.keyEvents.join('、')}`;
    }

    if (chapterContext.characterStates.length > 0) {
      prompt += `
人物状态：
${chapterContext.characterStates.map(state =>
        `- ${state.name}: ${state.status} | 情感：${state.emotions} | 关系：${state.relationships}`
      ).join('\n')}`;
    }

    prompt += `
情节推进：${chapterContext.plotProgress}`;

    if (chapterContext.contextualNotes) {
      prompt += `
重要注释：${chapterContext.contextualNotes}`;
    }
  }

  prompt += `

原文内容：
${originalText}

请严格按照改写规则进行改写，同时保持与小说整体背景和上下文的连贯性。改写后的内容应该：
1. 遵循所有指定的改写规则
2. 保持与小说整体背景的一致性
3. 确保人物性格和关系的连贯性
4. 保持情节发展的逻辑性
5. 维持原有的写作风格和语调
6. 确保文字流畅自然

请直接输出改写后的内容，不要添加任何解释或说明：`;

  return prompt;
}

// 调用Gemini API进行文本改写 - 增强错误处理和重试机制
export async function rewriteText(request: RewriteRequest): Promise<RewriteResponse> {
  const startTime = Date.now();
  let lastError = '';
  let lastDetailedError = '';

  for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {
    try {
      const apiKey = keyManager.getBestAvailableKey();

      // 智能等待策略：如果key在冷却中，使用指数退避
      if (apiKey.cooldownUntil > Date.now()) {
        const cooldownWait = Math.min(apiKey.cooldownUntil - Date.now(), MAX_WAIT_TIME);
        const exponentialWait = Math.min(EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt), MAX_WAIT_TIME);
        const waitTime = Math.max(cooldownWait, exponentialWait);

        console.log(`等待 ${waitTime}ms (尝试 ${attempt + 1}/${MAX_RETRIES}, API Key: ${apiKey.name})`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }

      const prompt = buildPrompt(request);

      // 增加请求超时设置
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60秒超时

      const apiUrl = getGeminiApiUrl(request.model);
      const response = await fetch(`${apiUrl}?key=${apiKey.key}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.6,
            topK: 10,
            topP: 0.8,
            "thinkingConfig": {
              "thinkingBudget": 0
            }
          },
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      const processingTime = Date.now() - startTime;

      // 处理429错误（API限流）
      if (response.status === 429) {
        keyManager.recordUsage(apiKey.name, false);
        lastError = `API限流 (${apiKey.name})`;
        lastDetailedError = `第${attempt + 1}次尝试: API Key "${apiKey.name}" 遇到限流，状态码: 429`;

        if (attempt < MAX_RETRIES - 1) {
          const retryDelay = EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt);
          console.log(`API限流，${retryDelay}ms后重试...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          continue;
        }
      }

      // 处理其他HTTP错误
      if (!response.ok) {
        const errorData = await response.text();
        console.error('Gemini API error:', errorData);
        keyManager.recordUsage(apiKey.name, false);
        lastError = `API请求失败: ${response.status} ${response.statusText}`;
        lastDetailedError = `第${attempt + 1}次尝试: HTTP ${response.status} ${response.statusText}, 响应: ${errorData.substring(0, 200)}`;

        if (attempt < MAX_RETRIES - 1) {
          const retryDelay = REQUEST_DELAY * (attempt + 1);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          continue;
        }

        return {
          rewrittenText: '',
          success: false,
          error: lastError,
          apiKeyUsed: apiKey.name,
          processingTime,
          detailedError: lastDetailedError,
        };
      }

      // 解析响应数据
      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        keyManager.recordUsage(apiKey.name, false);
        lastError = 'JSON解析失败';
        lastDetailedError = `第${attempt + 1}次尝试: 无法解析API响应为JSON, 错误: ${parseError instanceof Error ? parseError.message : '未知错误'}`;

        if (attempt < MAX_RETRIES - 1) {
          const retryDelay = REQUEST_DELAY * (attempt + 1);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          continue;
        }

        return {
          rewrittenText: '',
          success: false,
          error: lastError,
          apiKeyUsed: apiKey.name,
          processingTime,
          detailedError: lastDetailedError,
          retryCount: attempt + 1,
        };
      }

      // 记录成功使用
      keyManager.recordUsage(apiKey.name, true);

      // 增强响应验证
      if (!data.candidates || data.candidates.length === 0) {
        lastError = '没有收到有效的响应内容';
        lastDetailedError = `第${attempt + 1}次尝试: API响应中没有candidates字段或为空数组, 完整响应: ${JSON.stringify(data).substring(0, 500)}`;

        if (attempt < MAX_RETRIES - 1) {
          keyManager.recordUsage(apiKey.name, false); // 标记为失败，触发冷却
          const retryDelay = EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          continue;
        }

        return {
          rewrittenText: '',
          success: false,
          error: lastError,
          apiKeyUsed: apiKey.name,
          processingTime,
          detailedError: lastDetailedError,
          retryCount: attempt + 1,
        };
      }

      const candidate = data.candidates[0];

      if (candidate.finishReason === 'SAFETY') {
        return {
          rewrittenText: '',
          success: false,
          error: '内容被安全过滤器拦截，请调整改写规则或原文内容',
          apiKeyUsed: apiKey.name,
          processingTime,
          detailedError: `内容被安全过滤器拦截，finishReason: SAFETY`,
          retryCount: attempt + 1,
        };
      }

      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
        lastError = '响应内容格式错误';
        lastDetailedError = `第${attempt + 1}次尝试: candidate内容格式错误, candidate: ${JSON.stringify(candidate).substring(0, 300)}`;

        if (attempt < MAX_RETRIES - 1) {
          keyManager.recordUsage(apiKey.name, false);
          const retryDelay = REQUEST_DELAY * (attempt + 1);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          continue;
        }

        return {
          rewrittenText: '',
          success: false,
          error: lastError,
          apiKeyUsed: apiKey.name,
          processingTime,
          detailedError: lastDetailedError,
          retryCount: attempt + 1,
        };
      }

      const rewrittenText = candidate.content.parts[0].text;

      // 验证生成的内容质量
      if (!rewrittenText || rewrittenText.trim().length < 10) {
        lastError = '生成的内容过短或为空';
        lastDetailedError = `第${attempt + 1}次尝试: 生成的内容长度: ${rewrittenText?.length || 0}, 内容: "${rewrittenText?.substring(0, 100) || 'null'}"`;

        if (attempt < MAX_RETRIES - 1) {
          keyManager.recordUsage(apiKey.name, false);
          const retryDelay = REQUEST_DELAY * (attempt + 1);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          continue;
        }

        return {
          rewrittenText: '',
          success: false,
          error: lastError,
          apiKeyUsed: apiKey.name,
          processingTime,
          detailedError: lastDetailedError,
          retryCount: attempt + 1,
        };
      }

      // 尝试从响应中提取token使用信息
      const tokensUsed = data.usageMetadata?.totalTokenCount || 0;

      return {
        rewrittenText: rewrittenText.trim(),
        success: true,
        apiKeyUsed: apiKey.name,
        tokensUsed,
        model: request.model || 'gemini-2.5-flash-lite',
        processingTime,
        retryCount: attempt + 1,
      };

    } catch (error) {
      console.error('Gemini API调用错误:', error);

      // 处理不同类型的错误
      if (error instanceof Error && error.name === 'AbortError') {
        lastError = '请求超时';
        lastDetailedError = `第${attempt + 1}次尝试: 请求超时 (60秒)`;
      } else {
        lastError = `网络错误: ${error instanceof Error ? error.message : '未知错误'}`;
        lastDetailedError = `第${attempt + 1}次尝试: ${error instanceof Error ? error.stack || error.message : '未知网络错误'}`;
      }

      if (attempt < MAX_RETRIES - 1) {
        const retryDelay = EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt);
        console.log(`网络错误，${retryDelay}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }

  return {
    rewrittenText: '',
    success: false,
    error: `重试${MAX_RETRIES}次后仍然失败: ${lastError}`,
    processingTime: Date.now() - startTime,
    detailedError: lastDetailedError,
    retryCount: MAX_RETRIES,
  };
}

// 改进的批量改写函数 - 支持实时写入、详细进度跟踪和失败恢复
export async function rewriteChapters(
  chapters: Array<{ content: string; title: string; number: number }>,
  rules: string,
  onProgress?: (progress: number, currentChapter: number, details?: any) => void,
  onChapterComplete?: (chapterIndex: number, result: any) => void,
  concurrency: number = 3, // 降低并发数以避免429错误
  model: string = 'gemini-2.5-flash-lite', // 模型选择
  enableFailureRecovery: boolean = true // 启用失败恢复机制
): Promise<Array<{ success: boolean; content: string; error?: string; details?: any }>> {
  const results: Array<{ success: boolean; content: string; error?: string; details?: any }> = new Array(chapters.length);
  let completed = 0;
  let totalTokensUsed = 0;
  const startTime = Date.now();

  // 使用更保守的并发策略
  const semaphore = new Semaphore(concurrency);

  const processChapter = async (chapter: { content: string; title: string; number: number }, index: number) => {
    await semaphore.acquire();
    const chapterStartTime = Date.now();

    try {

      const result = await rewriteText({
        originalText: chapter.content,
        rules,
        chapterTitle: chapter.title,
        chapterNumber: chapter.number,
        model,
      });

      const chapterProcessingTime = Date.now() - chapterStartTime;

      if (result.tokensUsed) {
        totalTokensUsed += result.tokensUsed;
      }

      const chapterResult = {
        success: result.success,
        content: result.rewrittenText,
        error: result.error,
        details: {
          apiKeyUsed: result.apiKeyUsed,
          tokensUsed: result.tokensUsed,
          model: result.model,
          processingTime: chapterProcessingTime,
          chapterNumber: chapter.number,
          chapterTitle: chapter.title,
        }
      };

      results[index] = chapterResult;
      completed++;

      // 实时回调章节完成
      if (onChapterComplete) {
        onChapterComplete(index, chapterResult);
      }

      // 更新进度，包含详细信息
      if (onProgress) {
        const progressDetails = {
          completed,
          total: chapters.length,
          totalTokensUsed,
          totalTime: Date.now() - startTime,
          averageTimePerChapter: (Date.now() - startTime) / completed,
          apiKeyStats: keyManager.getStats(),
          currentChapter: {
            number: chapter.number,
            title: chapter.title,
            processingTime: chapterProcessingTime,
            apiKey: result.apiKeyUsed,
            tokens: result.tokensUsed,
          }
        };

        onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);
      }

      // 添加请求间隔
      await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));

      return result;
    } catch (error) {
      const chapterErrorTime = Date.now();
      const errorResult = {
        success: false,
        content: '',
        error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,
        details: {
          chapterNumber: chapter.number,
          chapterTitle: chapter.title,
          processingTime: chapterErrorTime - chapterStartTime,
        }
      };

      results[index] = errorResult;
      completed++;

      if (onChapterComplete) {
        onChapterComplete(index, errorResult);
      }

      if (onProgress) {
        const progressDetails = {
          completed,
          total: chapters.length,
          totalTokensUsed,
          totalTime: Date.now() - startTime,
          averageTimePerChapter: (Date.now() - startTime) / completed,
          apiKeyStats: keyManager.getStats(),
          currentChapter: {
            number: chapter.number,
            title: chapter.title,
            error: error instanceof Error ? error.message : '未知错误',
          }
        };

        onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);
      }

      return null;
    } finally {
      semaphore.release();
    }
  };

  // 并发处理所有章节
  const promises = chapters.map((chapter, index) => processChapter(chapter, index));
  await Promise.all(promises);

  // 失败恢复机制：对失败的章节进行额外重试
  if (enableFailureRecovery) {
    const failedChapters = results
      .map((result, index) => ({ result, index, chapter: chapters[index] }))
      .filter(item => !item.result.success);

    if (failedChapters.length > 0) {
      console.log(`开始恢复 ${failedChapters.length} 个失败的章节...`);

      // 为失败恢复使用更保守的设置
      const recoverySemaphore = new Semaphore(1); // 串行处理失败的章节

      for (const { index, chapter } of failedChapters) {
        await recoverySemaphore.acquire();

        try {
          console.log(`正在恢复第 ${chapter.number} 章: ${chapter.title}`);

          // 等待更长时间再重试
          await new Promise(resolve => setTimeout(resolve, 5000));

          const recoveryResult = await rewriteText({
            originalText: chapter.content,
            rules,
            chapterTitle: chapter.title,
            chapterNumber: chapter.number,
            model,
          });

          if (recoveryResult.success) {
            console.log(`成功恢复第 ${chapter.number} 章`);

            const recoveredChapterResult = {
              success: true,
              content: recoveryResult.rewrittenText,
              error: undefined,
              details: {
                ...recoveryResult,
                chapterNumber: chapter.number,
                chapterTitle: chapter.title,
                isRecovered: true, // 标记为恢复的章节
              }
            };

            results[index] = recoveredChapterResult;
            completed++;

            // 通知章节恢复完成
            if (onChapterComplete) {
              onChapterComplete(index, recoveredChapterResult);
            }

            // 更新进度
            if (onProgress) {
              const progressDetails = {
                completed,
                total: chapters.length,
                totalTokensUsed: totalTokensUsed + (recoveryResult.tokensUsed || 0),
                totalTime: Date.now() - startTime,
                averageTimePerChapter: (Date.now() - startTime) / completed,
                apiKeyStats: keyManager.getStats(),
                currentChapter: {
                  number: chapter.number,
                  title: chapter.title,
                  processingTime: recoveryResult.processingTime,
                  apiKey: recoveryResult.apiKeyUsed,
                  tokens: recoveryResult.tokensUsed,
                  isRecovered: true,
                }
              };

              onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);
            }
          } else {
            console.log(`第 ${chapter.number} 章恢复失败: ${recoveryResult.error}`);
            // 更新失败信息，包含恢复尝试的详细信息
            results[index] = {
              ...results[index],
              error: `原始失败: ${results[index].error}; 恢复失败: ${recoveryResult.error}`,
              details: {
                ...results[index].details,
                recoveryAttempted: true,
                recoveryError: recoveryResult.error,
                recoveryDetailedError: recoveryResult.detailedError,
              }
            };
          }
        } catch (error) {
          console.error(`恢复第 ${chapter.number} 章时发生异常:`, error);
          results[index] = {
            ...results[index],
            error: `${results[index].error}; 恢复异常: ${error instanceof Error ? error.message : '未知错误'}`,
            details: {
              ...results[index].details,
              recoveryAttempted: true,
              recoveryException: error instanceof Error ? error.message : '未知错误',
            }
          };
        } finally {
          recoverySemaphore.release();
        }
      }
    }
  }

  return results;
}

// 信号量类，用于控制并发
class Semaphore {
  private permits: number;
  private waitQueue: Array<() => void> = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--;
      return Promise.resolve();
    }

    return new Promise<void>((resolve) => {
      this.waitQueue.push(resolve);
    });
  }

  release(): void {
    this.permits++;
    if (this.waitQueue.length > 0) {
      const resolve = this.waitQueue.shift();
      if (resolve) {
        this.permits--;
        resolve();
      }
    }
  }
}

// 测试API连接 - 增强版
export async function testGeminiConnection(): Promise<{
  success: boolean;
  error?: string;
  details?: any;
}> {
  try {
    const testResult = await rewriteText({
      originalText: '这是一个测试文本。',
      rules: '保持原文不变',
    });

    return {
      success: testResult.success,
      error: testResult.error,
      details: {
        apiKeyUsed: testResult.apiKeyUsed,
        tokensUsed: testResult.tokensUsed,
        model: testResult.model,
        processingTime: testResult.processingTime,
        apiKeyStats: keyManager.getStats(),
      }
    };
  } catch (error) {
    return {
      success: false,
      error: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,
      details: {
        apiKeyStats: keyManager.getStats(),
      }
    };
  }
}

// 获取API Key使用统计
export function getApiKeyStats() {
  return keyManager.getStats();
}

// 重置API Key统计
export function resetApiKeyStats() {
  API_KEYS.forEach(key => {
    key.requestCount = 0;
    key.lastUsed = 0;
    key.cooldownUntil = 0;
  });
}

// 预设的改写规则模板
export let PRESET_RULES: Record<string, { name: string; description: string; rules: string }> = {
  romance_focus: {
    name: '感情戏增强',
    description: '扩写男女主互动内容，对非感情戏部分一笔带过',
    rules: `请按照以下规则改写：
1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节
2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过
3. 增加角色间的情感张力和暧昧氛围
4. 保持故事主线不变，但重点突出感情发展`
  },

  character_fix: {
    name: '人设修正',
    description: '修正主角人设和对话风格',
    rules: `请按照以下规则改写：
1. 修正主角的性格设定，使其更加立体和讨喜
2. 改善对话风格，使其更加自然流畅
3. 去除过于中二或不合理的行为描写
4. 保持角色的核心特征，但优化表现方式`
  },

  toxic_content_removal: {
    name: '毒点清除',
    description: '移除送女、绿帽等毒点情节',
    rules: `请按照以下规则改写：
1. 完全移除或修改送女、绿帽、圣母等毒点情节
2. 删除或改写让读者不适的桥段
3. 保持故事逻辑的完整性
4. 用更合理的情节替代被删除的内容`
  },

  pacing_improvement: {
    name: '节奏优化',
    description: '优化故事节奏，删除拖沓内容',
    rules: `请按照以下规则改写：
1. 删除重复和拖沓的描写
2. 加快故事节奏，突出重点情节
3. 简化过于冗长的对话和心理描写
4. 保持故事的紧凑性和可读性`
  },

  custom: {
    name: '自定义规则',
    description: '用户自定义的改写规则',
    rules: ''
  }
};

// 从数据库加载自定义预设并合并到 PRESET_RULES（仅在服务端使用）
export function loadCustomPresets() {
  // 这个函数只在服务端使用，客户端组件应该通过 API 获取预设
  if (typeof window !== 'undefined') {
    console.warn('loadCustomPresets should not be called on client side');
    return;
  }

  try {
    const { presetDb } = require('@/lib/database');
    const customPresets = presetDb.getAll();

    // 将数据库中的预设添加到 PRESET_RULES
    customPresets.forEach((preset: any) => {
      PRESET_RULES[`custom_${preset.id}`] = {
        name: preset.name,
        description: preset.description,
        rules: preset.rules
      };
    });
  } catch (error) {
    console.error('加载自定义预设失败:', error);
  }
}

// 添加自定义预设规则（保持向后兼容）
export function addCustomPreset(name: string, description: string, rules: string): string {
  const key = `custom_${Date.now()}`;
  PRESET_RULES = {
    ...PRESET_RULES,
    [key]: {
      name,
      description,
      rules
    }
  };
  return key;
}

// 带上下文的重写函数（仅服务端使用）
export async function rewriteTextWithContext(
  novelId: string,
  chapterNumber: number,
  originalText: string,
  rules: string,
  chapterTitle?: string,
  model?: string
): Promise<RewriteResponse> {
  // 检查是否在服务端环境
  if (typeof window !== 'undefined') {
    console.warn('rewriteTextWithContext should only be used on server side');
    // 在客户端环境下回退到普通重写
    return await rewriteText({
      originalText,
      rules,
      chapterTitle,
      chapterNumber,
      model
    });
  }

  try {
    // 动态导入避免循环依赖，只在服务端执行
    const { novelContextDb, chapterContextDb } = require('./database');

    // 获取小说整体上下文
    const novelContext = novelContextDb.getByNovelId(novelId);

    // 获取章节上下文
    const chapterContext = chapterContextDb.getByChapter(novelId, chapterNumber);

    // 构建请求
    const request: RewriteRequest = {
      originalText,
      rules,
      chapterTitle,
      chapterNumber,
      model,
      novelContext: novelContext ? {
        summary: novelContext.summary,
        mainCharacters: novelContext.mainCharacters,
        worldSetting: novelContext.worldSetting,
        writingStyle: novelContext.writingStyle,
        tone: novelContext.tone
      } : undefined,
      chapterContext: chapterContext ? {
        previousChapterSummary: chapterContext.previousChapterSummary,
        keyEvents: chapterContext.keyEvents,
        characterStates: chapterContext.characterStates,
        plotProgress: chapterContext.plotProgress,
        contextualNotes: chapterContext.contextualNotes
      } : undefined
    };

    return await rewriteText(request);
  } catch (error) {
    console.error('带上下文重写失败:', error);
    // 如果获取上下文失败，回退到普通重写
    return await rewriteText({
      originalText,
      rules,
      chapterTitle,
      chapterNumber,
      model
    });
  }
}
