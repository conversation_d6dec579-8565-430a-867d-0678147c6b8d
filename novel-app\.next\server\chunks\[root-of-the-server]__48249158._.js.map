{"version": 3, "sources": ["turbopack:///[project]/src/lib/database.ts", "turbopack:///[project]/src/lib/file-manager.ts", "turbopack:///[project]/src/lib/novel-parser.ts", "turbopack:///[project]/node_modules/next/dist/esm/build/templates/app-route.js", "turbopack:///[project]/src/app/api/novels/route.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport crypto from 'crypto';\n\n// 数据类型定义\nexport interface Novel {\n  id: string;\n  title: string;\n  filename: string;\n  createdAt: string;\n  chapterCount?: number;\n}\n\nexport interface Chapter {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  title: string;\n  content: string;\n  filename: string;\n  createdAt: string;\n}\n\nexport interface RewriteRule {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Character {\n  id: string;\n  novelId: string;\n  name: string;\n  role: string; // 角色类型：男主、女主、配角、反派、其他\n  description: string;\n  personality?: string; // 性格特点\n  appearance?: string; // 外貌描述\n  relationships?: string; // 人物关系\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 小说整体上下文\nexport interface NovelContext {\n  id: string;\n  novelId: string;\n  summary: string; // 小说整体摘要\n  mainCharacters: Array<{\n    name: string;\n    role: string;\n    description: string;\n    relationships?: string;\n  }>; // 主要人物信息\n  worldSetting: string; // 世界观设定\n  writingStyle: string; // 写作风格特征\n  mainPlotlines: string[]; // 主要情节线\n  themes: string[]; // 主题\n  tone: string; // 语调风格\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 章节上下文\nexport interface ChapterContext {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  keyEvents: string[]; // 关键事件\n  characterStates: Array<{\n    name: string;\n    status: string; // 人物在本章的状态\n    emotions: string; // 情感状态\n    relationships: string; // 关系变化\n  }>; // 人物状态\n  plotProgress: string; // 情节推进要点\n  previousChapterSummary?: string; // 前一章摘要\n  nextChapterHints?: string; // 对下一章的暗示\n  contextualNotes: string; // 上下文注释\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Preset {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface RewriteJob {\n  id: string;\n  novelId: string;\n  chapters: number[];\n  ruleId: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n  // 新增详细信息字段\n  details?: {\n    totalChapters: number;\n    completedChapters: number;\n    failedChapters: number;\n    totalTokensUsed: number;\n    totalProcessingTime: number;\n    averageTimePerChapter: number;\n    apiKeyStats: Array<{\n      name: string;\n      requestCount: number;\n      weight: number;\n      isAvailable: boolean;\n    }>;\n    chapterResults: Array<{\n      chapterNumber: number;\n      chapterTitle: string;\n      success: boolean;\n      error?: string;\n      apiKeyUsed?: string;\n      tokensUsed?: number;\n      processingTime?: number;\n      completedAt?: string;\n    }>;\n    model?: string;\n    concurrency?: number;\n  };\n}\n\n// 数据存储路径\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst NOVELS_FILE = path.join(DATA_DIR, 'novels.json');\nconst CHAPTERS_FILE = path.join(DATA_DIR, 'chapters.json');\nconst RULES_FILE = path.join(DATA_DIR, 'rewrite_rules.json');\nconst JOBS_FILE = path.join(DATA_DIR, 'rewrite_jobs.json');\nconst CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');\nconst PRESETS_FILE = path.join(DATA_DIR, 'presets.json');\nconst NOVEL_CONTEXTS_FILE = path.join(DATA_DIR, 'novel-contexts.json');\nconst CHAPTER_CONTEXTS_FILE = path.join(DATA_DIR, 'chapter-contexts.json');\n\n// 确保数据目录存在\nfunction ensureDataDir() {\n  if (!fs.existsSync(DATA_DIR)) {\n    fs.mkdirSync(DATA_DIR, { recursive: true });\n  }\n}\n\n// 读取JSON文件\nfunction readJsonFile<T>(filePath: string): T[] {\n  ensureDataDir();\n  if (!fs.existsSync(filePath)) {\n    return [];\n  }\n  try {\n    const data = fs.readFileSync(filePath, 'utf-8');\n    return JSON.parse(data);\n  } catch (error) {\n    console.error(`Error reading ${filePath}:`, error);\n    return [];\n  }\n}\n\n// 写入JSON文件\nfunction writeJsonFile<T>(filePath: string, data: T[]) {\n  ensureDataDir();\n  try {\n    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');\n  } catch (error) {\n    console.error(`Error writing ${filePath}:`, error);\n    throw error;\n  }\n}\n\n// 生成唯一ID\nfunction generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substring(2);\n}\n\n// 基于内容生成确定性ID\nfunction generateDeterministicId(content: string): string {\n  return crypto.createHash('md5').update(content).digest('hex').substring(0, 18);\n}\n\n// 小说相关操作\nexport const novelDb = {\n  getAll: (): Novel[] => readJsonFile<Novel>(NOVELS_FILE),\n\n  getById: (id: string): Novel | undefined => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    return novels.find(novel => novel.id === id);\n  },\n\n  create: (novel: Omit<Novel, 'id' | 'createdAt'>): Novel => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n\n    // 使用书名生成确定性ID\n    const novelId = generateDeterministicId(novel.title);\n\n    // 检查是否已存在相同ID的小说\n    const existingNovel = novels.find(n => n.id === novelId);\n    if (existingNovel) {\n      // 如果已存在，更新现有记录\n      existingNovel.filename = novel.filename;\n      existingNovel.chapterCount = novel.chapterCount;\n      writeJsonFile(NOVELS_FILE, novels);\n      return existingNovel;\n    }\n\n    const newNovel: Novel = {\n      ...novel,\n      id: novelId,\n      createdAt: new Date().toISOString(),\n    };\n    novels.push(newNovel);\n    writeJsonFile(NOVELS_FILE, novels);\n    return newNovel;\n  },\n\n  update: (id: string, updates: Partial<Novel>): Novel | null => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return null;\n\n    novels[index] = { ...novels[index], ...updates };\n    writeJsonFile(NOVELS_FILE, novels);\n    return novels[index];\n  },\n\n  delete: (id: string): boolean => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return false;\n\n    novels.splice(index, 1);\n    writeJsonFile(NOVELS_FILE, novels);\n    return true;\n  }\n};\n\n// 章节相关操作\nexport const chapterDb = {\n  getAll: (): Chapter[] => readJsonFile<Chapter>(CHAPTERS_FILE),\n\n  getByNovelId: (novelId: string): Chapter[] => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.filter(chapter => chapter.novelId === novelId);\n  },\n\n  getById: (id: string): Chapter | undefined => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.find(chapter => chapter.id === id);\n  },\n\n  create: (chapter: Omit<Chapter, 'id' | 'createdAt'>): Chapter => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapter: Chapter = {\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    chapters.push(newChapter);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return newChapter;\n  },\n\n  createBatch: (chapters: Omit<Chapter, 'id' | 'createdAt'>[]): Chapter[] => {\n    const existingChapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapters = chapters.map(chapter => ({\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    }));\n    existingChapters.push(...newChapters);\n    writeJsonFile(CHAPTERS_FILE, existingChapters);\n    return newChapters;\n  },\n\n  delete: (id: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const index = chapters.findIndex(chapter => chapter.id === id);\n    if (index === -1) return false;\n\n    chapters.splice(index, 1);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const filteredChapters = chapters.filter(chapter => chapter.novelId !== novelId);\n    writeJsonFile(CHAPTERS_FILE, filteredChapters);\n    return true;\n  }\n};\n\n// 改写规则相关操作\nexport const ruleDb = {\n  getAll: (): RewriteRule[] => readJsonFile<RewriteRule>(RULES_FILE),\n\n  getById: (id: string): RewriteRule | undefined => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    return rules.find(rule => rule.id === id);\n  },\n\n  create: (rule: Omit<RewriteRule, 'id' | 'createdAt' | 'updatedAt'>): RewriteRule => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const newRule: RewriteRule = {\n      ...rule,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    rules.push(newRule);\n    writeJsonFile(RULES_FILE, rules);\n    return newRule;\n  },\n\n  update: (id: string, updates: Partial<RewriteRule>): RewriteRule | null => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return null;\n\n    rules[index] = {\n      ...rules[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(RULES_FILE, rules);\n    return rules[index];\n  },\n\n  delete: (id: string): boolean => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return false;\n\n    rules.splice(index, 1);\n    writeJsonFile(RULES_FILE, rules);\n    return true;\n  }\n};\n\n// 改写任务相关操作\nexport const jobDb = {\n  getAll: (): RewriteJob[] => readJsonFile<RewriteJob>(JOBS_FILE),\n\n  getById: (id: string): RewriteJob | undefined => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    return jobs.find(job => job.id === id);\n  },\n\n  create: (job: Omit<RewriteJob, 'id' | 'createdAt' | 'updatedAt'>): RewriteJob => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const newJob: RewriteJob = {\n      ...job,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    jobs.push(newJob);\n    writeJsonFile(JOBS_FILE, jobs);\n    return newJob;\n  },\n\n  update: (id: string, updates: Partial<RewriteJob>): RewriteJob | null => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return null;\n\n    jobs[index] = {\n      ...jobs[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(JOBS_FILE, jobs);\n    return jobs[index];\n  },\n\n  delete: (id: string): boolean => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return false;\n\n    jobs.splice(index, 1);\n    writeJsonFile(JOBS_FILE, jobs);\n    return true;\n  }\n};\n\n// 人物设定相关操作\nexport const characterDb = {\n  getAll: (): Character[] => readJsonFile<Character>(CHARACTERS_FILE),\n\n  getByNovelId: (novelId: string): Character[] => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.filter(character => character.novelId === novelId);\n  },\n\n  getById: (id: string): Character | undefined => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.find(character => character.id === id);\n  },\n\n  create: (character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>): Character => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const newCharacter: Character = {\n      ...character,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    characters.push(newCharacter);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return newCharacter;\n  },\n\n  update: (id: string, updates: Partial<Character>): Character | null => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return null;\n\n    characters[index] = {\n      ...characters[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return characters[index];\n  },\n\n  delete: (id: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return false;\n\n    characters.splice(index, 1);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const filteredCharacters = characters.filter(character => character.novelId !== novelId);\n    writeJsonFile(CHARACTERS_FILE, filteredCharacters);\n    return true;\n  }\n};\n\n// 预设相关操作\nexport const presetDb = {\n  getAll: (): Preset[] => readJsonFile<Preset>(PRESETS_FILE),\n\n  getById: (id: string): Preset | undefined => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    return presets.find(preset => preset.id === id);\n  },\n\n  create: (preset: Omit<Preset, 'id' | 'createdAt' | 'updatedAt'>): Preset => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const newPreset: Preset = {\n      ...preset,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    presets.push(newPreset);\n    writeJsonFile(PRESETS_FILE, presets);\n    return newPreset;\n  },\n\n  update: (id: string, updates: Partial<Preset>): Preset | null => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const index = presets.findIndex(preset => preset.id === id);\n    if (index === -1) return null;\n\n    presets[index] = {\n      ...presets[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(PRESETS_FILE, presets);\n    return presets[index];\n  },\n\n  delete: (id: string): boolean => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const index = presets.findIndex(preset => preset.id === id);\n    if (index === -1) return false;\n\n    presets.splice(index, 1);\n    writeJsonFile(PRESETS_FILE, presets);\n    return true;\n  }\n};\n\n// 小说上下文相关操作\nexport const novelContextDb = {\n  getAll: (): NovelContext[] => readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE),\n\n  getByNovelId: (novelId: string): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    return contexts.find(context => context.novelId === novelId);\n  },\n\n  getById: (id: string): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    return contexts.find(context => context.id === id);\n  },\n\n  create: (context: Omit<NovelContext, 'id' | 'createdAt' | 'updatedAt'>): NovelContext => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const newContext: NovelContext = {\n      ...context,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    contexts.push(newContext);\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return newContext;\n  },\n\n  update: (id: string, updates: Partial<Omit<NovelContext, 'id' | 'createdAt'>>): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return undefined;\n\n    contexts[index] = {\n      ...contexts[index],\n      ...updates,\n      updatedAt: new Date().toISOString(),\n    };\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return contexts[index];\n  },\n\n  delete: (id: string): boolean => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return false;\n\n    contexts.splice(index, 1);\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return true;\n  }\n};\n\n// 章节上下文相关操作\nexport const chapterContextDb = {\n  getAll: (): ChapterContext[] => readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE),\n\n  getByNovelId: (novelId: string): ChapterContext[] => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.filter(context => context.novelId === novelId);\n  },\n\n  getByChapter: (novelId: string, chapterNumber: number): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.find(context =>\n      context.novelId === novelId && context.chapterNumber === chapterNumber\n    );\n  },\n\n  getById: (id: string): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.find(context => context.id === id);\n  },\n\n  create: (context: Omit<ChapterContext, 'id' | 'createdAt' | 'updatedAt'>): ChapterContext => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const newContext: ChapterContext = {\n      ...context,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    contexts.push(newContext);\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return newContext;\n  },\n\n  update: (id: string, updates: Partial<Omit<ChapterContext, 'id' | 'createdAt'>>): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return undefined;\n\n    contexts[index] = {\n      ...contexts[index],\n      ...updates,\n      updatedAt: new Date().toISOString(),\n    };\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return contexts[index];\n  },\n\n  delete: (id: string): boolean => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return false;\n\n    contexts.splice(index, 1);\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return true;\n  },\n\n  // 获取章节的上下文窗口（前后几章的上下文）\n  getContextWindow: (novelId: string, chapterNumber: number, windowSize: number = 2): ChapterContext[] => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const novelContexts = contexts.filter(context => context.novelId === novelId);\n\n    const startChapter = Math.max(1, chapterNumber - windowSize);\n    const endChapter = chapterNumber + windowSize;\n\n    return novelContexts.filter(context =>\n      context.chapterNumber >= startChapter && context.chapterNumber <= endChapter\n    ).sort((a, b) => a.chapterNumber - b.chapterNumber);\n  }\n};\n", "import fs from 'fs';\nimport path from 'path';\n\n// 文件管理工具类\nexport class FileManager {\n  private static instance: FileManager;\n  private baseDir: string;\n\n  private constructor() {\n    this.baseDir = process.cwd();\n  }\n\n  public static getInstance(): FileManager {\n    if (!FileManager.instance) {\n      FileManager.instance = new FileManager();\n    }\n    return FileManager.instance;\n  }\n\n  // 确保目录存在\n  public ensureDir(dirPath: string): void {\n    if (!fs.existsSync(dirPath)) {\n      fs.mkdirSync(dirPath, { recursive: true });\n    }\n  }\n\n  // 获取novels目录路径\n  public getNovelsDir(): string {\n    return path.join(this.baseDir, '..', 'novels');\n  }\n\n  // 获取chapters目录路径\n  public getChaptersDir(): string {\n    return path.join(this.baseDir, '..', 'chapters');\n  }\n\n  // 获取数据目录路径\n  public getDataDir(): string {\n    const dataDir = path.join(this.baseDir, 'data');\n    this.ensureDir(dataDir);\n    return dataDir;\n  }\n\n  // 获取改写结果目录路径\n  public getRewrittenDir(): string {\n    const rewrittenDir = path.join(this.getDataDir(), 'rewritten');\n    this.ensureDir(rewrittenDir);\n    return rewrittenDir;\n  }\n\n  // 获取特定小说的改写结果目录\n  public getNovelRewrittenDir(novelTitle: string): string {\n    const novelDir = path.join(this.getRewrittenDir(), this.sanitizeFilename(novelTitle));\n    this.ensureDir(novelDir);\n    return novelDir;\n  }\n\n  // 获取完成小说目录路径\n  public getDoneNovelsDir(): string {\n    const doneNovelsDir = path.join(this.baseDir, '..', 'done-novels');\n    this.ensureDir(doneNovelsDir);\n    return doneNovelsDir;\n  }\n\n  // 获取特定小说的章节目录\n  public getNovelChaptersDir(novelTitle: string): string {\n    const chaptersDir = this.getChaptersDir();\n    this.ensureDir(chaptersDir);\n    const novelDir = path.join(chaptersDir, this.sanitizeFilename(novelTitle));\n    this.ensureDir(novelDir);\n    return novelDir;\n  }\n\n  // 清理文件名中的非法字符\n  public sanitizeFilename(filename: string): string {\n    return filename.replace(/[<>:\"/\\\\|?*]/g, '_').trim();\n  }\n\n  // 读取文件内容\n  public readFile(filePath: string): string {\n    try {\n      return fs.readFileSync(filePath, 'utf-8');\n    } catch (error) {\n      console.error(`读取文件失败: ${filePath}`, error);\n      throw error;\n    }\n  }\n\n  // 写入文件内容\n  public writeFile(filePath: string, content: string): void {\n    try {\n      const dir = path.dirname(filePath);\n      this.ensureDir(dir);\n      fs.writeFileSync(filePath, content, 'utf-8');\n    } catch (error) {\n      console.error(`写入文件失败: ${filePath}`, error);\n      throw error;\n    }\n  }\n\n  // 检查文件是否存在\n  public fileExists(filePath: string): boolean {\n    return fs.existsSync(filePath);\n  }\n\n  // 获取目录中的所有文件\n  public listFiles(dirPath: string, extensions?: string[]): string[] {\n    try {\n      if (!fs.existsSync(dirPath)) {\n        return [];\n      }\n\n      const files = fs.readdirSync(dirPath);\n\n      if (extensions) {\n        return files.filter(file => {\n          const ext = path.extname(file).toLowerCase();\n          return extensions.includes(ext);\n        });\n      }\n\n      return files;\n    } catch (error) {\n      console.error(`读取目录失败: ${dirPath}`, error);\n      return [];\n    }\n  }\n\n  // 获取文件信息\n  public getFileStats(filePath: string): fs.Stats | null {\n    try {\n      return fs.statSync(filePath);\n    } catch (error) {\n      console.error(`获取文件信息失败: ${filePath}`, error);\n      return null;\n    }\n  }\n\n  // 删除文件\n  public deleteFile(filePath: string): boolean {\n    try {\n      if (fs.existsSync(filePath)) {\n        fs.unlinkSync(filePath);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error(`删除文件失败: ${filePath}`, error);\n      return false;\n    }\n  }\n\n  // 删除目录\n  public deleteDir(dirPath: string): boolean {\n    try {\n      if (fs.existsSync(dirPath)) {\n        fs.rmSync(dirPath, { recursive: true, force: true });\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error(`删除目录失败: ${dirPath}`, error);\n      return false;\n    }\n  }\n\n  // 复制文件\n  public copyFile(srcPath: string, destPath: string): boolean {\n    try {\n      const destDir = path.dirname(destPath);\n      this.ensureDir(destDir);\n      fs.copyFileSync(srcPath, destPath);\n      return true;\n    } catch (error) {\n      console.error(`复制文件失败: ${srcPath} -> ${destPath}`, error);\n      return false;\n    }\n  }\n\n  // 移动文件\n  public moveFile(srcPath: string, destPath: string): boolean {\n    try {\n      const destDir = path.dirname(destPath);\n      this.ensureDir(destDir);\n      fs.renameSync(srcPath, destPath);\n      return true;\n    } catch (error) {\n      console.error(`移动文件失败: ${srcPath} -> ${destPath}`, error);\n      return false;\n    }\n  }\n\n  // 获取目录大小\n  public getDirSize(dirPath: string): number {\n    let totalSize = 0;\n\n    try {\n      if (!fs.existsSync(dirPath)) {\n        return 0;\n      }\n\n      const files = fs.readdirSync(dirPath);\n\n      for (const file of files) {\n        const filePath = path.join(dirPath, file);\n        const stats = fs.statSync(filePath);\n\n        if (stats.isDirectory()) {\n          totalSize += this.getDirSize(filePath);\n        } else {\n          totalSize += stats.size;\n        }\n      }\n    } catch (error) {\n      console.error(`计算目录大小失败: ${dirPath}`, error);\n    }\n\n    return totalSize;\n  }\n\n  // 格式化文件大小\n  public formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 B';\n\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  // 创建备份\n  public createBackup(filePath: string): string | null {\n    try {\n      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n      const ext = path.extname(filePath);\n      const baseName = path.basename(filePath, ext);\n      const dir = path.dirname(filePath);\n\n      const backupPath = path.join(dir, `${baseName}_backup_${timestamp}${ext}`);\n\n      if (this.copyFile(filePath, backupPath)) {\n        return backupPath;\n      }\n\n      return null;\n    } catch (error) {\n      console.error(`创建备份失败: ${filePath}`, error);\n      return null;\n    }\n  }\n\n  // 合并改写的章节为一个完整的小说文件\n  public mergeRewrittenChapters(novelTitle: string): { success: boolean; filePath?: string; error?: string } {\n    try {\n      const rewrittenDir = this.getNovelRewrittenDir(novelTitle);\n      const doneNovelsDir = this.getDoneNovelsDir();\n\n      // 获取所有改写的章节文件\n      const chapterFiles = this.listFiles(rewrittenDir, ['.txt'])\n        .filter(file => file.startsWith('chapter_') && file.includes('_rewritten'))\n        .sort((a, b) => {\n          // 提取章节号进行排序\n          const aNum = parseInt(a.match(/chapter_(\\d+)/)?.[1] || '0');\n          const bNum = parseInt(b.match(/chapter_(\\d+)/)?.[1] || '0');\n          return aNum - bNum;\n        });\n\n      if (chapterFiles.length === 0) {\n        return { success: false, error: '没有找到改写的章节文件' };\n      }\n\n      // 读取并合并所有章节\n      let mergedContent = '';\n      for (const chapterFile of chapterFiles) {\n        const chapterPath = path.join(rewrittenDir, chapterFile);\n        const chapterContent = this.readFile(chapterPath);\n        mergedContent += chapterContent + '\\n\\n';\n      }\n\n      // 生成合并后的文件名\n      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);\n      const mergedFileName = `${this.sanitizeFilename(novelTitle)}_merged_${timestamp}.txt`;\n      const mergedFilePath = path.join(doneNovelsDir, mergedFileName);\n\n      // 写入合并后的文件\n      this.writeFile(mergedFilePath, mergedContent.trim());\n\n      return {\n        success: true,\n        filePath: mergedFilePath\n      };\n    } catch (error) {\n      console.error(`合并章节失败: ${novelTitle}`, error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '未知错误'\n      };\n    }\n  }\n\n  // 清理旧备份文件\n  public cleanupBackups(dirPath: string, maxBackups: number = 5): void {\n    try {\n      const files = this.listFiles(dirPath);\n      const backupFiles = files\n        .filter(file => file.includes('_backup_'))\n        .map(file => ({\n          name: file,\n          path: path.join(dirPath, file),\n          stats: this.getFileStats(path.join(dirPath, file))\n        }))\n        .filter(item => item.stats !== null)\n        .sort((a, b) => b.stats!.mtime.getTime() - a.stats!.mtime.getTime());\n\n      // 删除超出数量限制的备份文件\n      if (backupFiles.length > maxBackups) {\n        const filesToDelete = backupFiles.slice(maxBackups);\n        for (const file of filesToDelete) {\n          this.deleteFile(file.path);\n        }\n      }\n    } catch (error) {\n      console.error(`清理备份文件失败: ${dirPath}`, error);\n    }\n  }\n}\n\n// 导出单例实例\nexport const fileManager = FileManager.getInstance();\n", "import fs from 'fs';\nimport path from 'path';\nimport { novelDb, chapterDb, Novel, Chapter } from './database';\nimport { fileManager } from './file-manager';\n\n// 小说解析配置\ninterface ParseConfig {\n  chapterPattern: RegExp;\n  minChapterLength: number;\n  maxChapterLength: number;\n}\n\n// 分卷/分集匹配模式（用于分栏，不作为章节）\nconst VOLUME_PATTERNS = [\n  /^\\s*(?:第[一二三四五六七八九十百千万\\d]+[卷集部])\\s*.*$/gmi,\n  /^\\s*(?:[卷集部][一二三四五六七八九十百千万\\d]+)\\s*.*$/gmi,\n];\n\n// 章节匹配模式\nconst CHAPTER_PATTERNS = [\n  /^\\s*(?:第[一二三四五六七八九十百千万\\d]+[章节回])\\s*.*$/gmi,\n  /^\\s*(?:Chapter\\s+\\d+)\\s*.*$/gmi,\n];\n\n// 解析小说文件\nexport async function parseNovelFile(filePath: string): Promise<{\n  novel: Novel;\n  chapters: Chapter[];\n}> {\n  const filename = path.basename(filePath);\n  const title = filename.replace(/\\.(txt|md)$/i, '');\n\n  // 读取文件内容\n  const content = fs.readFileSync(filePath, 'utf-8');\n\n  // 创建小说记录\n  const novel = novelDb.create({\n    title,\n    filename,\n  });\n\n  // 解析章节\n  const chapters = parseChapters(content, novel.id);\n\n  // 批量创建章节记录\n  const createdChapters = chapterDb.createBatch(chapters);\n\n  // 更新小说的章节数量\n  novelDb.update(novel.id, { chapterCount: createdChapters.length });\n\n  // 保存章节文件\n  await saveChapterFiles(createdChapters);\n\n  return {\n    novel: { ...novel, chapterCount: createdChapters.length },\n    chapters: createdChapters,\n  };\n}\n\n// 解析章节内容\nfunction parseChapters(content: string, novelId: string): Omit<Chapter, 'id' | 'createdAt'>[] {\n  const chapters: Omit<Chapter, 'id' | 'createdAt'>[] = [];\n\n  // 首先识别分卷/分集标记\n  const volumeMatches = findVolumeMarkers(content);\n\n  // 然后在每个分卷内或整个文本中查找章节\n  if (volumeMatches.length > 0) {\n    // 有分卷的情况\n    console.log(`Found ${volumeMatches.length} volumes`);\n    let chapterNumber = 1;\n\n    for (let i = 0; i < volumeMatches.length; i++) {\n      const volumeStart = volumeMatches[i].index;\n      const volumeEnd = i + 1 < volumeMatches.length ? volumeMatches[i + 1].index : content.length;\n      const volumeContent = content.slice(volumeStart, volumeEnd);\n\n      // 在分卷内查找章节\n      const volumeChapters = parseChaptersInVolume(volumeContent, novelId, chapterNumber, volumeMatches[i].title);\n      chapters.push(...volumeChapters);\n      chapterNumber += volumeChapters.length;\n    }\n  } else {\n    // 没有分卷，直接解析章节\n    const directChapters = parseChaptersInVolume(content, novelId, 1);\n    chapters.push(...directChapters);\n  }\n\n  console.log(`Successfully parsed ${chapters.length} chapters`);\n  return chapters;\n}\n\n// 查找分卷标记\nfunction findVolumeMarkers(content: string): Array<{ index: number; title: string }> {\n  const volumeMarkers: Array<{ index: number; title: string }> = [];\n\n  for (const pattern of VOLUME_PATTERNS) {\n    const matches = Array.from(content.matchAll(pattern));\n    for (const match of matches) {\n      volumeMarkers.push({\n        index: match.index!,\n        title: extractChapterTitle(match[0])\n      });\n    }\n  }\n\n  // 按位置排序\n  return volumeMarkers.sort((a, b) => a.index - b.index);\n}\n\n// 在指定内容中解析章节（重构版）\nfunction parseChaptersInVolume(\n  content: string,\n  novelId: string,\n  startChapterNumber: number,\n  volumeTitle?: string\n): Omit<Chapter, 'id' | 'createdAt'>[] {\n  const chapters: Omit<Chapter, 'id' | 'createdAt'>[] = [];\n  let chapterNumber = startChapterNumber;\n\n  // 1. 寻找最佳匹配模式\n  let bestPattern: RegExp | null = null;\n  let bestMatchesCount = -1;\n\n  for (const pattern of CHAPTER_PATTERNS) {\n    const matches = content.match(pattern); // 使用 match 而不是 matchAll 来计数\n    const matchCount = matches ? matches.length : 0;\n    if (matchCount > bestMatchesCount) {\n      bestMatchesCount = matchCount;\n      bestPattern = pattern;\n    }\n  }\n\n  // 2. 如果没有找到任何章节标记，将整个内容作为一章\n  if (!bestPattern || bestMatchesCount === 0) {\n    const trimmedContent = content.trim();\n    if (trimmedContent.length > 100) { // 只有内容足够长才作为章节\n      chapters.push({\n        novelId,\n        chapterNumber: chapterNumber,\n        title: volumeTitle || '全文',\n        content: trimmedContent,\n        filename: `chapter_${chapterNumber}.txt`,\n      });\n    }\n    return chapters;\n  }\n\n  // 3. 使用 split 进行分割 (关键改动)\n  // 创建一个带捕获组的新正则表达式，以便 split 保留分隔符\n  const splitPattern = new RegExp(`(${bestPattern.source})`, 'gmi');\n  const parts = content.split(splitPattern);\n\n  // parts 数组的结构会是: [前言部分, 标题1, 内容1, 标题2, 内容2, ...]\n\n  let currentContent = '';\n\n  // 处理可能存在的前言/序章（parts[0]）\n  const prologue = parts[0]?.trim();\n  if (prologue && prologue.length > 100) {\n    chapters.push({\n      novelId,\n      chapterNumber: chapterNumber,\n      // 你可以给它一个固定的名字，或者尝试从内容中提取\n      title: '序章',\n      content: prologue,\n      filename: `chapter_${chapterNumber}.txt`,\n    });\n    chapterNumber++;\n  }\n  console.log(parts.map((v, i) => i + v));\n\n  // 4. 循环处理分割后的部分\n  for (let i = 1; i < parts.length; i += 2) {\n\n    const titlePart = parts[i];\n    const contentPart = parts[i + 1] || ''; // 后面的内容部分\n\n    if (!titlePart) continue;\n\n    const trimmedContent = (titlePart + contentPart).trim();\n\n    if (trimmedContent.length > 100) { // 检查章节总长度\n      chapters.push({\n        novelId,\n        chapterNumber: chapterNumber,\n        title: extractChapterTitle(titlePart), // 标题就是分割符本身\n        content: trimmedContent,\n        filename: `chapter_${chapterNumber}.txt`,\n      });\n      chapterNumber++;\n    }\n  }\n\n  return chapters;\n}\n// 提取章节标题\nfunction extractChapterTitle(chapterText: string): string {\n  const lines = chapterText.trim().split('\\n');\n  const firstLine = lines[0].trim();\n\n  // 如果第一行看起来像标题，使用它\n  if (firstLine.length < 100 && firstLine.length > 0) {\n    return firstLine;\n  }\n\n  // 否则尝试从前几行中找到标题\n  for (let i = 0; i < Math.min(3, lines.length); i++) {\n    const line = lines[i].trim();\n    if (line.length > 0 && line.length < 100) {\n      return line;\n    }\n  }\n\n  return '未命名章节';\n}\n\n// 保存章节文件到chapters目录\nasync function saveChapterFiles(chapters: Chapter[]): Promise<void> {\n  // 为每个小说创建子目录\n  const novelIds = [...new Set(chapters.map(ch => ch.novelId))];\n\n  for (const novelId of novelIds) {\n    const novel = novelDb.getById(novelId);\n    if (!novel) continue;\n\n    const novelDir = fileManager.getNovelChaptersDir(novel.title);\n\n    // 保存该小说的所有章节\n    const novelChapters = chapters.filter(ch => ch.novelId === novelId);\n    for (const chapter of novelChapters) {\n      const chapterPath = path.join(novelDir, chapter.filename);\n      fileManager.writeFile(chapterPath, chapter.content);\n    }\n  }\n}\n\n// 获取novels目录中的所有小说文件\nexport function getAvailableNovels(): string[] {\n  const novelsDir = fileManager.getNovelsDir();\n  return fileManager.listFiles(novelsDir, ['.txt', '.md']);\n}\n\n// 检查小说是否已经被解析\nexport function isNovelParsed(filename: string): boolean {\n  const novels = novelDb.getAll();\n  return novels.some(novel => novel.filename === filename);\n}\n\n// 重新解析小说（删除旧数据并重新解析）\nexport async function reparseNovel(filename: string): Promise<{\n  novel: Novel;\n  chapters: Chapter[];\n} | null> {\n  const novelsDir = fileManager.getNovelsDir();\n  const filePath = path.join(novelsDir, filename);\n\n  if (!fileManager.fileExists(filePath)) {\n    return null;\n  }\n\n  // 删除旧的小说和章节数据\n  const existingNovels = novelDb.getAll();\n  const existingNovel = existingNovels.find(novel => novel.filename === filename);\n\n  if (existingNovel) {\n    chapterDb.deleteByNovelId(existingNovel.id);\n    novelDb.delete(existingNovel.id);\n  }\n\n  // 重新解析\n  return await parseNovelFile(filePath);\n}\n\n// 解析章节范围字符串 (例如: \"1-5,7,10-12\")\nexport function parseChapterRange(rangeStr: string, maxChapter: number): number[] {\n  const chapters: number[] = [];\n  const parts = rangeStr.split(',').map(part => part.trim());\n\n  for (const part of parts) {\n    if (part.includes('-')) {\n      // 范围格式 (例如: \"1-5\")\n      const [start, end] = part.split('-').map(num => parseInt(num.trim()));\n      if (!isNaN(start) && !isNaN(end) && start <= end) {\n        for (let i = start; i <= Math.min(end, maxChapter); i++) {\n          if (i > 0 && !chapters.includes(i)) {\n            chapters.push(i);\n          }\n        }\n      }\n    } else {\n      // 单个章节\n      const chapterNum = parseInt(part);\n      if (!isNaN(chapterNum) && chapterNum > 0 && chapterNum <= maxChapter && !chapters.includes(chapterNum)) {\n        chapters.push(chapterNum);\n      }\n    }\n  }\n\n  return chapters.sort((a, b) => a - b);\n}\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/novels/route\",\n        pathname: \"/api/novels\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/src/app/api/novels/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/novels/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n", "import { NextRequest, NextResponse } from 'next/server';\nimport { novelDb } from '@/lib/database';\nimport { getAvailableNovels, parseNovelFile, isNovelParsed, reparseNovel } from '@/lib/novel-parser';\nimport path from 'path';\n\n// GET - 获取所有小说列表\nexport async function GET() {\n  try {\n    const novels = novelDb.getAll();\n    const availableFiles = getAvailableNovels();\n    \n    // 标记哪些文件已经被解析\n    const novelsWithStatus = availableFiles.map(filename => {\n      const parsed = novels.find(novel => novel.filename === filename);\n      return {\n        filename,\n        parsed: !!parsed,\n        novel: parsed || null,\n      };\n    });\n    \n    return NextResponse.json({\n      success: true,\n      data: {\n        novels: novels,\n        availableFiles: novelsWithStatus,\n      },\n    });\n  } catch (error) {\n    console.error('获取小说列表失败:', error);\n    return NextResponse.json(\n      { success: false, error: '获取小说列表失败' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST - 解析小说文件\nexport async function POST(request: NextRequest) {\n  try {\n    const { filename, reparse = false } = await request.json();\n    \n    if (!filename) {\n      return NextResponse.json(\n        { success: false, error: '文件名不能为空' },\n        { status: 400 }\n      );\n    }\n    \n    // 检查文件是否存在\n    const availableFiles = getAvailableNovels();\n    if (!availableFiles.includes(filename)) {\n      return NextResponse.json(\n        { success: false, error: '文件不存在' },\n        { status: 404 }\n      );\n    }\n    \n    // 检查是否已经解析过\n    if (!reparse && isNovelParsed(filename)) {\n      return NextResponse.json(\n        { success: false, error: '该小说已经解析过，如需重新解析请设置reparse=true' },\n        { status: 409 }\n      );\n    }\n    \n    const novelsDir = path.join(process.cwd(), '..', 'novels');\n    const filePath = path.join(novelsDir, filename);\n    \n    let result;\n    if (reparse) {\n      result = await reparseNovel(filename);\n    } else {\n      result = await parseNovelFile(filePath);\n    }\n    \n    if (!result) {\n      return NextResponse.json(\n        { success: false, error: '解析失败' },\n        { status: 500 }\n      );\n    }\n    \n    return NextResponse.json({\n      success: true,\n      data: result,\n      message: `成功解析小说《${result.novel.title}》，共${result.chapters.length}章`,\n    });\n    \n  } catch (error) {\n    console.error('解析小说失败:', error);\n    return NextResponse.json(\n      { success: false, error: '解析小说失败' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": "m1CAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAoIA,IAAM,EAAW,EAAA,OAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAI,QACpC,EAAc,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,eAClC,EAAgB,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,iBACpC,EAAa,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,sBACjC,EAAY,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,qBAChC,EAAkB,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,mBACtC,EAAe,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,gBACnC,EAAsB,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,uBAC1C,EAAwB,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,yBAGlD,SAAS,IACH,AAAC,EAAA,OAAE,CAAC,UAAU,CAAC,IACjB,EAAA,KAD4B,EAC1B,CAAC,SAAS,CAAC,EAAU,CAAE,WAAW,CAAK,EAE7C,CAGA,SAAS,EAAgB,CAAgB,EAEvC,GADA,IACI,CAAC,EAAA,OAAE,CAAC,UAAU,CAAC,GACjB,MAAO,EADqB,AACnB,CAEX,GAAI,CACF,IAAM,EAAO,EAAA,OAAE,CAAC,YAAY,CAAC,EAAU,SACvC,OAAO,KAAK,KAAK,CAAC,EACpB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,EAAS,CAAC,CAAC,CAAE,GACrC,EAAE,AACX,CACF,CAGA,SAAS,EAAiB,CAAgB,CAAE,CAAS,EACnD,IACA,GAAI,CACF,EAAA,OAAE,CAAC,aAAa,CAAC,EAAU,KAAK,SAAS,CAAC,EAAM,KAAM,GAAI,QAC5D,CAAE,MAAO,EAAO,CAEd,MADA,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,EAAS,CAAC,CAAC,CAAE,GACtC,CACR,CACF,CAGA,SAAS,IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,IAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,EACxE,CAQO,IAAM,EAAU,CACrB,OAAQ,IAAe,EAAoB,GAE3C,QAAS,AAAC,GACO,AACR,EAD4B,GACrB,IAAI,CAAC,GAAS,EAAM,EAAE,GAAK,GAG3C,OAAQ,AAAC,UACP,IAAM,EAAS,EAAoB,GAG7B,GAjBuB,EAiBW,EAAM,GAjBF,AAiB5B,EAAmC,CAhB9C,EAAA,OAAM,CAAC,UAAU,CAAC,OAAO,MAAM,CAAC,GAAS,MAAM,CAAC,OAAO,SAAS,CAAC,EAAG,KAmBnE,EAAgB,EAAO,IAAI,CAAC,GAAK,EAAE,EAAE,GAAK,GAChD,GAAI,EAKF,OAHA,EAAc,IAFG,IAEK,CAAG,EAAM,QAAQ,CACvC,EAAc,YAAY,CAAG,EAAM,YAAY,CAC/C,EAAc,EAAa,GACpB,EAGT,IAAM,EAAkB,CACtB,GAAG,CAAK,CACR,GAAI,EACJ,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAO,IAAI,CAAC,GACZ,EAAc,EAAa,GACpB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAS,EAAoB,GAC7B,EAAQ,EAAO,SAAS,CAAC,GAAS,EAAM,EAAE,GAAK,UACrD,AAAc,CAAC,GAAG,CAAd,EAAqB,MAEzB,CAAM,CAAC,EAAM,CAAG,CAAE,GAAG,CAAM,CAAC,EAAM,CAAE,GAAG,CAAO,AAAC,EAC/C,EAAc,EAAa,GACpB,CAAM,CAAC,EAAM,CACtB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAS,EAAoB,GAC7B,EAAQ,EAAO,SAAS,CAAC,GAAS,EAAM,EAAE,GAAK,UACrD,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAElB,MAAM,CAAC,EAAO,GACrB,EAAc,EAAa,IACpB,EACT,CACF,EAGa,EAAY,CACvB,OAAQ,IAAiB,EAAsB,GAE/C,aAAc,AAAC,GACI,AACV,EADgC,GACvB,MAAM,CAAC,GAAW,EAAQ,OAAO,GAAK,GAGxD,QAAS,AAAC,GAED,AADU,EAAsB,GACvB,IAAI,CAAC,GAAW,EAAQ,EAAE,GAAK,GAGjD,OAAQ,AAAC,IACP,IAAM,EAAW,EAAsB,GACjC,EAAsB,CAC1B,GAAG,CAAO,CACV,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAS,IAAI,CAAC,GACd,EAAc,EAAe,GACtB,CACT,EAEA,YAAa,AAAC,IACZ,IAAM,EAAmB,EAAsB,GACzC,EAAc,EAAS,GAAG,CAAC,IAAY,CAC3C,GAAG,CAAO,CADgC,AAE1C,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACnC,CAAC,EAGD,OAFA,EAAiB,IAAI,IAAI,GACzB,EAAc,EAAe,GACtB,CACT,EAEA,OAAQ,AAAC,IACP,IAAM,EAAW,EAAsB,GACjC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,UAC3D,AAAc,CAAC,GAAG,CAAd,IAEJ,EAAS,AAFgB,MAEV,CAAC,EAAO,GACvB,EAAc,EAAe,IACtB,EACT,EAEA,gBAAiB,AAAC,IAEhB,IAAM,EADW,AACQ,EADc,GACL,MAAM,CAAC,GAAW,EAAQ,OAAO,GAAK,GAExE,OADA,EAAc,EAAe,IACtB,CACT,CACF,EAGa,EAAS,CACpB,OAAQ,IAAqB,EAA0B,GAEvD,QAAS,AAAC,GACM,AACP,EADiC,GAC3B,IAAI,CAAC,GAAQ,EAAK,EAAE,GAAK,GAGxC,OAAQ,AAAC,IACP,IAAM,EAAQ,EAA0B,GAClC,EAAuB,CAC3B,GAAG,CAAI,CACP,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAM,IAAI,CAAC,GACX,EAAc,EAAY,GACnB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAQ,EAA0B,GAClC,EAAQ,EAAM,SAAS,CAAC,GAAQ,EAAK,EAAE,GAAK,UAClD,AAAI,AAAU,CAAC,GAAG,GAAO,MAEzB,CAAK,CAAC,EAAM,CAAG,CACb,GAAG,CAAK,CAAC,EAAM,CACf,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAY,GACnB,CAAK,CAAC,EAAM,CACrB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAQ,EAA0B,GAClC,EAAQ,EAAM,SAAS,CAAC,GAAQ,EAAK,EAAE,GAAK,UAClD,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAEnB,MAAM,CAAC,EAAO,GACpB,EAAc,EAAY,IACnB,EACT,CACF,EAGa,EAAQ,CACnB,OAAQ,IAAoB,EAAyB,GAErD,QAAS,AAAC,GACK,AACN,EAD+B,GAC1B,IAAI,CAAC,GAAO,EAAI,EAAE,GAAK,GAGrC,OAAQ,AAAC,IACP,IAAM,EAAO,EAAyB,GAChC,EAAqB,CACzB,GAAG,CAAG,CACN,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAK,IAAI,CAAC,GACV,EAAc,EAAW,GAClB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAO,EAAyB,GAChC,EAAQ,EAAK,SAAS,CAAC,GAAO,EAAI,EAAE,GAAK,UAC/C,AAAc,CAAC,GAAG,CAAd,EAAqB,MAEzB,CAAI,CAAC,EAAM,CAAG,CACZ,GAAG,CAAI,CAAC,EAAM,CACd,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAW,GAClB,CAAI,CAAC,EAAM,CACpB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAO,EAAyB,GAChC,EAAQ,EAAK,SAAS,CAAC,GAAO,EAAI,EAAE,GAAK,UAC/C,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAEpB,MAAM,CAAC,EAAO,GACnB,EAAc,EAAW,IAClB,EACT,CACF,EAGa,EAAc,CACzB,OAAQ,IAAmB,EAAwB,GAEnD,aAAc,AAAC,GACM,AACZ,EADoC,GACzB,MAAM,CAAC,GAAa,EAAU,OAAO,GAAK,GAG9D,QAAS,AAAC,GACW,AACZ,EADoC,GACzB,IAAI,CAAC,GAAa,EAAU,EAAE,GAAK,GAGvD,OAAQ,AAAC,IACP,IAAM,EAAa,EAAwB,GACrC,EAA0B,CAC9B,GAAG,CAAS,CACZ,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAW,IAAI,CAAC,GAChB,EAAc,EAAiB,GACxB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAa,EAAwB,GACrC,EAAQ,EAAW,SAAS,CAAC,GAAa,EAAU,EAAE,GAAK,UACjE,AAAc,CAAC,GAAG,CAAd,EAAqB,MAEzB,CAAU,CAAC,EAAM,CAAG,CAClB,GAAG,CAAU,CAAC,EAAM,CACpB,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAiB,GACxB,CAAU,CAAC,EAAM,CAC1B,EAEA,OAAQ,AAAC,IACP,IAAM,EAAa,EAAwB,GACrC,EAAQ,EAAW,SAAS,CAAC,GAAa,EAAU,EAAE,GAAK,UACjE,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAEd,MAAM,CAAC,EAAO,GACzB,EAAc,EAAiB,IACxB,EACT,EAEA,gBAAiB,AAAC,IAEhB,IAAM,EADa,AACQ,EADgB,GACL,MAAM,CAAC,GAAa,EAAU,OAAO,GAAK,GAEhF,OADA,EAAc,EAAiB,IACxB,CACT,CACF,EAGa,EAAW,CACtB,OAAQ,IAAgB,EAAqB,GAE7C,QAAS,AAAC,GACQ,AACT,EAD8B,GACtB,IAAI,CAAC,GAAU,EAAO,EAAE,GAAK,GAG9C,OAAQ,AAAC,IACP,IAAM,EAAU,EAAqB,GAC/B,EAAoB,CACxB,GAAG,CAAM,CACT,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAQ,IAAI,CAAC,GACb,EAAc,EAAc,GACrB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAU,EAAqB,GAC/B,EAAQ,EAAQ,SAAS,CAAC,GAAU,EAAO,EAAE,GAAK,UACxD,AAAc,CAAC,GAAG,CAAd,EAAqB,MAEzB,CAAO,CAAC,EAAM,CAAG,CACf,GAAG,CAAO,CAAC,EAAM,CACjB,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAc,GACrB,CAAO,CAAC,EAAM,CACvB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAU,EAAqB,GAC/B,EAAQ,EAAQ,SAAS,CAAC,GAAU,EAAO,EAAE,GAAK,UACxD,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAEjB,MAAM,CAAC,EAAO,GACtB,EAAc,EAAc,IACrB,EACT,CACF,EAGa,EAAiB,CAC5B,OAAQ,IAAsB,EAA2B,GAEzD,aAAc,AAAC,GAEN,AADU,EAA2B,GAC5B,IAAI,CAAC,GAAW,EAAQ,OAAO,GAAK,GAGtD,QAAU,AAAD,GACU,AACV,EADqC,GAC5B,IAAI,CAAC,GAAW,EAAQ,EAAE,GAAK,GAGjD,OAAQ,AAAC,IACP,IAAM,EAAW,EAA2B,GACtC,EAA2B,CAC/B,GAAG,CAAO,CACV,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAS,IAAI,CAAC,GACd,EAAc,EAAqB,GAC5B,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAW,EAA2B,GACtC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,GAC3D,GAAI,AAAU,CAAC,GAAG,GAQlB,IARyB,GAEzB,CAAQ,CAAC,EAAM,CAAG,CAChB,GAAG,CAAQ,CAAC,EAAM,CAClB,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAqB,GAC5B,CAAQ,CAAC,EAAM,AACxB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAW,EAA2B,GACtC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,UAC3D,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAEhB,MAAM,CAAC,EAAO,GACvB,EAAc,EAAqB,IAC5B,EACT,CACF,EAGa,EAAmB,CAC9B,OAAQ,IAAwB,EAA6B,GAE7D,aAAc,AAAC,GACI,AACV,EADuC,GAC9B,MAAM,CAAC,GAAW,EAAQ,OAAO,GAAK,GAGxD,aAAc,CAAC,EAAiB,IACb,AACV,EADuC,GAC9B,IAAI,CAAC,GACnB,EAAQ,OAAO,GAAK,GAAW,EAAQ,aAAa,GAAK,GAI7D,QAAS,AAAC,GACS,AACV,EADuC,GAC9B,IAAI,CAAC,GAAW,EAAQ,EAAE,GAAK,GAGjD,OAAQ,AAAC,IACP,IAAM,EAAW,EAA6B,GACxC,EAA6B,CACjC,GAAG,CAAO,CACV,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAS,IAAI,CAAC,GACd,EAAc,EAAuB,GAC9B,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAW,EAA6B,GACxC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,GAC3D,GAAc,CAAC,GAAG,CAAd,EAQJ,IARyB,GAEzB,CAAQ,CAAC,EAAM,CAAG,CAChB,GAAG,CAAQ,CAAC,EAAM,CAClB,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAuB,GAC9B,CAAQ,CAAC,EAAM,AACxB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAW,EAA6B,GACxC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,UAC7C,AAAd,CAAe,GAAG,CAAd,IAEJ,EAFyB,AAEhB,MAAM,CAAC,EAAO,GACvB,EAAc,EAAuB,IAC9B,EACT,EAGA,iBAAkB,CAAC,EAAiB,EAAuB,EAAqB,CAAC,IAE/E,IAAM,EADW,AACK,EADwB,GACf,MAAM,CAAC,GAAW,EAAQ,OAAO,GAAK,GAE/D,EAAe,KAAK,GAAG,CAAC,EAAG,EAAgB,GAC3C,EAAa,EAAgB,EAEnC,OAAO,EAAc,MAAM,CAAC,GAC1B,EAAQ,aAAa,EAAI,GAAgB,EAAQ,aAAa,EAAI,GAClE,IAAI,CAAC,CAAC,EAAG,IAAM,EAAE,aAAa,CAAG,EAAE,aAAa,CACpD,CACF,2XC7mBA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAGO,OAAM,EACX,OAAe,QACP,AAD6B,QACb,AAExB,cAAsB,CACpB,IAAI,CAAC,OAAO,CAAG,QAAQ,GAAG,EAC5B,CAEA,OAAc,aAA2B,CAIvC,OAHI,AAAC,EAAY,QAAQ,EAAE,CACzB,EAAY,QAAQ,CAAG,IAAI,CAAA,EAEtB,EAAY,QAAQ,AAC7B,CAGO,UAAU,CAAe,CAAQ,CAClC,AAAC,EAAA,OAAE,CAAC,UAAU,CAAC,IACjB,EAAA,IAD2B,GACzB,CAAC,SAAS,CAAC,EAAS,CAAE,WAAW,CAAK,EAE5C,CAGO,cAAuB,CAC5B,OAAO,EAAA,OAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAE,KAAM,SACvC,CAGO,gBAAyB,CAC9B,OAAO,EAAA,OAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAE,KAAM,WACvC,CAGO,YAAqB,CAC1B,IAAM,EAAU,EAAA,OAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAE,QAExC,OADA,IAAI,CAAC,SAAS,CAAC,GACR,CACT,CAGO,iBAA0B,CAC/B,IAAM,EAAe,EAAA,OAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAI,aAElD,OADA,IAAI,CAAC,SAAS,CAAC,GACR,CACT,CAGO,qBAAqB,CAAkB,CAAU,CACtD,IAAM,EAAW,EAAA,OAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,GAAI,IAAI,CAAC,gBAAgB,CAAC,IAEzE,OADA,IAAI,CAAC,SAAS,CAAC,GACR,CACT,CAGO,kBAA2B,CAChC,IAAM,EAAgB,EAAA,OAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAE,KAAM,eAEpD,OADA,IAAI,CAAC,SAAS,CAAC,GACR,CACT,CAGO,oBAAoB,CAAkB,CAAU,CACrD,IAAM,EAAc,IAAI,CAAC,cAAc,GACvC,IAAI,CAAC,SAAS,CAAC,GACf,IAAM,EAAW,EAAA,OAAI,CAAC,IAAI,CAAC,EAAa,IAAI,CAAC,gBAAgB,CAAC,IAE9D,OADA,IAAI,CAAC,SAAS,CAAC,GACR,CACT,CAGO,iBAAiB,CAAgB,CAAU,CAChD,OAAO,EAAS,OAAO,CAAC,gBAAiB,KAAK,IAAI,EACpD,CAGO,SAAS,CAAgB,CAAU,CACxC,GAAI,CACF,OAAO,EAAA,OAAE,CAAC,YAAY,CAAC,EAAU,QACnC,CAAE,MAAO,EAAO,CAEd,MADA,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAA,CAAU,CAAE,GAC/B,CACR,CACF,CAGO,UAAU,CAAgB,CAAE,CAAe,CAAQ,CACxD,GAAI,CACF,IAAM,EAAM,EAAA,OAAI,CAAC,OAAO,CAAC,GACzB,IAAI,CAAC,SAAS,CAAC,GACf,EAAA,OAAE,CAAC,aAAa,CAAC,EAAU,EAAS,QACtC,CAAE,MAAO,EAAO,CAEd,MADA,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAA,CAAU,CAAE,GAC/B,CACR,CACF,CAGO,WAAW,CAAgB,CAAW,CAC3C,OAAO,EAAA,OAAE,CAAC,UAAU,CAAC,EACvB,CAGO,UAAU,CAAe,CAAE,CAAqB,CAAY,CACjE,GAAI,CACF,GAAI,CAAC,EAAA,OAAE,CAAC,UAAU,CAAC,GACjB,MAAO,CADoB,CAClB,CAGX,IAAM,EAAQ,EAAA,OAAE,CAAC,WAAW,CAAC,GAE7B,GAAI,EACF,OAAO,EAAM,CADC,KACK,CAAC,IAClB,IAAM,EAAM,EAAA,OAAI,CAAC,OAAO,CAAC,GAAM,WAAW,GAC1C,OAAO,EAAW,QAAQ,CAAC,EAC7B,GAGF,OAAO,CACT,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAA,CAAS,CAAE,GAC7B,EAAE,AACX,CACF,CAGO,aAAa,CAAgB,CAAmB,CACrD,GAAI,CACF,OAAO,EAAA,OAAE,CAAC,QAAQ,CAAC,EACrB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,EAAA,CAAU,CAAE,GAChC,IACT,CACF,CAGO,WAAW,CAAgB,CAAW,CAC3C,GAAI,CACF,GAAI,EAAA,OAAE,CAAC,UAAU,CAAC,GAEhB,OADA,CAD2B,CAC3B,OAAE,CAAC,UAAU,CAAC,IACP,EAET,OAAO,CACT,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAA,CAAU,CAAE,IAC9B,CACT,CACF,CAGO,UAAU,CAAe,CAAW,CACzC,GAAI,CACF,GAAI,EAAA,OAAE,CAAC,UAAU,CAAC,GAEhB,OAF0B,AAC1B,EAAA,OAAE,CAAC,MAAM,CAAC,EAAS,CAAE,WAAW,EAAM,OAAO,CAAK,IAC3C,EAET,OAAO,CACT,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAA,CAAS,CAAE,IAC7B,CACT,CACF,CAGO,SAAS,CAAe,CAAE,CAAgB,CAAW,CAC1D,GAAI,CACF,IAAM,EAAU,EAAA,OAAI,CAAC,OAAO,CAAC,GAG7B,OAFA,IAAI,CAAC,SAAS,CAAC,GACf,EAAA,OAAE,CAAC,YAAY,CAAC,EAAS,IAClB,CACT,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAQ,IAAI,EAAE,EAAA,CAAU,CAAE,IAC5C,CACT,CACF,CAGO,SAAS,CAAe,CAAE,CAAgB,CAAW,CAC1D,GAAI,CACF,IAAM,EAAU,EAAA,OAAI,CAAC,OAAO,CAAC,GAG7B,OAFA,IAAI,CAAC,SAAS,CAAC,GACf,EAAA,OAAE,CAAC,UAAU,CAAC,EAAS,IAChB,CACT,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAQ,IAAI,EAAE,EAAA,CAAU,CAAE,IAC5C,CACT,CACF,CAGO,WAAW,CAAe,CAAU,CACzC,IAAI,EAAY,EAEhB,GAAI,CACF,GAAI,CAAC,EAAA,OAAE,CAAC,UAAU,CAAC,GACjB,OAD2B,AACpB,EAKT,IAAK,IAAM,KAFG,EAAA,CAEK,MAFH,CAAC,WAAW,CAAC,GAEH,CACxB,IAAM,EAAW,EAAA,OAAI,CAAC,IAAI,CAAC,EAAS,GAC9B,EAAQ,EAAA,OAAE,CAAC,QAAQ,CAAC,GAEtB,EAAM,WAAW,GACnB,CADuB,EACV,IAAI,CAAC,UAAU,CAAC,GAE7B,GAAa,EAAM,IAEvB,AAF2B,CAG7B,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,EAAA,CAAS,CAAE,EACxC,CAEA,OAAO,CACT,CAGO,eAAe,CAAa,CAAU,CAC3C,GAAc,IAAV,EAAa,MAAO,MAIxB,IAAM,EAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAS,KAAK,GAAG,CAAC,OAEhD,OAAO,WAAW,CAAC,EAAQ,KAAK,GAAG,CAJzB,AAI0B,KAAG,EAAA,CAAE,CAAE,OAAO,CAAC,IAAM,IAH3C,AAGiD,CAHhD,IAAK,KAAM,KAAM,KAGoC,AAH/B,CAGgC,EAAE,AACzE,CAGO,aAAa,CAAgB,CAAiB,CACnD,GAAI,CACF,IAAM,EAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,QAAS,KACtD,EAAM,EAAA,OAAI,CAAC,OAAO,CAAC,GACnB,EAAW,EAAA,OAAI,CAAC,QAAQ,CAAC,EAAU,GACnC,EAAM,EAAA,OAAI,CAAC,OAAO,CAAC,GAEnB,EAAa,EAAA,OAAI,CAAC,IAAI,CAAC,EAAK,CAAA,EAAG,EAAS,QAAQ,EAAE,EAAA,EAAY,EAAA,CAAK,EAEzE,GAAI,IAAI,CAAC,QAAQ,CAAC,EAAU,GAC1B,OAAO,EAGT,CAJyC,MAIlC,IACT,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAA,CAAU,CAAE,GAC9B,IACT,CACF,CAGO,uBAAuB,CAAkB,CAA2D,CACzG,GAAI,CACF,IAAM,EAAe,IAAI,CAAC,oBAAoB,CAAC,GACzC,EAAgB,IAAI,CAAC,gBAAgB,GAGrC,EAAe,IAAI,CAAC,SAAS,CAAC,EAAc,CAAC,OAAO,EACvD,MAAM,CAAC,GAAQ,EAAK,UAAU,CAAC,aAAe,EAAK,QAAQ,CAAC,eAC5D,IAAI,CAAC,CAAC,EAAG,KAER,IAAM,EAAO,SAAS,EAAE,KAAK,CAAC,kBAAkB,CAAC,EAAE,EAAI,KACjD,EAAO,SAAS,EAAE,KAAK,CAAC,kBAAkB,CAAC,EAAE,EAAI,KACvD,OAAO,EAAO,CAChB,GAEF,GAA4B,GAAG,CAA3B,EAAa,MAAM,CACrB,MAAO,CAAE,SAAS,EAAO,MAAO,aAAc,EAIhD,IAAI,EAAgB,GACpB,IAAK,IAAM,KAAe,EAAc,CACtC,IAAM,EAAc,EAAA,OAAI,CAAC,IAAI,CAAC,EAAc,GACtC,EAAiB,IAAI,CAAC,QAAQ,CAAC,GACrC,GAAiB,EAAiB,MACpC,CAGA,IAAM,EAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,QAAS,KAAK,SAAS,CAAC,EAAG,IACxE,EAAiB,CAAA,EAAG,IAAI,CAAC,gBAAgB,CAAC,GAAY,QAAQ,EAAE,EAAU,IAAI,CAAC,CAC/E,EAAiB,EAAA,OAAI,CAAC,IAAI,CAAC,EAAe,GAKhD,OAFA,IAAI,CAAC,SAAS,CAAC,EAAgB,EAAc,IAAI,IAE1C,CACL,SAAS,EACT,SAAU,CACZ,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAA,CAAY,CAAE,GAChC,CACL,SAAS,EACT,MAAO,aAAiB,MAAQ,EAAM,OAAO,CAAG,MAClD,CACF,CACF,CAGO,eAAe,CAAe,CAAE,EAAqB,CAAC,CAAQ,CACnE,GAAI,CAEF,IAAM,EAAc,AADN,IAAI,CAAC,SAAS,CAAC,GAE1B,MAAM,CAAC,GAAQ,EAAK,QAAQ,CAAC,aAC7B,GAAG,CAAC,IAAS,CACZ,EADW,GACL,EACN,KAAM,EAAA,OAAI,CAAC,IAAI,CAAC,EAAS,GACzB,MAAO,IAAI,CAAC,YAAY,CAAC,EAAA,OAAI,CAAC,IAAI,CAAC,EAAS,IAC9C,CAAC,EACA,MAAM,CAAC,GAAQ,AAAe,SAAV,KAAK,EACzB,IAAI,CAAC,CAAC,EAAG,IAAM,EAAE,KAAK,CAAE,KAAK,CAAC,OAAO,GAAK,EAAE,KAAK,CAAE,KAAK,CAAC,OAAO,IAGnE,GAAI,EAAY,MAAM,CAAG,EAEvB,IAAK,IAAM,EAFwB,GACb,EAAY,CACf,IADoB,CAAC,GAEtC,IAAI,CAAC,CAD2B,SACjB,CAAC,EAAK,IAAI,CAG/B,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,EAAA,CAAS,CAAE,EACxC,CACF,CACF,CAGO,IAAM,EAAc,EAAY,WAAW,yJCzUlD,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAUA,IAAM,EAAkB,CACtB,4CACA,2CACD,CAGK,EAAmB,CACvB,4CACA,iCACD,CAGM,eAAe,EAAe,CAAgB,EAInD,IAAM,EAAW,EAAA,OAAI,CAAC,QAAQ,CAAC,GACzB,EAAQ,EAAS,OAAO,CAAC,eAAgB,IAGzC,EAAU,EAAA,OAAE,CAAC,YAAY,CAAC,EAAU,SAGpC,EAAQ,EAAA,OAAO,CAAC,MAAM,CAAC,OAC3B,WACA,CACF,GAGM,EAAW,AAkBnB,SAAS,AAAc,CAAe,CAAE,CAAe,EACrD,IAAM,EAAgD,EAAE,CAGlD,EAAgB,AA6BxB,SAA2B,AAAlB,CAAiC,EACxC,IAAM,EAAyD,EAAE,CAEjE,IAAK,IAAM,KAAW,EAEpB,IAAK,IAAM,KADK,CADqB,GAEjB,EADE,IAAI,CAAC,CACE,CADM,QAAQ,CAAC,IAE1C,EAAc,IAAI,CAAC,CACjB,MAAO,EAAM,KAAK,CAClB,MAAO,EAAoB,CAAK,CAAC,EAAE,CACrC,GAKJ,OAAO,EAAc,IAAI,CAAC,CAAC,EAAG,IAAM,EAAE,KAAK,CAAG,EAAE,KAAK,CACvD,EA5C0C,GAGxC,GAAI,EAAc,MAAM,CAAG,EAAG,CAE5B,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,EAAc,MAAM,CAAC,QAAQ,CAAC,EACnD,IAAI,EAAgB,EAEpB,IAAK,IAAI,EAAI,EAAG,EAAI,EAAc,MAAM,CAAE,IAAK,CAC7C,IAAM,EAAc,CAAa,CAAC,EAAE,CAAC,KAAK,CACpC,EAAY,EAAI,EAAI,EAAc,MAAM,CAAG,CAAa,CAAC,EAAI,EAAE,CAAC,KAAK,CAAG,EAAQ,MAAM,CAItF,EAAiB,EAHD,EAAQ,KAAK,CAAC,EAAa,GAGW,EAAS,EAAe,CAAa,CAAC,CAArD,CAAuD,CAAC,KAAK,EAC1G,EAAS,IAAI,IAAI,GACjB,GAAiB,EAAe,MAAM,AACxC,CACF,KAAO,CAEL,IAAM,EAAiB,EAAsB,EAAS,EAAS,GAC/D,EAAS,IAAI,IAAI,EACnB,CAGA,OADA,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,EAAS,MAAM,CAAC,SAAS,CAAC,EACtD,CACT,EAhDiC,EAAS,EAAM,EAAE,EAG1C,EAAkB,EAAA,SAAS,CAAC,WAAW,CAAC,GAQ9C,OALA,EAAA,OAAO,CAAC,MAAM,CAAC,EAAM,EAAE,CAAE,CAAE,aAAc,EAAgB,MAAM,AAAC,GAGhE,MAAM,EAAiB,GAEhB,CACL,MAAO,CAAE,GAAG,CAAK,CAAE,aAAc,EAAgB,MAAM,AAAC,EACxD,SAAU,CACZ,CACF,CAsDA,SAAS,EACP,CAAe,CACf,CAAe,CACf,CAA0B,CAC1B,CAAoB,EAEpB,IAAM,EAAgD,EAAE,CACpD,EAAgB,EAGhB,EAA6B,KAC7B,EAAmB,CAAC,EAExB,IAAK,IAAM,KAAW,EAAkB,CACtC,IAAM,EAAU,EAAQ,KAAK,CAAC,GACxB,EAAa,EAAU,EAAQ,CADG,KACG,CAAG,EAC1C,EAAa,IACf,EAAmB,EACnB,EAAc,EAElB,CAGA,GAAI,CAAC,CATiE,AAEjC,EAOI,IAArB,EAAwB,CAC1C,IAAM,EAAiB,EAAQ,IAAI,GAUnC,OATI,EAAe,MAAM,CAAG,KAAK,AAC/B,EAAS,IAAI,CAAC,SACZ,EACA,cAAe,EACf,MAAO,GAAe,KACtB,QAAS,EACT,SAAU,CAAC,QAAQ,EAAE,EAAc,IAAI,CAAC,AAC1C,GAEK,CACT,CAIA,IAAM,EAAe,AAAI,OAAO,CAAC,CAAC,EAAE,EAAY,MAAM,CAAC,CAAC,CAAC,CAAE,OACrD,EAAQ,EAAQ,KAAK,CAAC,GAOtB,EAAW,CAAK,CAAC,EAAE,EAAE,OACvB,GAAY,EAAS,MAAM,CAAG,KAAK,CACrC,EAAS,IAAI,CAAC,SACZ,EACA,cAAe,EAEf,MAAO,KACP,QAAS,EACT,SAAU,CAAC,QAAQ,EAAE,EAAc,IAAI,CAAC,AAC1C,GACA,KAEF,QAAQ,GAAG,CAAC,EAAM,GAAG,CAAC,CAAC,EAAG,IAAM,EAAI,IAGpC,IAAK,IAAI,EAAI,EAAG,EAAI,EAAM,MAAM,CAAE,GAAK,EAAG,CAExC,IAAM,EAAY,CAAK,CAAC,EAAE,CACpB,EAAc,CAAK,CAAC,EAAI,EAAE,EAAI,GAEpC,CAFwC,EAEpC,CAAC,EAAW,KAFkC,IAIlD,IAAM,EAAiB,CAAC,EAAY,CAAA,CAAW,CAAE,IAAI,EAEjD,GAAe,MAAM,CAAG,KAAK,CAC/B,EAAS,IAAI,CAAC,SACZ,EACA,cAAe,EACf,MAAO,EAAoB,GAC3B,QAAS,EACT,SAAU,CAAC,QAAQ,EAAE,EAAc,IAAI,CAAC,AAC1C,GACA,IAEJ,CAEA,OAAO,CACT,CAEA,SAAS,EAAoB,CAAmB,EAC9C,IAAM,EAAQ,EAAY,IAAI,GAAG,KAAK,CAAC,MACjC,EAAY,CAAK,CAAC,EAAE,CAAC,IAAI,GAG/B,GAAI,EAAU,MAAM,CAAG,KAAO,EAAU,MAAM,CAAG,EAC/C,CADkD,MAC3C,EAIT,IAAK,IAAI,EAAI,EAAG,EAAI,KAAK,GAAG,CAAC,EAAG,EAAM,MAAM,EAAG,IAAK,CAClD,IAAM,EAAO,CAAK,CAAC,EAAE,CAAC,IAAI,GAC1B,GAAI,EAAK,MAAM,CAAG,GAAK,EAAK,MAAM,CAAG,IACnC,CADwC,MACjC,CAEX,CAEA,MAAO,OACT,CAGA,eAAe,EAAiB,CAAmB,EAIjD,IAAK,IAAM,IAFM,IAAI,GAEC,CAFG,IAAI,EAAS,GAAG,CAAC,GAAM,EAAG,OAAO,GAAG,CAE7B,CAC9B,IAAM,EAAQ,EAAA,OAAO,CAAC,OAAO,CAAC,GAC9B,GAAI,CAAC,EAAO,SAEZ,IAAM,EAAW,EAAA,WAAW,CAAC,mBAAmB,CAAC,EAAM,KAAK,EAI5D,IAAK,IAAM,KADW,EAAS,IACT,EADe,CAAC,GAAM,EAAG,OAAO,GAAK,GACtB,CACnC,IAAM,EAAc,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,EAAQ,QAAQ,EACxD,EAAA,WAAW,CAAC,SAAS,CAAC,EAAa,EAAQ,OAAO,CACpD,CACF,CACF,CAGO,SAAS,IACd,IAAM,EAAY,EAAA,WAAW,CAAC,YAAY,GAC1C,OAAO,EAAA,WAAW,CAAC,SAAS,CAAC,EAAW,CAAC,OAAQ,MAAM,CACzD,CAGO,SAAS,EAAc,CAAgB,EAE5C,OAAO,AADQ,EAAA,OAAO,CAAC,MAAM,GACf,IAAI,CAAC,GAAS,EAAM,QAAQ,GAAK,EACjD,CAGO,eAAe,EAAa,CAAgB,EAIjD,IAAM,EAAY,EAAA,WAAW,CAAC,YAAY,GACpC,EAAW,EAAA,OAAI,CAAC,IAAI,CAAC,EAAW,GAEtC,GAAI,CAAC,EAAA,WAAW,CAAC,UAAU,CAAC,GAC1B,OAAO,CAD8B,IAMvC,IAAM,EAAgB,AADC,EAAA,OAAO,CAAC,MAAM,GACA,IAAI,CAAC,GAAS,EAAM,QAAQ,GAAK,GAQtE,OANI,IACF,EAAA,SAAS,AADQ,CACP,eAAe,CAAC,EAAc,EAAE,EAC1C,EAAA,OAAO,CAAC,MAAM,CAAC,EAAc,EAAE,GAI1B,MAAM,EAAe,EAC9B,CAGO,SAAS,EAAkB,CAAgB,CAAE,CAAkB,EACpE,IAAM,EAAqB,EAAE,CAG7B,IAAK,IAAM,KAFG,EAAS,CAEJ,IAFS,CAAC,CAEH,IAFQ,GAAG,CAAC,GAAQ,EAAK,IAAI,IAGrD,GAAI,EAAK,QAAQ,CAAC,KAAM,CAEtB,GAAM,CAAC,EAAO,EAAI,CAAG,EAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAO,SAAS,EAAI,IAAI,KACjE,GAAI,CAAC,MAAM,IAAU,CAAC,MAAM,IAAQ,GAAS,EAC3C,GADgD,CAC3C,IAAI,EAAI,EAAO,GAAK,KAAK,GAAG,CAAC,EAAK,GAAa,IAAK,AACnD,EAAI,GAAK,CAAC,EAAS,QAAQ,CAAC,IAAI,AAClC,EAAS,IAAI,CAAC,EAItB,KAAO,CAEL,IAAM,EAAa,SAAS,EACxB,EAAC,MAAM,IAAe,EAAa,GAAK,GAAc,GAAc,CAAC,EAAS,QAAQ,CAAC,IACzF,EAAS,IAAI,CAAC,EADwF,AAG1G,CAGF,OAAO,EAAS,IAAI,CAAC,CAAC,EAAG,IAAM,EAAI,EACrC,0LC5SA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,0CCfA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAGO,eAAe,IACpB,GAAI,CACF,IAAM,EAAS,EAAA,OAAO,CAAC,MAAM,GAIvB,EAHiB,AAGE,CAHF,EAAA,EAAA,kBAAA,AAAkB,IAGD,GAAG,CAAC,IAC1C,IAAM,EAAS,EAAO,IAAI,CAAC,GAAS,EAAM,QAAQ,GAAK,GACvD,MAAO,UACL,EACA,OAAQ,CAAC,CAAC,EACV,MAAO,GAAU,IACnB,CACF,GAEA,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,SAAS,EACT,KAAM,CACJ,OAAQ,EACR,eAAgB,CAClB,CACF,EACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,YAAa,GACpB,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,UAAW,EACpC,CAAE,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe,EAAK,CAAoB,EAC7C,GAAI,CACF,IA6BI,EA7BE,UAAE,CAAQ,SAAE,GAAU,CAAK,CAAE,CAAG,MAAM,EAAQ,IAAI,GAExD,GAAI,CAAC,EACH,OAAO,CADM,CACN,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,SAAU,EACnC,CAAE,OAAQ,GAAI,GAMlB,GAAI,CADmB,AAClB,AADkB,CAAA,EAAA,EAAA,kBAAA,AAAkB,IACrB,QAAQ,CAAC,GAC3B,OAAO,CAD+B,CAC/B,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,OAAQ,EACjC,CAAE,OAAQ,GAAI,GAKlB,GAAI,CAAC,GAAW,CAAA,EAAA,EAAA,aAAA,AAAa,EAAC,GAC5B,OAAO,CADgC,CAChC,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,gCAAiC,EAC1D,CAAE,OAAQ,GAAI,GAIlB,IAAM,EAAY,EAAA,OAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAI,KAAM,UAC3C,EAAW,EAAA,OAAI,CAAC,IAAI,CAAC,EAAW,GAStC,GAAI,CAAC,CALH,EADE,EACO,GAKE,GALI,CADJ,AACI,EAAA,EAAA,YAAA,AAAY,EAAC,GAEnB,MAAM,CAAA,EAAA,EAAA,cAAc,AAAd,EAAe,IAI9B,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,MAAO,EAChC,CAAE,OAAQ,GAAI,GAIlB,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,SAAS,EACT,KAAM,EACN,QAAS,CAAC,OAAO,EAAE,EAAO,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,EAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,AACtE,EAEF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,UAAW,GAClB,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,QAAS,EAClC,CAAE,OAAQ,GAAI,EAElB,CACF,CDhFA,IAAA,EAAA,EAAA,CAAA,CAAA,MAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,oBACN,SAAU,cACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,wCAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,uBACA,CACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,oBAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,YAAE,CAAU,aAAE,CAAW,mBAAE,CAAiB,qBAAE,CAAmB,sBAAE,CAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,GAAQ,EAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,IAC+B,IAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,EACN,AAAsB,OAAV,CAAkB,IAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAa,EAAO,UAVyE,QAUvD,GACtC,EAAU,QACZ,oBACA,EACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAAmB,AAAwD,MAAvD,GAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,sBAAkB,EAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAI,AAAJ,EAEnC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAW,AAAR,EAAgB,UAAU,CAAC,mBAAmB,CACvL,EAAS,AAA8C,SAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,MAAG,EAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,YACV,SACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAA,AAAO,EAAE,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAmB,AAAnB,EAAoB,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,mBAAmB,uBACnB,0BACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAK,AAAL,EAAiB,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZI,AAAE,CAAD,AAAC,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAI,AAAL,SAAc,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAeV,GAbI,AAAC,GAAgB,WAAF,CAAC,CAAgB,EAAA,eAAe,EAC/C,CADkD,KAC5C,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAmB,AAAnB,EAAoB,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [3]}