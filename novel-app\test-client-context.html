<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户端上下文系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .loading {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .input-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <h1>客户端上下文系统测试</h1>
    <p>这个页面测试浏览器端的上下文系统功能。</p>

    <div class="input-group">
        <label for="novelId">小说ID:</label>
        <input type="text" id="novelId" placeholder="输入小说ID" value="">
    </div>

    <div class="input-group">
        <label for="chapterNumber">章节号:</label>
        <input type="number" id="chapterNumber" placeholder="输入章节号" value="1" min="1">
    </div>

    <div class="test-section">
        <h3>1. 测试 contextAnalyzer 实例</h3>
        <button onclick="testContextAnalyzer()">测试 contextAnalyzer</button>
        <div id="contextAnalyzerResult"></div>
    </div>

    <div class="test-section">
        <h3>2. 获取小说上下文</h3>
        <button onclick="testGetNovelContext()">获取小说上下文</button>
        <div id="novelContextResult"></div>
    </div>

    <div class="test-section">
        <h3>3. 获取章节上下文</h3>
        <button onclick="testGetChapterContext()">获取章节上下文</button>
        <div id="chapterContextResult"></div>
    </div>

    <div class="test-section">
        <h3>4. 分析小说上下文</h3>
        <button onclick="testAnalyzeNovel()">分析小说</button>
        <div id="analyzeResult"></div>
    </div>

    <div class="test-section">
        <h3>5. 获取章节上下文窗口</h3>
        <button onclick="testGetContextWindow()">获取上下文窗口</button>
        <div id="contextWindowResult"></div>
    </div>

    <script type="module">
        // 导入上下文分析器
        import { contextAnalyzer } from '/src/lib/context-analyzer.ts';
        import { 
            getNovelContext, 
            getChapterContext, 
            getChapterContextWindow,
            analyzeNovelClient 
        } from '/src/lib/context-client.ts';

        // 将函数暴露到全局作用域以便按钮调用
        window.contextAnalyzer = contextAnalyzer;
        window.getNovelContext = getNovelContext;
        window.getChapterContext = getChapterContext;
        window.getChapterContextWindow = getChapterContextWindow;
        window.analyzeNovelClient = analyzeNovelClient;

        console.log('客户端上下文系统已加载');
        console.log('contextAnalyzer:', contextAnalyzer);
    </script>

    <script>
        function getNovelId() {
            return document.getElementById('novelId').value.trim();
        }

        function getChapterNumber() {
            return parseInt(document.getElementById('chapterNumber').value) || 1;
        }

        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = content;
            element.className = isError ? 'error' : 'success';
        }

        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.innerHTML = '加载中...';
            element.className = 'loading';
        }

        async function testContextAnalyzer() {
            showLoading('contextAnalyzerResult');
            try {
                const result = `
                    <h4>contextAnalyzer 实例信息:</h4>
                    <p>类型: ${typeof window.contextAnalyzer}</p>
                    <p>是否为 null: ${window.contextAnalyzer === null}</p>
                    <p>构造函数: ${window.contextAnalyzer?.constructor?.name || 'N/A'}</p>
                    <p>可用方法: ${window.contextAnalyzer ? Object.getOwnPropertyNames(Object.getPrototypeOf(window.contextAnalyzer)).filter(name => name !== 'constructor').join(', ') : 'N/A'}</p>
                `;
                showResult('contextAnalyzerResult', result);
            } catch (error) {
                showResult('contextAnalyzerResult', `错误: ${error.message}`, true);
            }
        }

        async function testGetNovelContext() {
            const novelId = getNovelId();
            if (!novelId) {
                showResult('novelContextResult', '请输入小说ID', true);
                return;
            }

            showLoading('novelContextResult');
            try {
                const context = await window.getNovelContext(novelId);
                if (context) {
                    const result = `
                        <h4>小说上下文:</h4>
                        <pre>${JSON.stringify(context, null, 2)}</pre>
                    `;
                    showResult('novelContextResult', result);
                } else {
                    showResult('novelContextResult', '未找到小说上下文，可能需要先分析小说', true);
                }
            } catch (error) {
                showResult('novelContextResult', `错误: ${error.message}`, true);
            }
        }

        async function testGetChapterContext() {
            const novelId = getNovelId();
            const chapterNumber = getChapterNumber();
            if (!novelId) {
                showResult('chapterContextResult', '请输入小说ID', true);
                return;
            }

            showLoading('chapterContextResult');
            try {
                const context = await window.getChapterContext(novelId, chapterNumber);
                if (context) {
                    const result = `
                        <h4>第${chapterNumber}章上下文:</h4>
                        <pre>${JSON.stringify(context, null, 2)}</pre>
                    `;
                    showResult('chapterContextResult', result);
                } else {
                    showResult('chapterContextResult', `未找到第${chapterNumber}章上下文，可能需要先分析章节`, true);
                }
            } catch (error) {
                showResult('chapterContextResult', `错误: ${error.message}`, true);
            }
        }

        async function testAnalyzeNovel() {
            const novelId = getNovelId();
            if (!novelId) {
                showResult('analyzeResult', '请输入小说ID', true);
                return;
            }

            showLoading('analyzeResult');
            try {
                const result = await window.analyzeNovelClient(novelId, true);
                const content = `
                    <h4>分析结果:</h4>
                    <p>小说上下文: ${result.novelContext ? '✓' : '✗'}</p>
                    <p>章节上下文数量: ${result.chapterContexts?.length || 0}</p>
                    <details>
                        <summary>详细信息</summary>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </details>
                `;
                showResult('analyzeResult', content);
            } catch (error) {
                showResult('analyzeResult', `错误: ${error.message}`, true);
            }
        }

        async function testGetContextWindow() {
            const novelId = getNovelId();
            const chapterNumber = getChapterNumber();
            if (!novelId) {
                showResult('contextWindowResult', '请输入小说ID', true);
                return;
            }

            showLoading('contextWindowResult');
            try {
                const contexts = await window.getChapterContextWindow(novelId, chapterNumber, 2);
                const result = `
                    <h4>第${chapterNumber}章上下文窗口 (窗口大小: 2):</h4>
                    <p>找到 ${contexts.length} 个相关章节上下文</p>
                    <pre>${JSON.stringify(contexts, null, 2)}</pre>
                `;
                showResult('contextWindowResult', result);
            } catch (error) {
                showResult('contextWindowResult', `错误: ${error.message}`, true);
            }
        }
    </script>
</body>
</html>
