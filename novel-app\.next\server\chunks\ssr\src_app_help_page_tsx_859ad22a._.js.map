{"version": 3, "sources": ["turbopack:///[project]/src/app/help/page.tsx", "turbopack:///[project]/node_modules/lucide-react/src/icons/lightbulb.ts", "turbopack:///[project]/node_modules/lucide-react/src/icons/arrow-left.ts", "turbopack:///[project]/node_modules/lucide-react/src/icons/target.ts"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { ArrowLeft, BookOpen, Wand2, Target, Lightbulb, CheckCircle, XCircle } from 'lucide-react';\n\nexport default function HelpPage() {\n  const [selectedCategory, setSelectedCategory] = useState<string>('basics');\n\n  const categories = {\n    basics: {\n      title: '基础概念',\n      icon: BookOpen,\n      content: (\n        <div className=\"space-y-6\">\n          <section>\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">什么是改写规则？</h3>\n            <p className=\"text-gray-700 mb-4\">\n              改写规则是告诉AI如何修改小说内容的指令。通过详细的规则描述，AI可以按照你的要求对小说进行个性化改写。\n            </p>\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <h4 className=\"font-medium text-blue-800 mb-2\">规则的作用</h4>\n              <ul className=\"text-blue-700 text-sm space-y-1\">\n                <li>• 指导AI理解你的改写需求</li>\n                <li>• 确保改写结果符合你的期望</li>\n                <li>• 保持故事的连贯性和逻辑性</li>\n                <li>• 个性化定制阅读体验</li>\n              </ul>\n            </div>\n          </section>\n\n          <section>\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">如何编写有效的规则？</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                <div className=\"flex items-center mb-2\">\n                  <CheckCircle className=\"text-green-600 mr-2\" size={16} />\n                  <h4 className=\"font-medium text-green-800\">好的规则</h4>\n                </div>\n                <ul className=\"text-green-700 text-sm space-y-1\">\n                  <li>• 具体明确的描述</li>\n                  <li>• 包含具体的改写方向</li>\n                  <li>• 考虑故事的整体性</li>\n                  <li>• 适度的改写幅度</li>\n                </ul>\n              </div>\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                <div className=\"flex items-center mb-2\">\n                  <XCircle className=\"text-red-600 mr-2\" size={16} />\n                  <h4 className=\"font-medium text-red-800\">避免的问题</h4>\n                </div>\n                <ul className=\"text-red-700 text-sm space-y-1\">\n                  <li>• 过于模糊的描述</li>\n                  <li>• 相互矛盾的要求</li>\n                  <li>• 过度的改写要求</li>\n                  <li>• 忽略故事逻辑</li>\n                </ul>\n              </div>\n            </div>\n          </section>\n        </div>\n      )\n    },\n    examples: {\n      title: '规则示例',\n      icon: Wand2,\n      content: (\n        <div className=\"space-y-6\">\n          <section>\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">感情戏增强</h3>\n            <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n              <pre className=\"text-sm text-gray-700 whitespace-pre-wrap\">\n{`请按照以下规则改写：\n1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节\n2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过\n3. 增加角色间的情感张力和暧昧氛围\n4. 保持故事主线不变，但重点突出感情发展\n5. 添加更多的日常生活场景和温馨互动`}\n              </pre>\n            </div>\n          </section>\n\n          <section>\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">毒点清除</h3>\n            <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n              <pre className=\"text-sm text-gray-700 whitespace-pre-wrap\">\n{`请按照以下规则改写：\n1. 完全移除或修改送女、绿帽、圣母等毒点情节\n2. 删除或改写让读者不适的桥段\n3. 修正主角的三观和行为逻辑\n4. 用更合理的情节替代被删除的内容\n5. 确保故事逻辑的完整性和连贯性`}\n              </pre>\n            </div>\n          </section>\n\n          <section>\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">人设优化</h3>\n            <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n              <pre className=\"text-sm text-gray-700 whitespace-pre-wrap\">\n{`请按照以下规则改写：\n1. 修正主角的性格设定，使其更加立体和讨喜\n2. 改善对话风格，使其更加自然流畅\n3. 去除过于中二或不合理的行为描写\n4. 增强角色的智商和情商表现\n5. 保持角色的核心特征，但优化表现方式`}\n              </pre>\n            </div>\n          </section>\n        </div>\n      )\n    },\n    tips: {\n      title: '写作技巧',\n      icon: Target,\n      content: (\n        <div className=\"space-y-6\">\n          <section>\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">规则编写技巧</h3>\n            <div className=\"space-y-4\">\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <h4 className=\"font-medium text-gray-800 mb-2\">1. 使用具体的动词</h4>\n                <p className=\"text-gray-700 text-sm mb-2\">用\"扩写\"、\"删除\"、\"修改\"等具体动词，而不是\"优化\"、\"改善\"等模糊词汇。</p>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm\">\n                  <div className=\"bg-green-50 p-2 rounded\">\n                    <span className=\"text-green-600 font-medium\">好：</span> 扩写男女主对话\n                  </div>\n                  <div className=\"bg-red-50 p-2 rounded\">\n                    <span className=\"text-red-600 font-medium\">差：</span> 优化感情戏\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <h4 className=\"font-medium text-gray-800 mb-2\">2. 设定优先级</h4>\n                <p className=\"text-gray-700 text-sm mb-2\">用数字标号明确各项规则的重要程度。</p>\n                <div className=\"bg-blue-50 p-3 rounded text-sm\">\n                  <div className=\"text-blue-800\">\n                    1. 首要任务：删除毒点情节<br/>\n                    2. 次要任务：扩写感情戏<br/>\n                    3. 可选任务：优化对话风格\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <h4 className=\"font-medium text-gray-800 mb-2\">3. 提供具体例子</h4>\n                <p className=\"text-gray-700 text-sm mb-2\">在规则中包含具体的例子或场景描述。</p>\n                <div className=\"bg-yellow-50 p-3 rounded text-sm\">\n                  <div className=\"text-yellow-800\">\n                    例如：将\"他很强\"改写为具体的实力展现，如\"他一剑斩断了千年古树\"\n                  </div>\n                </div>\n              </div>\n            </div>\n          </section>\n\n          <section>\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">常见问题解决</h3>\n            <div className=\"space-y-3\">\n              <details className=\"border border-gray-200 rounded-lg\">\n                <summary className=\"p-3 cursor-pointer font-medium text-gray-800 hover:bg-gray-50\">\n                  改写结果不符合预期怎么办？\n                </summary>\n                <div className=\"p-3 border-t border-gray-200 text-sm text-gray-700\">\n                  <ul className=\"space-y-1\">\n                    <li>• 检查规则是否足够具体和明确</li>\n                    <li>• 避免相互矛盾的要求</li>\n                    <li>• 尝试分步骤进行改写</li>\n                    <li>• 参考预设规则模板</li>\n                  </ul>\n                </div>\n              </details>\n\n              <details className=\"border border-gray-200 rounded-lg\">\n                <summary className=\"p-3 cursor-pointer font-medium text-gray-800 hover:bg-gray-50\">\n                  如何保持故事的连贯性？\n                </summary>\n                <div className=\"p-3 border-t border-gray-200 text-sm text-gray-700\">\n                  <ul className=\"space-y-1\">\n                    <li>• 在规则中强调保持主线剧情</li>\n                    <li>• 避免大幅修改关键情节</li>\n                    <li>• 注意角色性格的一致性</li>\n                    <li>• 考虑前后章节的衔接</li>\n                  </ul>\n                </div>\n              </details>\n\n              <details className=\"border border-gray-200 rounded-lg\">\n                <summary className=\"p-3 cursor-pointer font-medium text-gray-800 hover:bg-gray-50\">\n                  改写速度很慢怎么办？\n                </summary>\n                <div className=\"p-3 border-t border-gray-200 text-sm text-gray-700\">\n                  <ul className=\"space-y-1\">\n                    <li>• 减少单次改写的章节数量</li>\n                    <li>• 简化规则描述</li>\n                    <li>• 避免在高峰时段使用</li>\n                    <li>• 检查网络连接状态</li>\n                  </ul>\n                </div>\n              </details>\n            </div>\n          </section>\n        </div>\n      )\n    },\n    advanced: {\n      title: '高级技巧',\n      icon: Lightbulb,\n      content: (\n        <div className=\"space-y-6\">\n          <section>\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">组合规则策略</h3>\n            <p className=\"text-gray-700 mb-4\">\n              对于复杂的改写需求，可以将多个简单规则组合使用，分阶段完成改写。\n            </p>\n            <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4\">\n              <h4 className=\"font-medium text-gray-800 mb-3\">分阶段改写示例</h4>\n              <div className=\"space-y-3\">\n                <div className=\"bg-white rounded p-3 border-l-4 border-blue-500\">\n                  <div className=\"font-medium text-blue-800\">第一阶段：清理内容</div>\n                  <div className=\"text-sm text-gray-700\">删除毒点情节，修正逻辑错误</div>\n                </div>\n                <div className=\"bg-white rounded p-3 border-l-4 border-green-500\">\n                  <div className=\"font-medium text-green-800\">第二阶段：增强内容</div>\n                  <div className=\"text-sm text-gray-700\">扩写感情戏，增加互动描写</div>\n                </div>\n                <div className=\"bg-white rounded p-3 border-l-4 border-purple-500\">\n                  <div className=\"font-medium text-purple-800\">第三阶段：优化细节</div>\n                  <div className=\"text-sm text-gray-700\">改善对话风格，调整节奏</div>\n                </div>\n              </div>\n            </div>\n          </section>\n\n          <section>\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">针对性改写</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <h4 className=\"font-medium text-gray-800 mb-2\">都市小说</h4>\n                <ul className=\"text-sm text-gray-700 space-y-1\">\n                  <li>• 现代化语言表达</li>\n                  <li>• 职场/校园场景描写</li>\n                  <li>• 现实感情发展</li>\n                  <li>• 社会背景融入</li>\n                </ul>\n              </div>\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <h4 className=\"font-medium text-gray-800 mb-2\">玄幻小说</h4>\n                <ul className=\"text-sm text-gray-700 space-y-1\">\n                  <li>• 修炼体系完善</li>\n                  <li>• 世界观构建</li>\n                  <li>• 战斗场面描写</li>\n                  <li>• 境界提升逻辑</li>\n                </ul>\n              </div>\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <h4 className=\"font-medium text-gray-800 mb-2\">历史小说</h4>\n                <ul className=\"text-sm text-gray-700 space-y-1\">\n                  <li>• 历史背景考证</li>\n                  <li>• 古代语言风格</li>\n                  <li>• 政治军事描写</li>\n                  <li>• 文化细节还原</li>\n                </ul>\n              </div>\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <h4 className=\"font-medium text-gray-800 mb-2\">言情小说</h4>\n                <ul className=\"text-sm text-gray-700 space-y-1\">\n                  <li>• 情感细腻描写</li>\n                  <li>• 心理活动刻画</li>\n                  <li>• 浪漫场景营造</li>\n                  <li>• 角色魅力塑造</li>\n                </ul>\n              </div>\n            </div>\n          </section>\n\n          <section>\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">质量控制</h3>\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <h4 className=\"font-medium text-yellow-800 mb-2\">改写质量检查清单</h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm\">\n                <div>\n                  <div className=\"font-medium text-yellow-800 mb-1\">内容质量</div>\n                  <ul className=\"text-yellow-700 space-y-1\">\n                    <li>□ 故事逻辑完整</li>\n                    <li>□ 角色行为合理</li>\n                    <li>□ 情节发展自然</li>\n                    <li>□ 语言表达流畅</li>\n                  </ul>\n                </div>\n                <div>\n                  <div className=\"font-medium text-yellow-800 mb-1\">规则执行</div>\n                  <ul className=\"text-yellow-700 space-y-1\">\n                    <li>□ 改写目标达成</li>\n                    <li>□ 重点内容突出</li>\n                    <li>□ 不需要内容简化</li>\n                    <li>□ 整体风格统一</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </section>\n        </div>\n      )\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* 头部 */}\n        <div className=\"mb-8\">\n          <Link \n            href=\"/\"\n            className=\"inline-flex items-center text-blue-600 hover:text-blue-800 mb-4\"\n          >\n            <ArrowLeft className=\"mr-2\" size={20} />\n            返回主页\n          </Link>\n          <h1 className=\"text-3xl font-bold text-gray-800\">改写规则帮助</h1>\n          <p className=\"text-gray-600 mt-2\">学习如何编写有效的改写规则，获得更好的改写效果</p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n          {/* 侧边栏 */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-lg shadow-md p-4 sticky top-4\">\n              <h2 className=\"font-semibold text-gray-800 mb-4\">目录</h2>\n              <nav className=\"space-y-2\">\n                {Object.entries(categories).map(([key, category]) => {\n                  const Icon = category.icon;\n                  return (\n                    <button\n                      key={key}\n                      onClick={() => setSelectedCategory(key)}\n                      className={`w-full text-left p-3 rounded-lg transition-colors flex items-center ${\n                        selectedCategory === key\n                          ? 'bg-blue-100 text-blue-800 border border-blue-200'\n                          : 'hover:bg-gray-100 text-gray-700'\n                      }`}\n                    >\n                      <Icon className=\"mr-3\" size={18} />\n                      {category.title}\n                    </button>\n                  );\n                })}\n              </nav>\n            </div>\n          </div>\n\n          {/* 主内容 */}\n          <div className=\"lg:col-span-3\">\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <div className=\"flex items-center mb-6\">\n                {React.createElement(categories[selectedCategory as keyof typeof categories].icon, {\n                  className: \"mr-3 text-blue-600\",\n                  size: 24\n                })}\n                <h2 className=\"text-2xl font-bold text-gray-800\">\n                  {categories[selectedCategory as keyof typeof categories].title}\n                </h2>\n              </div>\n              {categories[selectedCategory as keyof typeof categories].content}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5',\n      key: '1gvzjb',\n    },\n  ],\n  ['path', { d: 'M9 18h6', key: 'x1upvd' }],\n  ['path', { d: 'M10 22h4', key: 'ceow96' }],\n];\n\n/**\n * @component @name Lightbulb\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) - https://lucide.dev/icons/lightbulb\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Lightbulb = createLucideIcon('lightbulb', __iconNode);\n\nexport default Lightbulb;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['circle', { cx: '12', cy: '12', r: '6', key: '1vlfrh' }],\n  ['circle', { cx: '12', cy: '12', r: '2', key: '1c9p78' }],\n];\n\n/**\n * @component @name Target\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI2IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/target\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Target = createLucideIcon('target', __iconNode);\n\nexport default Target;\n"], "names": [], "mappings": "qFAEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,oBEgBA,IAAA,EAAA,CAAA,EAAA,EAAA,OAAA,EAAA,sBAfW,CAAE,CCAA,CDAG,CCAD,ADAC,CAAA,ACAD,eDAmB,IAAK,CCAD,ADAC,CCAD,ADAC,CCAD,ADAC,CCAD,ADAC,MAAU,CCAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EDCtC,CCAR,ADAQ,EAAK,CAAA,ACAD,CAAA,ADAC,uBAA2B,ECAA,CHD3C,AGC2C,IHD3C,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,yBGgBe,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAhBrC,AAgBqC,GAhBjC,CDAH,ACAG,CAAA,ADAH,CCAG,ADAH,CCAG,ADAH,CAAA,GCAa,CDAb,CAAA,CAAA,CAAA,ACAa,YAAoB,QAAA,CAAU,CAAA,YAC5C,GAAI,CAAA,ADAH,CCAG,ADAH,CAAA,ACAG,CAAA,ADAH,CAAA,ACAS,CDAT,ACAS,CDAT,ACAS,CAAI,CDAD,CAAA,CAAA,ECAO,CAAA,ADAF,CAAA,QCAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,WAC7C,CAAE,CAAA,CAAA,CAAA,AAAI,KAAM,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,AAAM,CAAA,CAAA,AAAG,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,GFoBpD,EAAA,CAAA,EAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAtBjC,AAsB8C,CArB5C,AAqB4C,CCrB7C,ADAC,AAqB4C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,AAnBjD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,ACYH,CDZG,ACYH,CAAA,ADZG,mGACE,CEYL,AFZK,OAAA,EAET,CACA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACxC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAE,AAAF,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAY,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAC3C,EDTA,IAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACtB,GAAM,CAAC,EAAkB,EAAoB,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAiB,UAE3D,EAAa,CACjB,OAAQ,CACN,MAAO,OACP,KAAM,EAAA,QAAQ,CACd,QACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDAA2C,aACzD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BAAqB,yDAGlC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6DACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0CAAiC,UAC/C,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,4CACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,mBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,mBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,mBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,yBAKV,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDAA2C,eACzD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,sBAAsB,KAAM,KACnD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,sCAA6B,YAE7C,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,6CACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,cACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,gBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,eACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,oBAGR,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAO,CAAA,CAAC,UAAU,oBAAoB,KAAM,KAC7C,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oCAA2B,aAE3C,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,2CACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,cACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,cACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,cACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,2BAOlB,EACA,SAAU,CACR,MAAO,OACP,KAAM,EAAA,KAAK,CACX,QACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDAA2C,UACzD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4DACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qDAC5B,CAAC;;;;;mBAKiB,CAAC,QAKV,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDAA2C,SACzD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4DACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qDAC5B,CAAC;;;;;iBAKe,CAAC,QAKR,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDAA2C,SACzD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4DACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qDAC5B,CAAC;;;;;oBAKkB,CAAC,UAMjB,EACA,KAAM,CACJ,MAAO,OACP,KAAM,EACN,QACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDAA2C,WACzD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0CAAiC,eAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,4CAC1C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,sCAA6B,OAAS,cAExD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kCACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAA2B,OAAS,kBAK1D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0CAAiC,aAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,sBAC1C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0BAAgB,iBACf,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAA,GAAI,gBACN,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAA,GAAI,yBAMxB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0CAAiC,cAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,sBAC1C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2BAAkB,iDAQzC,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDAA2C,WACzD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,CAAQ,UAAU,8CACjB,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,yEAAgE,kBAGnF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8DACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,sBACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,oBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,gBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,gBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,uBAKV,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,CAAQ,UAAU,8CACjB,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,yEAAgE,gBAGnF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8DACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,sBACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,mBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,iBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,iBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,wBAKV,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,CAAQ,UAAU,8CACjB,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,yEAAgE,eAGnF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8DACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,sBACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,kBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,gBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,+BAQpB,EACA,SAAU,CACR,MAAO,OACP,KAAM,EACN,QACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDAA2C,WACzD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BAAqB,qCAGlC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6FACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0CAAiC,YAC/C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4DACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qCAA4B,cAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAwB,qBAEzC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6DACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sCAA6B,cAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAwB,oBAEzC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8DACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCAA8B,cAC7C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAwB,4BAM/C,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDAA2C,UACzD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0CAAiC,SAC/C,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,4CACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,cACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,gBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,mBAGR,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0CAAiC,SAC/C,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,4CACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,YACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,mBAGR,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0CAAiC,SAC/C,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,4CACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,mBAGR,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0CAAiC,SAC/C,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,4CACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,yBAMZ,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDAA2C,SACzD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iEACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4CAAmC,aACjD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CAAmC,SAClD,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,sCACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,mBAGR,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CAAmC,SAClD,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,sCACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,aACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,cACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,8BAQpB,CACF,EAEA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,IACL,UAAU,4EAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAU,UAAU,OAAO,KAAM,KAAM,UAG1C,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4CAAmC,WACjD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BAAqB,+BAGpC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2DACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4CAAmC,OACjD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,OAAO,OAAO,CAAC,GAAY,GAAG,CAAC,CAAC,CAAC,EAAK,EAAS,IAC9C,IAAM,EAAO,EAAS,IAAI,CAC1B,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CAEC,QAAS,IAAM,EAAoB,GACnC,UAAW,CAAC,oEAAoE,EAC9E,IAAqB,EACjB,mDACA,kCAAA,CACJ,WAEF,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAK,UAAU,OAAO,KAAM,KAC5B,EAAS,KAAK,GATV,EAYX,UAMN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mCACZ,EAAA,OAAK,CAAC,aAAa,CAAC,CAAU,CAAC,EAA4C,CAAC,IAAI,CAAE,CACjF,UAAW,qBACX,KAAM,EACR,GACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4CACX,CAAU,CAAC,EAA4C,CAAC,KAAK,MAGjE,CAAU,CAAC,EAA4C,CAAC,OAAO,aAO9E", "ignoreList": [1, 2, 3]}