{"version": 3, "sources": ["turbopack:///[project]/src/lib/gemini.ts"], "sourcesContent": ["// Gemini API 集成 - 多Key池管理\nconst API_KEYS = [\n  {\n    key: 'AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw', // My First Project - 4倍强度\n    name: 'My First Project',\n    weight: 1, // 权重，表示相对强度\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0, // 冷却时间\n  },\n  {\n    key: 'AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y', // ankibot\n    name: 'ankibot',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY', // Generative Language Client\n    name: 'Generative Language Client',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc', // In The Novel\n    name: 'In The Novel',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk', // chat\n    name: 'chat',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  }\n];\n\n// API配置\nconst getGeminiApiUrl = (model: string = 'gemini-2.5-flash-lite') =>\n  `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;\nconst REQUEST_DELAY = 1000; // 请求间隔（毫秒）\nconst COOLDOWN_DURATION = 60000; // 429错误后的冷却时间（毫秒）\nconst MAX_RETRIES = 5; // 增加最大重试次数\nconst EXPONENTIAL_BACKOFF_BASE = 2000; // 指数退避基础时间（毫秒）\nconst MAX_WAIT_TIME = 30000; // 最大等待时间（毫秒）\n\n// API Key管理类\nclass ApiKeyManager {\n  private keys = [...API_KEYS];\n\n  // 获取最佳可用的API Key\n  getBestAvailableKey() {\n    const now = Date.now();\n\n    // 过滤掉冷却中的key\n    const availableKeys = this.keys.filter(key => key.cooldownUntil <= now);\n\n    if (availableKeys.length === 0) {\n      // 如果所有key都在冷却中，返回冷却时间最短的\n      return this.keys.reduce((min, key) =>\n        key.cooldownUntil < min.cooldownUntil ? key : min\n      );\n    }\n\n    // 根据权重和使用频率选择最佳key\n    const bestKey = availableKeys.reduce((best, key) => {\n      const keyScore = key.weight / (key.requestCount + 1);\n      const bestScore = best.weight / (best.requestCount + 1);\n      return keyScore > bestScore ? key : best;\n    });\n\n    return bestKey;\n  }\n\n  // 记录API使用\n  recordUsage(keyName: string, success: boolean) {\n    const key = this.keys.find(k => k.name === keyName);\n    if (key) {\n      key.requestCount++;\n      key.lastUsed = Date.now();\n\n      if (!success) {\n        // 如果失败，设置冷却时间\n        key.cooldownUntil = Date.now() + COOLDOWN_DURATION;\n      }\n    }\n  }\n\n  // 获取统计信息\n  getStats() {\n    return this.keys.map(key => ({\n      name: key.name,\n      requestCount: key.requestCount,\n      weight: key.weight,\n      isAvailable: key.cooldownUntil <= Date.now(),\n      cooldownRemaining: Math.max(0, key.cooldownUntil - Date.now()),\n    }));\n  }\n}\n\nconst keyManager = new ApiKeyManager();\n\nexport interface RewriteRequest {\n  originalText: string;\n  rules: string;\n  chapterTitle?: string;\n  chapterNumber?: number;\n  model?: string;\n  // 上下文信息\n  novelContext?: {\n    summary: string;\n    mainCharacters: Array<{\n      name: string;\n      role: string;\n      description: string;\n      relationships?: string;\n    }>;\n    worldSetting: string;\n    writingStyle: string;\n    tone: string;\n  };\n  chapterContext?: {\n    previousChapterSummary?: string;\n    keyEvents: string[];\n    characterStates: Array<{\n      name: string;\n      status: string;\n      emotions: string;\n      relationships: string;\n    }>;\n    plotProgress: string;\n    contextualNotes: string;\n  };\n}\n\nexport interface RewriteResponse {\n  rewrittenText: string;\n  success: boolean;\n  error?: string;\n  apiKeyUsed?: string;\n  tokensUsed?: number;\n  model?: string;\n  processingTime?: number;\n  detailedError?: string; // 新增详细错误信息\n  retryCount?: number; // 新增重试次数记录\n}\n\n// 构建改写提示词\nfunction buildPrompt(request: RewriteRequest): string {\n  const { originalText, rules, chapterTitle, chapterNumber, novelContext, chapterContext } = request;\n\n  let prompt = `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：\n\n改写规则：\n${rules}\n\n${chapterTitle ? `当前章节：${chapterTitle}` : ''}`;\n\n  // 添加小说整体上下文\n  if (novelContext) {\n    prompt += `\n\n【小说背景信息】\n小说摘要：${novelContext.summary}\n\n主要人物：\n${novelContext.mainCharacters.map(char =>\n      `- ${char.name}(${char.role}): ${char.description}${char.relationships ? ` | 关系：${char.relationships}` : ''}`\n    ).join('\\n')}\n\n世界观设定：${novelContext.worldSetting}\n\n写作风格：${novelContext.writingStyle}\n\n整体语调：${novelContext.tone}`;\n  }\n\n  // 添加章节上下文\n  if (chapterContext) {\n    prompt += `\n\n【章节上下文信息】`;\n\n    if (chapterContext.previousChapterSummary) {\n      prompt += `\n前一章摘要：${chapterContext.previousChapterSummary}`;\n    }\n\n    if (chapterContext.keyEvents.length > 0) {\n      prompt += `\n本章关键事件：${chapterContext.keyEvents.join('、')}`;\n    }\n\n    if (chapterContext.characterStates.length > 0) {\n      prompt += `\n人物状态：\n${chapterContext.characterStates.map(state =>\n        `- ${state.name}: ${state.status} | 情感：${state.emotions} | 关系：${state.relationships}`\n      ).join('\\n')}`;\n    }\n\n    prompt += `\n情节推进：${chapterContext.plotProgress}`;\n\n    if (chapterContext.contextualNotes) {\n      prompt += `\n重要注释：${chapterContext.contextualNotes}`;\n    }\n  }\n\n  prompt += `\n\n原文内容：\n${originalText}\n\n请严格按照改写规则进行改写，同时保持与小说整体背景和上下文的连贯性。改写后的内容应该：\n1. 遵循所有指定的改写规则\n2. 保持与小说整体背景的一致性\n3. 确保人物性格和关系的连贯性\n4. 保持情节发展的逻辑性\n5. 维持原有的写作风格和语调\n6. 确保文字流畅自然\n\n请直接输出改写后的内容，不要添加任何解释或说明：`;\n\n  return prompt;\n}\n\n// 调用Gemini API进行文本改写 - 增强错误处理和重试机制\nexport async function rewriteText(request: RewriteRequest): Promise<RewriteResponse> {\n  const startTime = Date.now();\n  let lastError = '';\n  let lastDetailedError = '';\n\n  for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {\n    try {\n      const apiKey = keyManager.getBestAvailableKey();\n\n      // 智能等待策略：如果key在冷却中，使用指数退避\n      if (apiKey.cooldownUntil > Date.now()) {\n        const cooldownWait = Math.min(apiKey.cooldownUntil - Date.now(), MAX_WAIT_TIME);\n        const exponentialWait = Math.min(EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt), MAX_WAIT_TIME);\n        const waitTime = Math.max(cooldownWait, exponentialWait);\n\n        console.log(`等待 ${waitTime}ms (尝试 ${attempt + 1}/${MAX_RETRIES}, API Key: ${apiKey.name})`);\n        await new Promise(resolve => setTimeout(resolve, waitTime));\n      }\n\n      const prompt = buildPrompt(request);\n\n      // 增加请求超时设置\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60秒超时\n\n      const apiUrl = getGeminiApiUrl(request.model);\n      const response = await fetch(`${apiUrl}?key=${apiKey.key}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          contents: [{\n            parts: [{\n              text: prompt\n            }]\n          }],\n          generationConfig: {\n            temperature: 0.6,\n            topK: 10,\n            topP: 0.8,\n            \"thinkingConfig\": {\n              \"thinkingBudget\": 0\n            }\n          },\n        }),\n        signal: controller.signal,\n      });\n\n      clearTimeout(timeoutId);\n      const processingTime = Date.now() - startTime;\n\n      // 处理429错误（API限流）\n      if (response.status === 429) {\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = `API限流 (${apiKey.name})`;\n        lastDetailedError = `第${attempt + 1}次尝试: API Key \"${apiKey.name}\" 遇到限流，状态码: 429`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          const retryDelay = EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt);\n          console.log(`API限流，${retryDelay}ms后重试...`);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n      }\n\n      // 处理其他HTTP错误\n      if (!response.ok) {\n        const errorData = await response.text();\n        console.error('Gemini API error:', errorData);\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = `API请求失败: ${response.status} ${response.statusText}`;\n        lastDetailedError = `第${attempt + 1}次尝试: HTTP ${response.status} ${response.statusText}, 响应: ${errorData.substring(0, 200)}`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          const retryDelay = REQUEST_DELAY * (attempt + 1);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n        };\n      }\n\n      // 解析响应数据\n      let data;\n      try {\n        data = await response.json();\n      } catch (parseError) {\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = 'JSON解析失败';\n        lastDetailedError = `第${attempt + 1}次尝试: 无法解析API响应为JSON, 错误: ${parseError instanceof Error ? parseError.message : '未知错误'}`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          const retryDelay = REQUEST_DELAY * (attempt + 1);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n          retryCount: attempt + 1,\n        };\n      }\n\n      // 记录成功使用\n      keyManager.recordUsage(apiKey.name, true);\n\n      // 增强响应验证\n      if (!data.candidates || data.candidates.length === 0) {\n        lastError = '没有收到有效的响应内容';\n        lastDetailedError = `第${attempt + 1}次尝试: API响应中没有candidates字段或为空数组, 完整响应: ${JSON.stringify(data).substring(0, 500)}`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          keyManager.recordUsage(apiKey.name, false); // 标记为失败，触发冷却\n          const retryDelay = EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n          retryCount: attempt + 1,\n        };\n      }\n\n      const candidate = data.candidates[0];\n\n      if (candidate.finishReason === 'SAFETY') {\n        return {\n          rewrittenText: '',\n          success: false,\n          error: '内容被安全过滤器拦截，请调整改写规则或原文内容',\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: `内容被安全过滤器拦截，finishReason: SAFETY`,\n          retryCount: attempt + 1,\n        };\n      }\n\n      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {\n        lastError = '响应内容格式错误';\n        lastDetailedError = `第${attempt + 1}次尝试: candidate内容格式错误, candidate: ${JSON.stringify(candidate).substring(0, 300)}`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          keyManager.recordUsage(apiKey.name, false);\n          const retryDelay = REQUEST_DELAY * (attempt + 1);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n          retryCount: attempt + 1,\n        };\n      }\n\n      const rewrittenText = candidate.content.parts[0].text;\n\n      // 验证生成的内容质量\n      if (!rewrittenText || rewrittenText.trim().length < 10) {\n        lastError = '生成的内容过短或为空';\n        lastDetailedError = `第${attempt + 1}次尝试: 生成的内容长度: ${rewrittenText?.length || 0}, 内容: \"${rewrittenText?.substring(0, 100) || 'null'}\"`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          keyManager.recordUsage(apiKey.name, false);\n          const retryDelay = REQUEST_DELAY * (attempt + 1);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n          detailedError: lastDetailedError,\n          retryCount: attempt + 1,\n        };\n      }\n\n      // 尝试从响应中提取token使用信息\n      const tokensUsed = data.usageMetadata?.totalTokenCount || 0;\n\n      return {\n        rewrittenText: rewrittenText.trim(),\n        success: true,\n        apiKeyUsed: apiKey.name,\n        tokensUsed,\n        model: request.model || 'gemini-2.5-flash-lite',\n        processingTime,\n        retryCount: attempt + 1,\n      };\n\n    } catch (error) {\n      console.error('Gemini API调用错误:', error);\n\n      // 处理不同类型的错误\n      if (error instanceof Error && error.name === 'AbortError') {\n        lastError = '请求超时';\n        lastDetailedError = `第${attempt + 1}次尝试: 请求超时 (60秒)`;\n      } else {\n        lastError = `网络错误: ${error instanceof Error ? error.message : '未知错误'}`;\n        lastDetailedError = `第${attempt + 1}次尝试: ${error instanceof Error ? error.stack || error.message : '未知网络错误'}`;\n      }\n\n      if (attempt < MAX_RETRIES - 1) {\n        const retryDelay = EXPONENTIAL_BACKOFF_BASE * Math.pow(2, attempt);\n        console.log(`网络错误，${retryDelay}ms后重试...`);\n        await new Promise(resolve => setTimeout(resolve, retryDelay));\n      }\n    }\n  }\n\n  return {\n    rewrittenText: '',\n    success: false,\n    error: `重试${MAX_RETRIES}次后仍然失败: ${lastError}`,\n    processingTime: Date.now() - startTime,\n    detailedError: lastDetailedError,\n    retryCount: MAX_RETRIES,\n  };\n}\n\n// 改进的批量改写函数 - 支持实时写入、详细进度跟踪和失败恢复\nexport async function rewriteChapters(\n  chapters: Array<{ content: string; title: string; number: number }>,\n  rules: string,\n  onProgress?: (progress: number, currentChapter: number, details?: any) => void,\n  onChapterComplete?: (chapterIndex: number, result: any) => void,\n  concurrency: number = 3, // 降低并发数以避免429错误\n  model: string = 'gemini-2.5-flash-lite', // 模型选择\n  enableFailureRecovery: boolean = true // 启用失败恢复机制\n): Promise<Array<{ success: boolean; content: string; error?: string; details?: any }>> {\n  const results: Array<{ success: boolean; content: string; error?: string; details?: any }> = new Array(chapters.length);\n  let completed = 0;\n  let totalTokensUsed = 0;\n  const startTime = Date.now();\n\n  // 使用更保守的并发策略\n  const semaphore = new Semaphore(concurrency);\n\n  const processChapter = async (chapter: { content: string; title: string; number: number }, index: number) => {\n    await semaphore.acquire();\n    const chapterStartTime = Date.now();\n\n    try {\n\n      const result = await rewriteText({\n        originalText: chapter.content,\n        rules,\n        chapterTitle: chapter.title,\n        chapterNumber: chapter.number,\n        model,\n      });\n\n      const chapterProcessingTime = Date.now() - chapterStartTime;\n\n      if (result.tokensUsed) {\n        totalTokensUsed += result.tokensUsed;\n      }\n\n      const chapterResult = {\n        success: result.success,\n        content: result.rewrittenText,\n        error: result.error,\n        details: {\n          apiKeyUsed: result.apiKeyUsed,\n          tokensUsed: result.tokensUsed,\n          model: result.model,\n          processingTime: chapterProcessingTime,\n          chapterNumber: chapter.number,\n          chapterTitle: chapter.title,\n        }\n      };\n\n      results[index] = chapterResult;\n      completed++;\n\n      // 实时回调章节完成\n      if (onChapterComplete) {\n        onChapterComplete(index, chapterResult);\n      }\n\n      // 更新进度，包含详细信息\n      if (onProgress) {\n        const progressDetails = {\n          completed,\n          total: chapters.length,\n          totalTokensUsed,\n          totalTime: Date.now() - startTime,\n          averageTimePerChapter: (Date.now() - startTime) / completed,\n          apiKeyStats: keyManager.getStats(),\n          currentChapter: {\n            number: chapter.number,\n            title: chapter.title,\n            processingTime: chapterProcessingTime,\n            apiKey: result.apiKeyUsed,\n            tokens: result.tokensUsed,\n          }\n        };\n\n        onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n      }\n\n      // 添加请求间隔\n      await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));\n\n      return result;\n    } catch (error) {\n      const chapterErrorTime = Date.now();\n      const errorResult = {\n        success: false,\n        content: '',\n        error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        details: {\n          chapterNumber: chapter.number,\n          chapterTitle: chapter.title,\n          processingTime: chapterErrorTime - chapterStartTime,\n        }\n      };\n\n      results[index] = errorResult;\n      completed++;\n\n      if (onChapterComplete) {\n        onChapterComplete(index, errorResult);\n      }\n\n      if (onProgress) {\n        const progressDetails = {\n          completed,\n          total: chapters.length,\n          totalTokensUsed,\n          totalTime: Date.now() - startTime,\n          averageTimePerChapter: (Date.now() - startTime) / completed,\n          apiKeyStats: keyManager.getStats(),\n          currentChapter: {\n            number: chapter.number,\n            title: chapter.title,\n            error: error instanceof Error ? error.message : '未知错误',\n          }\n        };\n\n        onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n      }\n\n      return null;\n    } finally {\n      semaphore.release();\n    }\n  };\n\n  // 并发处理所有章节\n  const promises = chapters.map((chapter, index) => processChapter(chapter, index));\n  await Promise.all(promises);\n\n  // 失败恢复机制：对失败的章节进行额外重试\n  if (enableFailureRecovery) {\n    const failedChapters = results\n      .map((result, index) => ({ result, index, chapter: chapters[index] }))\n      .filter(item => !item.result.success);\n\n    if (failedChapters.length > 0) {\n      console.log(`开始恢复 ${failedChapters.length} 个失败的章节...`);\n\n      // 为失败恢复使用更保守的设置\n      const recoverySemaphore = new Semaphore(1); // 串行处理失败的章节\n\n      for (const { index, chapter } of failedChapters) {\n        await recoverySemaphore.acquire();\n\n        try {\n          console.log(`正在恢复第 ${chapter.number} 章: ${chapter.title}`);\n\n          // 等待更长时间再重试\n          await new Promise(resolve => setTimeout(resolve, 5000));\n\n          const recoveryResult = await rewriteText({\n            originalText: chapter.content,\n            rules,\n            chapterTitle: chapter.title,\n            chapterNumber: chapter.number,\n            model,\n          });\n\n          if (recoveryResult.success) {\n            console.log(`成功恢复第 ${chapter.number} 章`);\n\n            const recoveredChapterResult = {\n              success: true,\n              content: recoveryResult.rewrittenText,\n              error: undefined,\n              details: {\n                ...recoveryResult,\n                chapterNumber: chapter.number,\n                chapterTitle: chapter.title,\n                isRecovered: true, // 标记为恢复的章节\n              }\n            };\n\n            results[index] = recoveredChapterResult;\n            completed++;\n\n            // 通知章节恢复完成\n            if (onChapterComplete) {\n              onChapterComplete(index, recoveredChapterResult);\n            }\n\n            // 更新进度\n            if (onProgress) {\n              const progressDetails = {\n                completed,\n                total: chapters.length,\n                totalTokensUsed: totalTokensUsed + (recoveryResult.tokensUsed || 0),\n                totalTime: Date.now() - startTime,\n                averageTimePerChapter: (Date.now() - startTime) / completed,\n                apiKeyStats: keyManager.getStats(),\n                currentChapter: {\n                  number: chapter.number,\n                  title: chapter.title,\n                  processingTime: recoveryResult.processingTime,\n                  apiKey: recoveryResult.apiKeyUsed,\n                  tokens: recoveryResult.tokensUsed,\n                  isRecovered: true,\n                }\n              };\n\n              onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n            }\n          } else {\n            console.log(`第 ${chapter.number} 章恢复失败: ${recoveryResult.error}`);\n            // 更新失败信息，包含恢复尝试的详细信息\n            results[index] = {\n              ...results[index],\n              error: `原始失败: ${results[index].error}; 恢复失败: ${recoveryResult.error}`,\n              details: {\n                ...results[index].details,\n                recoveryAttempted: true,\n                recoveryError: recoveryResult.error,\n                recoveryDetailedError: recoveryResult.detailedError,\n              }\n            };\n          }\n        } catch (error) {\n          console.error(`恢复第 ${chapter.number} 章时发生异常:`, error);\n          results[index] = {\n            ...results[index],\n            error: `${results[index].error}; 恢复异常: ${error instanceof Error ? error.message : '未知错误'}`,\n            details: {\n              ...results[index].details,\n              recoveryAttempted: true,\n              recoveryException: error instanceof Error ? error.message : '未知错误',\n            }\n          };\n        } finally {\n          recoverySemaphore.release();\n        }\n      }\n    }\n  }\n\n  return results;\n}\n\n// 信号量类，用于控制并发\nclass Semaphore {\n  private permits: number;\n  private waitQueue: Array<() => void> = [];\n\n  constructor(permits: number) {\n    this.permits = permits;\n  }\n\n  async acquire(): Promise<void> {\n    if (this.permits > 0) {\n      this.permits--;\n      return Promise.resolve();\n    }\n\n    return new Promise<void>((resolve) => {\n      this.waitQueue.push(resolve);\n    });\n  }\n\n  release(): void {\n    this.permits++;\n    if (this.waitQueue.length > 0) {\n      const resolve = this.waitQueue.shift();\n      if (resolve) {\n        this.permits--;\n        resolve();\n      }\n    }\n  }\n}\n\n// 测试API连接 - 增强版\nexport async function testGeminiConnection(): Promise<{\n  success: boolean;\n  error?: string;\n  details?: any;\n}> {\n  try {\n    const testResult = await rewriteText({\n      originalText: '这是一个测试文本。',\n      rules: '保持原文不变',\n    });\n\n    return {\n      success: testResult.success,\n      error: testResult.error,\n      details: {\n        apiKeyUsed: testResult.apiKeyUsed,\n        tokensUsed: testResult.tokensUsed,\n        model: testResult.model,\n        processingTime: testResult.processingTime,\n        apiKeyStats: keyManager.getStats(),\n      }\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,\n      details: {\n        apiKeyStats: keyManager.getStats(),\n      }\n    };\n  }\n}\n\n// 获取API Key使用统计\nexport function getApiKeyStats() {\n  return keyManager.getStats();\n}\n\n// 重置API Key统计\nexport function resetApiKeyStats() {\n  API_KEYS.forEach(key => {\n    key.requestCount = 0;\n    key.lastUsed = 0;\n    key.cooldownUntil = 0;\n  });\n}\n\n// 预设的改写规则模板\nexport let PRESET_RULES: Record<string, { name: string; description: string; rules: string }> = {\n  romance_focus: {\n    name: '感情戏增强',\n    description: '扩写男女主互动内容，对非感情戏部分一笔带过',\n    rules: `请按照以下规则改写：\n1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节\n2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过\n3. 增加角色间的情感张力和暧昧氛围\n4. 保持故事主线不变，但重点突出感情发展`\n  },\n\n  character_fix: {\n    name: '人设修正',\n    description: '修正主角人设和对话风格',\n    rules: `请按照以下规则改写：\n1. 修正主角的性格设定，使其更加立体和讨喜\n2. 改善对话风格，使其更加自然流畅\n3. 去除过于中二或不合理的行为描写\n4. 保持角色的核心特征，但优化表现方式`\n  },\n\n  toxic_content_removal: {\n    name: '毒点清除',\n    description: '移除送女、绿帽等毒点情节',\n    rules: `请按照以下规则改写：\n1. 完全移除或修改送女、绿帽、圣母等毒点情节\n2. 删除或改写让读者不适的桥段\n3. 保持故事逻辑的完整性\n4. 用更合理的情节替代被删除的内容`\n  },\n\n  pacing_improvement: {\n    name: '节奏优化',\n    description: '优化故事节奏，删除拖沓内容',\n    rules: `请按照以下规则改写：\n1. 删除重复和拖沓的描写\n2. 加快故事节奏，突出重点情节\n3. 简化过于冗长的对话和心理描写\n4. 保持故事的紧凑性和可读性`\n  },\n\n  custom: {\n    name: '自定义规则',\n    description: '用户自定义的改写规则',\n    rules: ''\n  }\n};\n\n// 从数据库加载自定义预设并合并到 PRESET_RULES（仅在服务端使用）\nexport function loadCustomPresets() {\n  // 这个函数只在服务端使用，客户端组件应该通过 API 获取预设\n  if (typeof window !== 'undefined') {\n    console.warn('loadCustomPresets should not be called on client side');\n    return;\n  }\n\n  try {\n    const { presetDb } = require('@/lib/database');\n    const customPresets = presetDb.getAll();\n\n    // 将数据库中的预设添加到 PRESET_RULES\n    customPresets.forEach((preset: any) => {\n      PRESET_RULES[`custom_${preset.id}`] = {\n        name: preset.name,\n        description: preset.description,\n        rules: preset.rules\n      };\n    });\n  } catch (error) {\n    console.error('加载自定义预设失败:', error);\n  }\n}\n\n// 添加自定义预设规则（保持向后兼容）\nexport function addCustomPreset(name: string, description: string, rules: string): string {\n  const key = `custom_${Date.now()}`;\n  PRESET_RULES = {\n    ...PRESET_RULES,\n    [key]: {\n      name,\n      description,\n      rules\n    }\n  };\n  return key;\n}\n\n// 带上下文的重写函数（仅服务端使用）\nexport async function rewriteTextWithContext(\n  novelId: string,\n  chapterNumber: number,\n  originalText: string,\n  rules: string,\n  chapterTitle?: string,\n  model?: string\n): Promise<RewriteResponse> {\n  // 检查是否在服务端环境\n  if (typeof window !== 'undefined') {\n    console.warn('rewriteTextWithContext should only be used on server side');\n    // 在客户端环境下回退到普通重写\n    return await rewriteText({\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model\n    });\n  }\n\n  try {\n    // 动态导入避免循环依赖，只在服务端执行\n    const { novelContextDb, chapterContextDb } = require('./database');\n\n    // 获取小说整体上下文\n    const novelContext = novelContextDb.getByNovelId(novelId);\n\n    // 获取章节上下文\n    const chapterContext = chapterContextDb.getByChapter(novelId, chapterNumber);\n\n    // 构建请求\n    const request: RewriteRequest = {\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model,\n      novelContext: novelContext ? {\n        summary: novelContext.summary,\n        mainCharacters: novelContext.mainCharacters,\n        worldSetting: novelContext.worldSetting,\n        writingStyle: novelContext.writingStyle,\n        tone: novelContext.tone\n      } : undefined,\n      chapterContext: chapterContext ? {\n        previousChapterSummary: chapterContext.previousChapterSummary,\n        keyEvents: chapterContext.keyEvents,\n        characterStates: chapterContext.characterStates,\n        plotProgress: chapterContext.plotProgress,\n        contextualNotes: chapterContext.contextualNotes\n      } : undefined\n    };\n\n    return await rewriteText(request);\n  } catch (error) {\n    console.error('带上下文重写失败:', error);\n    // 如果获取上下文失败，回退到普通重写\n    return await rewriteText({\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model\n    });\n  }\n}\n"], "names": [], "mappings": "6QACA,IAAM,EAAW,CACf,CACE,IAAK,0CACL,KAAM,mBACN,OAAQ,EACR,aAAc,EACd,SAAU,EACV,cAAe,CACjB,EACA,CACE,IAAK,0CACL,KAAM,UACN,OAAQ,EACR,aAAc,EACd,SAAU,EACV,cAAe,CACjB,EACA,CACE,IAAK,0CACL,KAAM,6BACN,OAAQ,EACR,aAAc,EACd,SAAU,EACV,cAAe,CACjB,EACA,CACE,IAAK,0CACL,KAAM,eACN,OAAQ,EACR,aAAc,EACd,SAAU,EACV,cAAe,CACjB,EACA,CACE,IAAK,0CACL,KAAM,OACN,OAAQ,EACR,aAAc,EACd,SAAU,EACV,cAAe,CACjB,EACD,CAGK,EAAkB,CAAC,EAAgB,uBAAuB,GAC9D,CAAC,wDAAwD,EAAE,EAAM,gBAAgB,CAAC,AAQpF,OAAM,EACI,KAAO,IAAI,EAAU,AAAD,CAG5B,qBAAsB,CACpB,IAAM,EAAM,KAAK,GAAG,GAGd,EAAgB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAO,EAAI,aAAa,EAAI,UAEnE,AAA6B,GAAG,CAA5B,EAAc,MAAM,CAEf,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAK,IAC5B,EAAI,aAAa,CAAG,EAAI,aAAa,CAAG,EAAM,GAKlC,EAAc,MAAM,CAAC,CAAC,EAAM,IACzB,AAEV,EAFc,MAAM,EAAI,CAAD,AAEZ,CAFiB,YAAY,EAAG,CAAC,CACjC,EAAK,MAAM,EAAI,CAAD,CAAM,YAAY,CAAG,CAAC,EACxB,EAAM,EAIxC,CAGA,YAAY,CAAe,CAAE,CAAgB,CAAE,CAC7C,IAAM,EAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAK,EAAE,IAAI,GAAK,GACvC,IACF,CADO,CACH,YAAY,GAChB,EAAI,QAAQ,CAAG,KAAK,GAAG,GAEnB,AAAC,IAEH,EAAI,GAFQ,UAEK,CAAG,KAAK,GAAG,GA1CV,EA0Ce,CAAA,EAGvC,CAGA,CAhD+B,SAgDpB,CACT,OAAO,CAjDwC,GAiDpC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAQ,CAC3B,CAD0B,IACpB,EAAI,IAAI,CACd,aAAc,EAAI,YAAY,CAC9B,OAAQ,EAAI,MAAM,CAClB,YAAa,EAAI,aAAa,EAAI,KAAK,GAAG,GAC1C,kBAAmB,KAAK,GAAG,CAAC,EAAG,EAAI,aAAa,CAAG,KAAK,GAAG,IAC7D,CAAC,CACH,CACF,CAEA,IAAM,EAAa,IAAI,EAiIhB,eAAe,EAAY,CAAuB,EACvD,IAAM,EAAY,KAAK,GAAG,GACtB,EAAY,GACZ,EAAoB,GAExB,IAAK,IAAI,EAAU,EAAG,IAAuB,IAC3C,EAD8B,CAC1B,CACF,EAFoD,EAsFhD,EApFE,EAAS,EAAW,mBAAmB,GAG7C,GAAI,EAAO,aAAa,CAAG,KAAK,GAAG,GAAI,CACrC,IAAM,EAAe,KAAK,GAAG,CAAC,EAAO,aAAa,CAAG,KAAK,GAAG,IAAI,IAC3D,EAAkB,KAAK,GAAG,CAAC,IAA2B,KAAK,GAAG,CAAC,EAAG,GArM1D,KAsMR,EAD4E,AACjE,AAtMI,KAsMC,GAAG,CAAC,EAAc,EAtMN,CAwMlC,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,SAAS,EAAS,EAAU,EAAE,CAAd,AAAe,EAAE,YAAY,AAAa,EAAO,IAAI,CAAC,CAAC,CAAC,EAAhB,AAC1E,MAAM,IAAI,QAAQ,GAAW,WAAW,EAAS,GACnD,CAEA,IAAM,EApGZ,AAoGqB,SApGZ,AAAY,CAAuB,EAC1C,GAAM,cAAE,CAAY,OAAE,CAAK,cAAE,CAAY,eAAE,CAAa,CAAE,cAAY,gBAAE,CAAc,CAAE,CAAG,EAEvF,EAAS,CAAC;;;AAGhB,EAAE,MAAM;;AAER,EAAE,EAAe,CAAC,KAAK,EAAE,EAAA,CAAc,CAAG,GAAA,CAAI,CAqE5C,OAAO,AAlEH,IACF,GAAU,CAAC,MADK;;;KAIf,EAAE,EAAa,OAAO,CAAC;;;AAG5B,EAAE,EAAa,cAAc,CAAC,GAAG,CAAC,GAC5B,CAAC,EAAE,EAAE,EAAK,IAAI,CAAC,CAAC,EAAE,EAAK,IAAI,CAAC,GAAG,EAAE,EAAK,WAAW,CAAA,EAAG,EAAK,aAAa,CAAG,CAAC,MAAM,EAAE,EAAK,aAAa,CAAA,CAAE,CAAG,GAAA,CAAI,EAC7G,IAAI,CAAC,MAAM;;MAEX,EAAE,EAAa,YAAY,CAAC;;KAE7B,EAAE,EAAa,YAAY,CAAC;;KAE5B,EAAE,EAAa,IAAI,CAAA,CAAA,AAAE,EAIpB,IACF,GAAU,CAAC,QADO;;SAGb,CAAC,CAEF,EAAe,sBAAsB,EAAE,CACzC,GAAU,CAAC;MACX,EAAE,EAAe,sBAAsB,CAAA,CAAA,AAAE,EAGvC,EAAe,SAAS,CAAC,MAAM,CAAG,GAAG,CACvC,GAAU,CAAC;OACV,EAAE,EAAe,SAAS,CAAC,IAAI,CAAC,KAAA,CAAA,AAAM,EAGrC,EAAe,eAAe,CAAC,MAAM,CAAG,GAAG,CAC7C,GAAU,CAAC;;AAEjB,EAAE,EAAe,eAAe,CAAC,GAAG,CAAC,GAC7B,CAAC,EAAE,EAAE,EAAM,IAAI,CAAC,EAAE,EAAE,EAAM,MAAM,CAAC,MAAM,EAAE,EAAM,QAAQ,CAAC,MAAM,EAAE,EAAM,aAAa,CAAA,CAAE,EACrF,IAAI,CAAC,MAAA,CAAO,AAAP,EAGT,GAAU,CAAC;KACV,EAAE,EAAe,YAAY,CAAA,CAAE,CAE5B,EAAe,eAAe,EAAE,CAClC,GAAU,CAAC;KACZ,EAAE,EAAe,eAAe,CAAA,CAAA,AAAE,GAIrC,GAAU,CAAC;;;AAGb,EAAE,aAAa;;;;;;;;;;wBAUS,CAAC,AAGzB,EAsBiC,GAGrB,EAAa,IAAI,gBACjB,EAAY,WAAW,IAAM,EAAW,KAAK,GAAI,KAEjD,EAAS,CAFgD,CAEhC,EAAQ,KAFgC,AAE3B,EACtC,EAAW,MAAM,MAAM,CAAA,EAAG,EAAO,KAAK,EAAE,EAAO,GAAG,CAAA,CAAE,CAAE,CAC1D,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,SAAU,CAAC,CACT,MAAO,CAAC,CACN,KAAM,CACR,EAAE,AACJ,EAAE,CACF,iBAAkB,CAChB,YAAa,GACb,KAAM,GACN,KAAM,GACN,eAAkB,CAChB,eAAkB,CACpB,CACF,CACF,GACA,OAAQ,EAAW,MAAM,AAC3B,GAEA,aAAa,GACb,IAAM,EAAiB,KAAK,GAAG,GAAK,EAGpC,GAAwB,KAAK,CAAzB,EAAS,MAAM,GACjB,EAAW,WAAW,CAAC,EAAO,IAAI,CAAE,IACpC,EAAY,CAAC,OAAO,EAAE,EAAO,IAAI,CAAC,CAAC,CAAC,CACpC,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,cAAc,EAAE,EAAO,IAAI,CAAC,eAAe,CAAC,CAE5E,EAAU,GAAiB,CAC7B,IAAM,EAAa,IAA2B,AADpB,KACyB,GAAG,CAAC,EAAG,GAC1D,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,EAAW,QAAQ,CAAC,EACzC,MAAM,IAAI,QAAQ,GAAW,WAAW,EAAS,IACjD,QACF,CAIF,GAAI,CAAC,EAAS,EAAE,CAAE,CAChB,IAAM,EAAY,MAAM,EAAS,IAAI,GAMrC,GALA,QAAQ,KAAK,CAAC,oBAAqB,GACnC,EAAW,WAAW,CAAC,EAAO,IAAI,EAAE,GACpC,EAAY,CAAC,SAAS,EAAE,EAAS,MAAM,CAAC,CAAC,EAAE,EAAS,UAAU,CAAA,CAAE,CAChE,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,UAAU,EAAE,EAAS,MAAM,CAAC,CAAC,EAAE,EAAS,UAAU,CAAC,MAAM,EAAE,EAAU,SAAS,CAAC,EAAG,KAAA,CAAM,CAExH,EAAU,EAAiB,CAC7B,IAAM,EAAa,KADO,AACU,GAAU,CAAC,AAC/C,OADmC,AAC7B,IAAI,QAAQ,GAAW,WAAW,EAAS,IACjD,QACF,CAEA,MAAO,CACL,cAAe,GACf,SAAS,EACT,MAAO,EACP,WAAY,EAAO,IAAI,CACvB,iBACA,cAAe,CACjB,CACF,CAIA,GAAI,CACF,EAAO,MAAM,EAAS,IAAI,EAC5B,CAAE,MAAO,EAAY,CAKnB,GAJA,EAAW,WAAW,CAAC,EAAO,IAAI,EAAE,GACpC,EAAY,WACZ,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,yBAAyB,EAAE,aAAsB,MAAQ,EAAW,OAAO,CAAG,OAAA,CAAQ,CAEtH,EAAU,EAAiB,CAC7B,IAAM,EAAa,KAAiB,AADV,GACoB,CAAC,AAC/C,OADmC,AAC7B,IAAI,QAAQ,GAAW,WAAW,EAAS,IACjD,QACF,CAEA,MAAO,CACL,cAAe,GACf,SAAS,EACT,MAAO,EACP,WAAY,EAAO,IAAI,gBACvB,EACA,cAAe,EACf,WAAY,EAAU,CACxB,CACF,CAMA,GAHA,EAAW,WAAW,CAAC,EAAO,IAAI,EAAE,GAGhC,CAAC,EAAK,UAAU,EAA+B,IAA3B,EAAK,UAAU,CAAC,MAAM,CAAQ,CAIpD,GAHA,EAAY,cACZ,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,sCAAsC,EAAE,KAAK,SAAS,CAAC,GAAM,SAAS,CAAC,EAAG,KAAA,CAAM,CAEhH,EAAU,EAAiB,CAC7B,EAAW,SADe,EACJ,CAAC,EAAO,IAAI,EAAE,GACpC,IAAM,CADsC,CACzB,IAA2B,KAAK,GADM,AACH,CAAC,EAAG,EAC1D,OAAM,IAAI,QAAQ,GAAW,WAAW,EAAS,IACjD,QACF,CAEA,MAAO,CACL,cAAe,GACf,SAAS,EACT,MAAO,EACP,WAAY,EAAO,IAAI,gBACvB,EACA,cAAe,EACf,WAAY,EAAU,CACxB,CACF,CAEA,IAAM,EAAY,EAAK,UAAU,CAAC,EAAE,CAEpC,GAA+B,UAAU,CAArC,EAAU,YAAY,CACxB,MAAO,CACL,cAAe,GACf,SAAS,EACT,MAAO,0BACP,WAAY,EAAO,IAAI,gBACvB,EACA,cAAe,CAAC,+BAA+B,CAAC,CAChD,WAAY,EAAU,CACxB,EAGF,GAAI,CAAC,EAAU,OAAO,EAAI,CAAC,EAAU,OAAO,CAAC,KAAK,EAAuC,IAAnC,EAAU,OAAO,CAAC,KAAK,CAAC,MAAM,CAAQ,CAI1F,GAHA,EAAY,WACZ,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,iCAAiC,EAAE,KAAK,SAAS,CAAC,GAAW,SAAS,CAAC,EAAG,KAAA,CAAM,CAEhH,EAAU,EAAiB,CAC7B,EAAW,SADe,EACJ,CAAC,EAAO,IAAI,EAAE,GACpC,IAAM,EAAa,KAAiB,GAAU,CAAC,AAC/C,OADmC,AAC7B,IAAI,QAAQ,GAAW,WAAW,EAAS,IACjD,QACF,CAEA,MAAO,CACL,cAAe,GACf,SAAS,EACT,MAAO,EACP,WAAY,EAAO,IAAI,gBACvB,EACA,cAAe,EACf,WAAY,EAAU,CACxB,CACF,CAEA,IAAM,EAAgB,EAAU,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAGrD,GAAI,CAAC,GAAiB,EAAc,IAAI,GAAG,MAAM,CAAG,GAAI,CAItD,GAHA,EAAY,aACZ,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,cAAc,EAAE,GAAe,QAAU,EAAE,OAAO,EAAE,GAAe,UAAU,EAAG,MAAQ,OAAO,CAAC,CAAC,CAEjI,EAAU,EAAiB,CAC7B,EAAW,SADe,EACJ,CAAC,EAAO,IAAI,CAAE,IACpC,IAAM,EAAa,IAAiB,GAAU,CAAC,CAC/C,OADmC,AAC7B,IAAI,QAAQ,GAAW,WAAW,EAAS,IACjD,QACF,CAEA,MAAO,CACL,cAAe,GACf,SAAS,EACT,MAAO,EACP,WAAY,EAAO,IAAI,gBACvB,EACA,cAAe,EACf,WAAY,EAAU,CACxB,CACF,CAGA,IAAM,EAAa,EAAK,aAAa,EAAE,iBAAmB,EAE1D,MAAO,CACL,cAAe,EAAc,IAAI,GACjC,SAAS,EACT,WAAY,EAAO,IAAI,YACvB,EACA,MAAO,EAAQ,KAAK,EAAI,uCACxB,EACA,WAAY,EAAU,CACxB,CAEF,CAAE,MAAO,EAAO,CAYd,GAXA,QAAQ,KAAK,CAAC,kBAAmB,GAG7B,aAAiB,OAAwB,cAAc,CAA7B,EAAM,IAAI,EACtC,EAAY,OACZ,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,eAAe,CAAC,GAEpD,EAAY,CAAC,MAAM,EAAE,aAAiB,MAAQ,EAAM,OAAO,CAAG,OAAA,CAAQ,CACtE,EAAoB,CAAC,CAAC,EAAE,EAAU,EAAE,KAAK,EAAE,aAAiB,MAAQ,EAAM,KAAK,EAAI,EAAM,OAAO,CAAG,SAAA,CAAU,EAG3G,EAAU,EAAiB,CAC7B,IAAM,EA/ZmB,AA+ZN,IAA2B,CADpB,CA9ZK,GA+ZoB,GAAG,CAAC,EAAG,GAC1D,GAha8C,KAgatC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAW,QAAQ,CAAC,EACxC,MAAM,IAAI,QAAQ,GAAW,WAAW,EAAS,GACnD,CACF,CAGF,MAAO,CACL,cAAe,GACf,SAAS,EACT,MAAO,CAAC,EAAE,EAAE,SAAsB,GAAV,AAAqB,CAC7C,OADgC,QAChB,KAAK,GAAG,GAAK,EAC7B,cAAe,EACf,WA7agB,CA6aJ,AACd,CACF,CAGO,AAlbgB,WAAW,IAkbZ,EACpB,CAAmE,CACnE,CAAa,CACb,CAA8E,CAC9E,CAA+D,CAC/D,EAAsB,CAAC,CACvB,EAAgB,uBAAuB,CACvC,GAAiC,CAAA,CAAK,CAEtC,IAAM,EAAuF,AAAI,IAFhD,EAEsD,EAAS,MAAM,EAClH,EAAY,EACZ,EAAkB,EAChB,EAAY,KAAK,GAAG,GAGpB,EAAY,IAAI,EAAU,GAE1B,EAAiB,MAAO,EAA6D,KACzF,MAAM,EAAU,OAAO,GACvB,IAAM,EAAmB,KAAK,GAAG,GAEjC,GAAI,CAEF,IAAM,EAAS,MAAM,EAAY,CAC/B,aAAc,EAAQ,OAAO,OAC7B,EACA,aAAc,EAAQ,KAAK,CAC3B,cAAe,EAAQ,MAAM,OAC7B,CACF,GAEM,EAAwB,KAAK,GAAG,GAAK,EAEvC,EAAO,UAAU,EAAE,CACrB,GAAmB,EAAO,UAAU,AAAV,EAG5B,IAAM,EAAgB,CACpB,QAAS,EAAO,OAAO,CACvB,QAAS,EAAO,aAAa,CAC7B,MAAO,EAAO,KAAK,CACnB,QAAS,CACP,WAAY,EAAO,UAAU,CAC7B,WAAY,EAAO,UAAU,CAC7B,MAAO,EAAO,KAAK,CACnB,eAAgB,EAChB,cAAe,EAAQ,MAAM,CAC7B,aAAc,EAAQ,KAAK,AAC7B,CACF,EAWA,GATA,CAAO,CAAC,EAAM,CAAG,EACjB,IAGI,GACF,EAAkB,EAAO,GAIvB,EAAY,CACd,IAAM,EAAkB,AANH,WAOnB,EACA,MAAO,EAAS,MAAM,iBACtB,EACA,UAAW,KAAK,GAAG,GAAK,EACxB,sBAAuB,CAAC,KAAK,GAAG,GAAK,CAAA,CAAS,CAAI,EAClD,YAAa,EAAW,QAAQ,GAChC,eAAgB,CACd,OAAQ,EAAQ,MAAM,CACtB,MAAO,EAAQ,KAAK,CACpB,eAAgB,EAChB,OAAQ,EAAO,UAAU,CACzB,OAAQ,EAAO,UAAU,AAC3B,CACF,EAEA,EAAY,EAAY,EAAS,MAAM,CAAI,IAAK,EAAQ,MAAM,CAAE,EAClE,CAKA,OAFA,MAAM,IAAI,QAAQ,GAAW,WAAW,EArgBxB,MAugBT,AAvgBe,CAqgB2B,AAGnD,CAAE,MAAO,EAAO,CACd,AAzgBiC,IAygB3B,EAAmB,KAAK,GAAG,GAC3B,EAAc,CAClB,SAAS,EACT,QAAS,GACT,MAAO,CAAC,MAAM,EAAE,aAAiB,MAAQ,EAAM,OAAO,CAAG,OAAA,CAAQ,CACjE,QAAS,CACP,cAAe,EAAQ,MAAM,CAC7B,aAAc,EAAQ,KAAK,CAC3B,eAAgB,EAAmB,CACrC,CACF,EASA,GAPA,CAAO,CAAC,EAAM,CAAG,EACjB,IAEI,GACF,EAAkB,EAAO,GAGvB,EAAY,CACd,IAAM,EALe,AAKG,WACtB,EACA,MAAO,EAAS,MAAM,iBACtB,EACA,UAAW,KAAK,GAAG,GAAK,EACxB,sBAAuB,CAAC,KAAK,GAAG,GAAK,CAAA,CAAS,CAAI,EAClD,YAAa,EAAW,QAAQ,GAChC,eAAgB,CACd,OAAQ,EAAQ,MAAM,CACtB,MAAO,EAAQ,KAAK,CACpB,MAAO,aAAiB,MAAQ,EAAM,OAAO,CAAG,MAClD,CACF,EAEA,EAAY,EAAY,EAAS,MAAM,CAAI,IAAK,EAAQ,MAAM,CAAE,EAClE,CAEA,OAAO,IACT,QAAU,CACR,EAAU,OAAO,EACnB,CACF,EAGM,EAAW,EAAS,GAAG,CAAC,CAAC,EAAS,IAAU,EAAe,EAAS,IAI1E,GAHA,MAAM,QAAQ,GAAG,CAAC,GAGd,EAAuB,CACzB,IAAM,EAAiB,EACpB,GAAG,CAAC,CAAC,EAAQ,KAAW,GAAD,KAAG,QAAQ,EAAO,QAAS,CAAQ,CAAC,EAAM,CAAC,CAAC,EACnE,MAAM,CAAC,GAAQ,CAAC,EAAK,MAAM,CAAC,OAAO,EAEtC,GAAI,EAAe,MAAM,CAAG,EAAG,CAC7B,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,EAAe,MAAM,CAAC,UAAU,CAAC,EAGrD,IAAM,EAAoB,IAAI,EAAU,GAExC,CAF4C,GAEvC,GAAM,CAAE,KAF2C,EAEtC,CAAE,SAAO,CAAE,GAAI,EAAgB,CAC/C,MAAM,EAAkB,OAAO,GAE/B,GAAI,CACF,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,EAAQ,MAAM,CAAC,IAAI,EAAE,EAAQ,KAAK,CAAA,CAAE,EAGzD,MAAM,IAAI,QAAQ,GAAW,WAAW,EAAS,MAEjD,IAAM,EAAiB,MAAM,EAAY,CACvC,aAAc,EAAQ,OAAO,OAC7B,EACA,aAAc,EAAQ,KAAK,CAC3B,cAAe,EAAQ,MAAM,CAC7B,OACF,GAEA,GAAI,EAAe,OAAO,CAAE,CAC1B,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,EAAQ,MAAM,CAAC,EAAE,CAAC,EAEvC,IAAM,EAAyB,CAC7B,QAAS,GACT,QAAS,EAAe,aAAa,CACrC,WAAO,EACP,QAAS,CACP,GAAG,CAAc,CACjB,cAAe,EAAQ,MAAM,CAC7B,aAAc,EAAQ,KAAK,CAC3B,YAAa,EACf,CACF,EAWA,GATA,CAAO,CAAC,EAAM,CAAG,EACjB,IAGI,GACF,EAAkB,EAAO,GAIvB,EAAY,CACd,IAAM,EAAkB,AANH,WAOnB,EACA,MAAO,EAAS,MAAM,CACtB,gBAAiB,GAAmB,EAAe,UAAU,GAA1B,AAA8B,CAAC,CAClE,UAAW,KAAK,GAAG,GAAK,EACxB,sBAAuB,CAAC,KAAK,GAAG,GAAK,CAAA,CAAS,CAAI,EAClD,YAAa,EAAW,QAAQ,GAChC,eAAgB,CACd,OAAQ,EAAQ,MAAM,CACtB,MAAO,EAAQ,KAAK,CACpB,eAAgB,EAAe,cAAc,CAC7C,OAAQ,EAAe,UAAU,CACjC,OAAQ,EAAe,UAAU,CACjC,aAAa,CACf,CACF,EAEA,EAAY,EAAY,EAAS,MAAM,CAAI,IAAK,EAAQ,MAAM,CAAE,EAClE,CACF,MACE,CADK,OACG,GAAG,CAAC,CAAC,EAAE,EAAE,EAAQ,MAAM,CAAC,QAAQ,EAAE,EAAe,KAAK,CAAA,CAAE,EAEhE,CAAO,CAAC,EAAM,CAAG,CACf,GAAG,CAAO,CAAC,EAAM,CACjB,MAAO,CAAC,MAAM,EAAE,CAAO,CAAC,EAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAe,KAAK,CAAA,CAAE,CACrE,QAAS,CACP,GAAG,CAAO,CAAC,EAAM,CAAC,OAAO,CACzB,kBAAmB,GACnB,cAAe,EAAe,KAAK,CACnC,sBAAuB,EAAe,aACxC,AADqD,CAEvD,CAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE,EAAQ,MAAM,CAAC,QAAQ,CAAC,CAAE,GAC/C,CAAO,CAAC,EAAM,CAAG,CACf,GAAG,CAAO,CAAC,EAAM,CACjB,MAAO,CAAA,EAAG,CAAO,CAAC,EAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,aAAiB,MAAQ,EAAM,OAAO,CAAG,OAAA,CAAQ,CAC1F,QAAS,CACP,GAAG,CAAO,CAAC,EAAM,CAAC,OAAO,CACzB,mBAAmB,EACnB,kBAAmB,aAAiB,MAAQ,EAAM,OAAO,CAAG,MAC9D,CACF,CACF,QAAU,CACR,EAAkB,OAAO,EAC3B,CACF,CACF,CACF,CAEA,OAAO,CACT,CAGA,MAAM,EACI,OAAgB,CAChB,UAA+B,EAAG,AAE1C,AAFyC,aAE7B,CAAe,CAAE,CAC3B,IAAI,CAAC,OAAO,CAAG,CACjB,CAEA,MAAM,SAAyB,QAC7B,AAAI,IAAI,CAAC,OAAO,CAAG,GAAG,AACpB,IAAI,CAAC,OAAO,GACL,QAAQ,OAAO,IAGjB,IAAI,QAAc,AAAC,IACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EACtB,EACF,CAEA,SAAgB,CAEd,GADA,IAAI,CAAC,OAAO,GACR,IAAI,CAAC,SAAS,CAAC,MAAM,CAAG,EAAG,CAC7B,IAAM,EAAU,IAAI,CAAC,SAAS,CAAC,KAAK,GAChC,IACF,IAAI,CADO,AACN,OAAO,GACZ,IAEJ,CACF,CACF,CAGO,eAAe,IAKpB,GAAI,CACF,IAAM,EAAa,MAAM,EAAY,CACnC,aAAc,YACd,MAAO,QACT,GAEA,MAAO,CACL,QAAS,EAAW,OAAO,CAC3B,MAAO,EAAW,KAAK,CACvB,QAAS,CACP,WAAY,EAAW,UAAU,CACjC,WAAY,EAAW,UAAU,CACjC,MAAO,EAAW,KAAK,CACvB,eAAgB,EAAW,cAAc,CACzC,YAAa,EAAW,QAAQ,EAClC,CACF,CACF,CAAE,MAAO,EAAO,CACd,MAAO,CACL,SAAS,EACT,MAAO,CAAC,QAAQ,EAAE,aAAiB,MAAQ,EAAM,OAAO,CAAG,OAAA,CAAQ,CACnE,QAAS,CACP,YAAa,EAAW,QAAQ,EAClC,CACF,CACF,CACF,CAGO,SAAS,IACd,OAAO,EAAW,QAAQ,EAC5B,CAGO,SAAS,IACd,EAAS,OAAO,CAAC,IACf,EAAI,YAAY,CAAG,EACnB,EAAI,QAAQ,CAAG,EACf,EAAI,aAAa,CAAG,CACtB,EACF,CAGO,IAAI,EAAqF,CAC9F,cAAe,CACb,KAAM,QACN,YAAa,wBACb,MAAO,CAAC;;;;qBAIS,CAAC,AACpB,EAEA,cAAe,CACb,KAAM,OACN,YAAa,cACb,MAAO,CAAC;;;;oBAIQ,CAAC,AACnB,EAEA,sBAAuB,CACrB,KAAM,OACN,YAAa,eACb,MAAO,CAAC;;;;kBAIM,CAAC,AACjB,EAEA,mBAAoB,CAClB,KAAM,OACN,YAAa,gBACb,MAAO,CAAC;;;;eAIG,CAAC,AACd,EAEA,OAAQ,CACN,KAAM,QACN,YAAa,aACb,MAAO,EACT,CACF,EAGO,SAAS,IAOd,GAAI,CACF,GAAM,UAAE,CAAQ,CAAE,CAAA,EAAA,CAAA,CAAA,OACI,AAGtB,EAH+B,MAAM,GAGvB,OAAO,CAAC,AAAC,IACrB,CAAY,CAAC,CAAC,OAAO,EAAE,EAAO,EAAE,CAAA,CAAE,CAAC,CAAG,CACpC,KAAM,EAAO,IAAI,CACjB,YAAa,EAAO,WAAW,CAC/B,MAAO,EAAO,KAAK,AACrB,CACF,EACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,aAAc,EAC9B,CACF,CAGO,SAAS,EAAgB,CAAY,CAAE,CAAmB,CAAE,CAAa,EAC9E,IAAM,EAAM,CAAC,OAAO,EAAE,KAAK,GAAG,GAAA,CAAI,CASlC,OARA,EAAe,CACb,GAAG,CAAY,CACf,CAAC,EAAI,CAAE,MACL,cACA,QACA,CACF,CACF,EACO,CACT,CAGO,eAAe,EACpB,CAAe,CACf,CAAqB,CACrB,CAAoB,CACpB,CAAa,CACb,CAAqB,CACrB,CAAc,EAed,GAAI,CAEF,GAAM,gBAAE,CAAc,kBAAE,CAAgB,CAAE,CAAA,EAAA,CAAA,CAAA,OAGpC,EAAe,EAAe,YAAY,CAAC,GAG3C,EAAiB,EAAiB,YAAY,CAAC,EAAS,GAGxD,EAA0B,cAC9B,QACA,eACA,gBACA,QACA,EACA,aAAc,EAAe,CAC3B,QAAS,EAAa,OAAO,CAC7B,eAAgB,EAAa,cAAc,CAC3C,aAAc,EAAa,YAAY,CACvC,aAAc,EAAa,YAAY,CACvC,KAAM,EAAa,IAAI,AACzB,OAAI,EACJ,eAAgB,EAAiB,CAC/B,uBAAwB,EAAe,sBAAsB,CAC7D,UAAW,EAAe,SAAS,CACnC,gBAAiB,EAAe,eAAe,CAC/C,aAAc,EAAe,YAAY,CACzC,gBAAiB,EAAe,eAAe,AACjD,OAAI,CACN,EAEA,OAAO,MAAM,EAAY,EAC3B,CAAE,MAAO,EAAO,CAGd,OAFA,QAAQ,KAAK,CAAC,YAAa,GAEpB,MAAM,EAAY,cACvB,QACA,eACA,gBACA,QACA,CACF,EACF,CACF"}