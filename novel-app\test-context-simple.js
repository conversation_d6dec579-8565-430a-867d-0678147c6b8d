// 简单的上下文系统测试
const fs = require('fs');
const path = require('path');

// 测试小说内容
const testContent = `第一章 初遇

李明是一个普通的大学生，今天他在图书馆遇到了美丽的女孩小雨。小雨正在看一本关于古代文学的书，李明鼓起勇气上前搭话。

"你好，我是李明，也对古代文学很感兴趣。"

小雨抬起头，露出了甜美的笑容："你好，我是小雨。你也喜欢古代文学吗？"

两人开始了愉快的交谈，李明发现小雨不仅美丽，而且很有才华。

第二章 深入了解

经过几天的相处，李明和小雨成为了好朋友。他们经常一起在图书馆学习，讨论各种文学作品。

李明发现自己越来越喜欢小雨了，但他不确定小雨对他的感觉。

"小雨，你觉得我们的友谊怎么样？"李明试探性地问道。

小雨脸红了："我觉得我们相处得很好，你是一个很特别的人。"

李明心中一阵欣喜，但他还是不敢表白。

第三章 表白

终于，在一个月圆之夜，李明决定向小雨表白。他们在校园的湖边散步。

"小雨，我有话想对你说。"李明紧张地说道。

"什么话？"小雨好奇地看着他。

"我喜欢你，从第一次见面就喜欢上了你。你愿意做我的女朋友吗？"

小雨沉默了一会儿，然后点了点头："我也喜欢你，李明。"

两人拥抱在一起，月光洒在湖面上，一切都显得那么美好。`;

async function testBasicContext() {
  console.log('开始测试基本上下文功能...\n');

  try {
    // 1. 创建测试文件到novels目录
    const novelsDir = path.join(__dirname, '..', 'novels');
    if (!fs.existsSync(novelsDir)) {
      fs.mkdirSync(novelsDir, { recursive: true });
    }

    const testFileName = `test-novel-${Date.now()}.txt`;
    const testFile = path.join(novelsDir, testFileName);
    fs.writeFileSync(testFile, testContent, 'utf-8');
    console.log('✓ 测试文件已创建');

    // 2. 测试解析
    console.log('\n测试小说解析...');
    const parseResponse = await fetch('http://localhost:3001/api/novels', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ filename: testFileName })
    });

    if (!parseResponse.ok) {
      throw new Error(`解析失败: ${parseResponse.status}`);
    }

    const parseResult = await parseResponse.json();
    if (!parseResult.success) {
      throw new Error(`解析失败: ${parseResult.error}`);
    }

    const novelId = parseResult.data.novel.id;
    console.log(`✓ 小说解析成功，ID: ${novelId}`);
    console.log(`✓ 章节数量: ${parseResult.data.chapters.length}`);

    // 3. 测试上下文分析
    console.log('\n测试上下文分析...');
    const analyzeResponse = await fetch('http://localhost:3001/api/context/analyze', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        novelId: novelId,
        analyzeChapters: true
      })
    });

    if (!analyzeResponse.ok) {
      throw new Error(`分析失败: ${analyzeResponse.status}`);
    }

    const analyzeResult = await analyzeResponse.json();
    if (!analyzeResult.success) {
      throw new Error(`分析失败: ${analyzeResult.error}`);
    }

    console.log('✓ 上下文分析成功');
    console.log(`✓ 小说摘要: ${analyzeResult.data.novelContext.summary.substring(0, 50)}...`);
    console.log(`✓ 主要人物数量: ${analyzeResult.data.novelContext.mainCharacters.length}`);
    console.log(`✓ 章节上下文数量: ${analyzeResult.data.chapterContexts.length}`);

    // 4. 测试上下文查询
    console.log('\n测试上下文查询...');
    try {
      const contextResponse = await fetch(`http://localhost:3001/api/context/novel?novelId=${novelId}`);

      console.log(`查询响应状态: ${contextResponse.status}`);

      if (!contextResponse.ok) {
        const errorText = await contextResponse.text();
        throw new Error(`查询失败: ${contextResponse.status} - ${errorText}`);
      }

      const contextResult = await contextResponse.json();
      console.log('查询响应:', JSON.stringify(contextResult, null, 2));

      if (!contextResult.success) {
        throw new Error(`查询失败: ${contextResult.error}`);
      }

      console.log('✓ 上下文查询成功');
      console.log(`✓ 写作风格: ${contextResult.data.writingStyle}`);
      console.log(`✓ 世界观: ${contextResult.data.worldSetting}`);
    } catch (queryError) {
      console.error('上下文查询错误:', queryError);
      throw queryError;
    }

    // 5. 清理测试文件
    if (fs.existsSync(testFile)) {
      fs.unlinkSync(testFile);
      console.log('\n✓ 测试文件已清理');
    }

    console.log('\n🎉 所有测试通过！上下文记忆系统工作正常！');

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);

    // 清理测试文件
    const novelsDir = path.join(__dirname, '..', 'novels');
    if (fs.existsSync(novelsDir)) {
      const files = fs.readdirSync(novelsDir).filter(f => f.startsWith('test-novel-'));
      files.forEach(file => {
        const filePath = path.join(novelsDir, file);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      });
    }
  }
}

// 运行测试
if (require.main === module) {
  testBasicContext().catch(console.error);
}

module.exports = { testBasicContext };
