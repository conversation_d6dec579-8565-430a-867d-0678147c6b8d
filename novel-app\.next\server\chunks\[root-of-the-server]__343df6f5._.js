module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},22734,(e,t,r)=>{t.exports=e.x("fs",()=>require("fs"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},84168,e=>{"use strict";e.s(["chapterContextDb",()=>A,"chapterDb",()=>v,"characterDb",()=>m,"jobDb",()=>S,"novelContextDb",()=>R,"novelDb",()=>w,"presetDb",()=>y,"ruleDb",()=>I]);var t=e.i(22734),r=e.i(14747),n=e.i(54799);let a=r.default.join(process.cwd(),"data"),i=r.default.join(a,"novels.json"),o=r.default.join(a,"chapters.json"),d=r.default.join(a,"rewrite_rules.json"),s=r.default.join(a,"rewrite_jobs.json"),l=r.default.join(a,"characters.json"),u=r.default.join(a,"presets.json"),p=r.default.join(a,"novel-contexts.json"),c=r.default.join(a,"chapter-contexts.json");function x(){t.default.existsSync(a)||t.default.mkdirSync(a,{recursive:!0})}function f(e){if(x(),!t.default.existsSync(e))return[];try{let r=t.default.readFileSync(e,"utf-8");return JSON.parse(r)}catch(t){return console.error(`Error reading ${e}:`,t),[]}}function g(e,r){x();try{t.default.writeFileSync(e,JSON.stringify(r,null,2),"utf-8")}catch(t){throw console.error(`Error writing ${e}:`,t),t}}function h(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}let w={getAll:()=>f(i),getById:e=>f(i).find(t=>t.id===e),create:e=>{var t;let r=f(i),a=(t=e.title,n.default.createHash("md5").update(t).digest("hex").substring(0,18)),o=r.find(e=>e.id===a);if(o)return o.filename=e.filename,o.chapterCount=e.chapterCount,g(i,r),o;let d={...e,id:a,createdAt:new Date().toISOString()};return r.push(d),g(i,r),d},update:(e,t)=>{let r=f(i),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t},g(i,r),r[n])},delete:e=>{let t=f(i),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(i,t),!0)}},v={getAll:()=>f(o),getByNovelId:e=>f(o).filter(t=>t.novelId===e),getById:e=>f(o).find(t=>t.id===e),create:e=>{let t=f(o),r={...e,id:h(),createdAt:new Date().toISOString()};return t.push(r),g(o,t),r},createBatch:e=>{let t=f(o),r=e.map(e=>({...e,id:h(),createdAt:new Date().toISOString()}));return t.push(...r),g(o,t),r},delete:e=>{let t=f(o),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(o,t),!0)},deleteByNovelId:e=>{let t=f(o).filter(t=>t.novelId!==e);return g(o,t),!0}},I={getAll:()=>f(d),getById:e=>f(d).find(t=>t.id===e),create:e=>{let t=f(d),r={...e,id:h(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(d,t),r},update:(e,t)=>{let r=f(d),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(d,r),r[n])},delete:e=>{let t=f(d),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(d,t),!0)}},S={getAll:()=>f(s),getById:e=>f(s).find(t=>t.id===e),create:e=>{let t=f(s),r={...e,id:h(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(s,t),r},update:(e,t)=>{let r=f(s),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(s,r),r[n])},delete:e=>{let t=f(s),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(s,t),!0)}},m={getAll:()=>f(l),getByNovelId:e=>f(l).filter(t=>t.novelId===e),getById:e=>f(l).find(t=>t.id===e),create:e=>{let t=f(l),r={...e,id:h(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(l,t),r},update:(e,t)=>{let r=f(l),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(l,r),r[n])},delete:e=>{let t=f(l),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(l,t),!0)},deleteByNovelId:e=>{let t=f(l).filter(t=>t.novelId!==e);return g(l,t),!0}},y={getAll:()=>f(u),getById:e=>f(u).find(t=>t.id===e),create:e=>{let t=f(u),r={...e,id:h(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(u,t),r},update:(e,t)=>{let r=f(u),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(u,r),r[n])},delete:e=>{let t=f(u),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(u,t),!0)}},R={getAll:()=>f(p),getByNovelId:e=>f(p).find(t=>t.novelId===e),getById:e=>f(p).find(t=>t.id===e),create:e=>{let t=f(p),r={...e,id:h(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(p,t),r},update:(e,t)=>{let r=f(p),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(p,r),r[n]},delete:e=>{let t=f(p),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(p,t),!0)}},A={getAll:()=>f(c),getByNovelId:e=>f(c).filter(t=>t.novelId===e),getByChapter:(e,t)=>f(c).find(r=>r.novelId===e&&r.chapterNumber===t),getById:e=>f(c).find(t=>t.id===e),create:e=>{let t=f(c),r={...e,id:h(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(c,t),r},update:(e,t)=>{let r=f(c),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(c,r),r[n]},delete:e=>{let t=f(c),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(c,t),!0)},getContextWindow:(e,t,r=2)=>{let n=f(c).filter(t=>t.novelId===e),a=Math.max(1,t-r),i=t+r;return n.filter(e=>e.chapterNumber>=a&&e.chapterNumber<=i).sort((e,t)=>e.chapterNumber-t.chapterNumber)}}},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},82976,(e,t,r)=>{},88581,e=>{"use strict";e.s(["handler",()=>C,"patchFetch",()=>b,"routeModule",()=>m,"serverHooks",()=>A,"workAsyncStorage",()=>y,"workUnitAsyncStorage",()=>R],88581);var t=e.i(47909),r=e.i(74017),n=e.i(96250),a=e.i(59756),i=e.i(61916),o=e.i(69741),d=e.i(16795),s=e.i(87718),l=e.i(95169),u=e.i(47587),p=e.i(66012),c=e.i(70101),x=e.i(26937),f=e.i(10372),g=e.i(93695);e.i(52474);var h=e.i(220);e.s(["GET",()=>I],54835);var w=e.i(89171),v=e.i(84168);async function I(e){try{let{searchParams:t}=new URL(e.url),r=t.get("novelId"),n=t.get("chapterNumber"),a=t.get("windowSize");if(!r||!n)return w.NextResponse.json({success:!1,error:"小说ID和章节号不能为空"},{status:400});let i=parseInt(n),o=a?parseInt(a):2;if(isNaN(i)||isNaN(o))return w.NextResponse.json({success:!1,error:"章节号和窗口大小必须是有效数字"},{status:400});let d=v.chapterContextDb.getContextWindow(r,i,o);return w.NextResponse.json({success:!0,data:{targetChapter:i,windowSize:o,contexts:d,totalContexts:d.length}})}catch(e){return console.error("获取章节上下文窗口失败:",e),w.NextResponse.json({success:!1,error:"获取章节上下文窗口失败"},{status:500})}}var S=e.i(54835);let m=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/context/window/route",pathname:"/api/context/window",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/context/window/route.ts",nextConfigOutput:"",userland:S}),{workAsyncStorage:y,workUnitAsyncStorage:R,serverHooks:A}=m;function b(){return(0,n.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:R})}async function C(e,t,n){var w;let v="/api/context/window/route";v=v.replace(/\/index$/,"")||"/";let I=await m.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!I)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:S,params:y,nextConfig:R,isDraftMode:A,prerenderManifest:b,routerServerContext:C,isOnDemandRevalidate:N,revalidateOnlyGenerated:j,resolvedPathname:D}=I,O=(0,o.normalizeAppPath)(v),E=!!(b.dynamicRoutes[O]||b.routes[D]);if(E&&!A){let e=!!b.routes[D],t=b.dynamicRoutes[O];if(t&&!1===t.fallback&&!e)throw new g.NoFallbackError}let q=null;!E||m.isDev||A||(q="/index"===(q=D)?"/":q);let T=!0===m.isDev||!E,k=E&&!T,P=e.method||"GET",_=(0,i.getTracer)(),B=_.getActiveScopeSpan(),H={params:y,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!R.experimental.cacheComponents,authInterrupts:!!R.experimental.authInterrupts},supportsDynamicResponse:T,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(w=R.experimental)?void 0:w.cacheLife,isRevalidate:k,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>m.onRequestError(e,t,n,C)},sharedContext:{buildId:S}},U=new d.NodeNextRequest(e),M=new d.NodeNextResponse(t),$=s.NextRequestAdapter.fromNodeNextRequest(U,(0,s.signalFromNodeResponse)(t));try{let o=async r=>m.handle($,H).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=_.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${P} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${P} ${e.url}`)}),d=async i=>{var d,s;let l=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&N&&j&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let d=await o(i);e.fetchMetrics=H.renderOpts.fetchMetrics;let s=H.renderOpts.pendingWaitUntil;s&&n.waitUntil&&(n.waitUntil(s),s=void 0);let l=H.renderOpts.collectedTags;if(!E)return await (0,p.sendResponse)(U,M,d,H.renderOpts.pendingWaitUntil),null;{let e=await d.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(d.headers);l&&(t[f.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==H.renderOpts.collectedRevalidate&&!(H.renderOpts.collectedRevalidate>=f.INFINITE_CACHE)&&H.renderOpts.collectedRevalidate,n=void 0===H.renderOpts.collectedExpire||H.renderOpts.collectedExpire>=f.INFINITE_CACHE?void 0:H.renderOpts.collectedExpire;return{value:{kind:h.CachedRouteKind.APP_ROUTE,status:d.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await m.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:k,isOnDemandRevalidate:N})},C),t}},g=await m.handleResponse({req:e,nextConfig:R,cacheKey:q,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:N,revalidateOnlyGenerated:j,responseGenerator:l,waitUntil:n.waitUntil});if(!E)return null;if((null==g||null==(d=g.value)?void 0:d.kind)!==h.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==g||null==(s=g.value)?void 0:s.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",N?"REVALIDATED":g.isMiss?"MISS":g.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let w=(0,c.fromNodeOutgoingHttpHeaders)(g.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&E||w.delete(f.NEXT_CACHE_TAGS_HEADER),!g.cacheControl||t.getHeader("Cache-Control")||w.get("Cache-Control")||w.set("Cache-Control",(0,x.getCacheControlHeader)(g.cacheControl)),await (0,p.sendResponse)(U,M,new Response(g.value.body,{headers:w,status:g.value.status||200})),null};B?await d(B):await _.withPropagatedContext(e.headers,()=>_.trace(l.BaseServerSpan.handleRequest,{spanName:`${P} ${e.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":P,"http.target":e.url}},d))}catch(t){if(B||t instanceof g.NoFallbackError||await m.onRequestError(e,t,{routerKind:"App Router",routePath:O,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:k,isOnDemandRevalidate:N})}),E)throw t;return await (0,p.sendResponse)(U,M,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__343df6f5._.js.map