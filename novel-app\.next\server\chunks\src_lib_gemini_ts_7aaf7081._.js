module.exports=[71994,e=>{"use strict";e.s(["PRESET_RULES",()=>m,"addCustomPreset",()=>g,"getApiKeyStats",()=>c,"loadCustomPresets",()=>d,"resetApiKeyStats",()=>u,"rewriteChapters",()=>a,"rewriteText",()=>s,"rewriteTextWithContext",()=>w,"testGeminiConnection",()=>l]);let t=[{key:"AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw",name:"My First Project",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y",name:"ankibot",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY",name:"Generative Language Client",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc",name:"In The Novel",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk",name:"chat",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0}],r=(e="gemini-2.5-flash-lite")=>`https://generativelanguage.googleapis.com/v1beta/models/${e}:generateContent`;class n{keys=[...t];getBestAvailableKey(){let e=Date.now(),t=this.keys.filter(t=>t.cooldownUntil<=e);return 0===t.length?this.keys.reduce((e,t)=>t.cooldownUntil<e.cooldownUntil?t:e):t.reduce((e,t)=>t.weight/(t.requestCount+1)>e.weight/(e.requestCount+1)?t:e)}recordUsage(e,t){let r=this.keys.find(t=>t.name===e);r&&(r.requestCount++,r.lastUsed=Date.now(),t||(r.cooldownUntil=Date.now()+6e4))}getStats(){return this.keys.map(e=>({name:e.name,requestCount:e.requestCount,weight:e.weight,isAvailable:e.cooldownUntil<=Date.now(),cooldownRemaining:Math.max(0,e.cooldownUntil-Date.now())}))}}let o=new n;async function s(e){let t=Date.now(),n="",s="";for(let a=0;a<5;a++)try{let i,l=o.getBestAvailableKey();if(l.cooldownUntil>Date.now()){let e=Math.min(l.cooldownUntil-Date.now(),3e4),t=Math.min(2e3*Math.pow(2,a),3e4),r=Math.max(e,t);console.log(`等待 ${r}ms (尝试 ${a+1}/5, API Key: ${l.name})`),await new Promise(e=>setTimeout(e,r))}let c=function(e){let{originalText:t,rules:r,chapterTitle:n,chapterNumber:o,novelContext:s,chapterContext:a}=e,i=`你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：

改写规则：
${r}

${n?`当前章节：${n}`:""}`;return s&&(i+=`

【小说背景信息】
小说摘要：${s.summary}

主要人物：
${s.mainCharacters.map(e=>`- ${e.name}(${e.role}): ${e.description}${e.relationships?` | 关系：${e.relationships}`:""}`).join("\n")}

世界观设定：${s.worldSetting}

写作风格：${s.writingStyle}

整体语调：${s.tone}`),a&&(i+=`

【章节上下文信息】`,a.previousChapterSummary&&(i+=`
前一章摘要：${a.previousChapterSummary}`),a.keyEvents.length>0&&(i+=`
本章关键事件：${a.keyEvents.join("、")}`),a.characterStates.length>0&&(i+=`
人物状态：
${a.characterStates.map(e=>`- ${e.name}: ${e.status} | 情感：${e.emotions} | 关系：${e.relationships}`).join("\n")}`),i+=`
情节推进：${a.plotProgress}`,a.contextualNotes&&(i+=`
重要注释：${a.contextualNotes}`)),i+=`

原文内容：
${t}

请严格按照改写规则进行改写，同时保持与小说整体背景和上下文的连贯性。改写后的内容应该：
1. 遵循所有指定的改写规则
2. 保持与小说整体背景的一致性
3. 确保人物性格和关系的连贯性
4. 保持情节发展的逻辑性
5. 维持原有的写作风格和语调
6. 确保文字流畅自然

请直接输出改写后的内容，不要添加任何解释或说明：`}(e),u=new AbortController,m=setTimeout(()=>u.abort(),6e4),d=r(e.model),g=await fetch(`${d}?key=${l.key}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:c}]}],generationConfig:{temperature:.6,topK:10,topP:.8,thinkingConfig:{thinkingBudget:0}}}),signal:u.signal});clearTimeout(m);let w=Date.now()-t;if(429===g.status&&(o.recordUsage(l.name,!1),n=`API限流 (${l.name})`,s=`第${a+1}次尝试: API Key "${l.name}" 遇到限流，状态码: 429`,a<4)){let e=2e3*Math.pow(2,a);console.log(`API限流，${e}ms后重试...`),await new Promise(t=>setTimeout(t,e));continue}if(!g.ok){let e=await g.text();if(console.error("Gemini API error:",e),o.recordUsage(l.name,!1),n=`API请求失败: ${g.status} ${g.statusText}`,s=`第${a+1}次尝试: HTTP ${g.status} ${g.statusText}, 响应: ${e.substring(0,200)}`,a<4){let e=1e3*(a+1);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:w,detailedError:s}}try{i=await g.json()}catch(e){if(o.recordUsage(l.name,!1),n="JSON解析失败",s=`第${a+1}次尝试: 无法解析API响应为JSON, 错误: ${e instanceof Error?e.message:"未知错误"}`,a<4){let e=1e3*(a+1);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:w,detailedError:s,retryCount:a+1}}if(o.recordUsage(l.name,!0),!i.candidates||0===i.candidates.length){if(n="没有收到有效的响应内容",s=`第${a+1}次尝试: API响应中没有candidates字段或为空数组, 完整响应: ${JSON.stringify(i).substring(0,500)}`,a<4){o.recordUsage(l.name,!1);let e=2e3*Math.pow(2,a);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:w,detailedError:s,retryCount:a+1}}let h=i.candidates[0];if("SAFETY"===h.finishReason)return{rewrittenText:"",success:!1,error:"内容被安全过滤器拦截，请调整改写规则或原文内容",apiKeyUsed:l.name,processingTime:w,detailedError:`内容被安全过滤器拦截，finishReason: SAFETY`,retryCount:a+1};if(!h.content||!h.content.parts||0===h.content.parts.length){if(n="响应内容格式错误",s=`第${a+1}次尝试: candidate内容格式错误, candidate: ${JSON.stringify(h).substring(0,300)}`,a<4){o.recordUsage(l.name,!1);let e=1e3*(a+1);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:w,detailedError:s,retryCount:a+1}}let p=h.content.parts[0].text;if(!p||p.trim().length<10){if(n="生成的内容过短或为空",s=`第${a+1}次尝试: 生成的内容长度: ${p?.length||0}, 内容: "${p?.substring(0,100)||"null"}"`,a<4){o.recordUsage(l.name,!1);let e=1e3*(a+1);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:w,detailedError:s,retryCount:a+1}}let y=i.usageMetadata?.totalTokenCount||0;return{rewrittenText:p.trim(),success:!0,apiKeyUsed:l.name,tokensUsed:y,model:e.model||"gemini-2.5-flash-lite",processingTime:w,retryCount:a+1}}catch(e){if(console.error("Gemini API调用错误:",e),e instanceof Error&&"AbortError"===e.name?(n="请求超时",s=`第${a+1}次尝试: 请求超时 (60秒)`):(n=`网络错误: ${e instanceof Error?e.message:"未知错误"}`,s=`第${a+1}次尝试: ${e instanceof Error?e.stack||e.message:"未知网络错误"}`),a<4){let e=2e3*Math.pow(2,a);console.log(`网络错误，${e}ms后重试...`),await new Promise(t=>setTimeout(t,e))}}return{rewrittenText:"",success:!1,error:`重试5次后仍然失败: ${n}`,processingTime:Date.now()-t,detailedError:s,retryCount:5}}async function a(e,t,r,n,l=3,c="gemini-2.5-flash-lite",u=!0){let m=Array(e.length),d=0,g=0,w=Date.now(),h=new i(l),p=async(a,i)=>{await h.acquire();let l=Date.now();try{let u=await s({originalText:a.content,rules:t,chapterTitle:a.title,chapterNumber:a.number,model:c}),h=Date.now()-l;u.tokensUsed&&(g+=u.tokensUsed);let p={success:u.success,content:u.rewrittenText,error:u.error,details:{apiKeyUsed:u.apiKeyUsed,tokensUsed:u.tokensUsed,model:u.model,processingTime:h,chapterNumber:a.number,chapterTitle:a.title}};if(m[i]=p,d++,n&&n(i,p),r){let t={completed:d,total:e.length,totalTokensUsed:g,totalTime:Date.now()-w,averageTimePerChapter:(Date.now()-w)/d,apiKeyStats:o.getStats(),currentChapter:{number:a.number,title:a.title,processingTime:h,apiKey:u.apiKeyUsed,tokens:u.tokensUsed}};r(d/e.length*100,a.number,t)}return await new Promise(e=>setTimeout(e,1e3)),u}catch(c){let t=Date.now(),s={success:!1,content:"",error:`处理失败: ${c instanceof Error?c.message:"未知错误"}`,details:{chapterNumber:a.number,chapterTitle:a.title,processingTime:t-l}};if(m[i]=s,d++,n&&n(i,s),r){let t={completed:d,total:e.length,totalTokensUsed:g,totalTime:Date.now()-w,averageTimePerChapter:(Date.now()-w)/d,apiKeyStats:o.getStats(),currentChapter:{number:a.number,title:a.title,error:c instanceof Error?c.message:"未知错误"}};r(d/e.length*100,a.number,t)}return null}finally{h.release()}},y=e.map((e,t)=>p(e,t));if(await Promise.all(y),u){let a=m.map((t,r)=>({result:t,index:r,chapter:e[r]})).filter(e=>!e.result.success);if(a.length>0){console.log(`开始恢复 ${a.length} 个失败的章节...`);let l=new i(1);for(let{index:i,chapter:u}of a){await l.acquire();try{console.log(`正在恢复第 ${u.number} 章: ${u.title}`),await new Promise(e=>setTimeout(e,5e3));let a=await s({originalText:u.content,rules:t,chapterTitle:u.title,chapterNumber:u.number,model:c});if(a.success){console.log(`成功恢复第 ${u.number} 章`);let t={success:!0,content:a.rewrittenText,error:void 0,details:{...a,chapterNumber:u.number,chapterTitle:u.title,isRecovered:!0}};if(m[i]=t,d++,n&&n(i,t),r){let t={completed:d,total:e.length,totalTokensUsed:g+(a.tokensUsed||0),totalTime:Date.now()-w,averageTimePerChapter:(Date.now()-w)/d,apiKeyStats:o.getStats(),currentChapter:{number:u.number,title:u.title,processingTime:a.processingTime,apiKey:a.apiKeyUsed,tokens:a.tokensUsed,isRecovered:!0}};r(d/e.length*100,u.number,t)}}else console.log(`第 ${u.number} 章恢复失败: ${a.error}`),m[i]={...m[i],error:`原始失败: ${m[i].error}; 恢复失败: ${a.error}`,details:{...m[i].details,recoveryAttempted:!0,recoveryError:a.error,recoveryDetailedError:a.detailedError}}}catch(e){console.error(`恢复第 ${u.number} 章时发生异常:`,e),m[i]={...m[i],error:`${m[i].error}; 恢复异常: ${e instanceof Error?e.message:"未知错误"}`,details:{...m[i].details,recoveryAttempted:!0,recoveryException:e instanceof Error?e.message:"未知错误"}}}finally{l.release()}}}}return m}class i{permits;waitQueue=[];constructor(e){this.permits=e}async acquire(){return this.permits>0?(this.permits--,Promise.resolve()):new Promise(e=>{this.waitQueue.push(e)})}release(){if(this.permits++,this.waitQueue.length>0){let e=this.waitQueue.shift();e&&(this.permits--,e())}}}async function l(){try{let e=await s({originalText:"这是一个测试文本。",rules:"保持原文不变"});return{success:e.success,error:e.error,details:{apiKeyUsed:e.apiKeyUsed,tokensUsed:e.tokensUsed,model:e.model,processingTime:e.processingTime,apiKeyStats:o.getStats()}}}catch(e){return{success:!1,error:`连接测试失败: ${e instanceof Error?e.message:"未知错误"}`,details:{apiKeyStats:o.getStats()}}}}function c(){return o.getStats()}function u(){t.forEach(e=>{e.requestCount=0,e.lastUsed=0,e.cooldownUntil=0})}let m={romance_focus:{name:"感情戏增强",description:"扩写男女主互动内容，对非感情戏部分一笔带过",rules:`请按照以下规则改写：
1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节
2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过
3. 增加角色间的情感张力和暧昧氛围
4. 保持故事主线不变，但重点突出感情发展`},character_fix:{name:"人设修正",description:"修正主角人设和对话风格",rules:`请按照以下规则改写：
1. 修正主角的性格设定，使其更加立体和讨喜
2. 改善对话风格，使其更加自然流畅
3. 去除过于中二或不合理的行为描写
4. 保持角色的核心特征，但优化表现方式`},toxic_content_removal:{name:"毒点清除",description:"移除送女、绿帽等毒点情节",rules:`请按照以下规则改写：
1. 完全移除或修改送女、绿帽、圣母等毒点情节
2. 删除或改写让读者不适的桥段
3. 保持故事逻辑的完整性
4. 用更合理的情节替代被删除的内容`},pacing_improvement:{name:"节奏优化",description:"优化故事节奏，删除拖沓内容",rules:`请按照以下规则改写：
1. 删除重复和拖沓的描写
2. 加快故事节奏，突出重点情节
3. 简化过于冗长的对话和心理描写
4. 保持故事的紧凑性和可读性`},custom:{name:"自定义规则",description:"用户自定义的改写规则",rules:""}};function d(){try{let{presetDb:t}=e.r(84168);t.getAll().forEach(e=>{m[`custom_${e.id}`]={name:e.name,description:e.description,rules:e.rules}})}catch(e){console.error("加载自定义预设失败:",e)}}function g(e,t,r){let n=`custom_${Date.now()}`;return m={...m,[n]:{name:e,description:t,rules:r}},n}async function w(t,r,n,o,a,i){try{let{novelContextDb:l,chapterContextDb:c}=e.r(84168),u=l.getByNovelId(t),m=c.getByChapter(t,r),d={originalText:n,rules:o,chapterTitle:a,chapterNumber:r,model:i,novelContext:u?{summary:u.summary,mainCharacters:u.mainCharacters,worldSetting:u.worldSetting,writingStyle:u.writingStyle,tone:u.tone}:void 0,chapterContext:m?{previousChapterSummary:m.previousChapterSummary,keyEvents:m.keyEvents,characterStates:m.characterStates,plotProgress:m.plotProgress,contextualNotes:m.contextualNotes}:void 0};return await s(d)}catch(e){return console.error("带上下文重写失败:",e),await s({originalText:n,rules:o,chapterTitle:a,chapterNumber:r,model:i})}}}];

//# sourceMappingURL=src_lib_gemini_ts_7aaf7081._.js.map