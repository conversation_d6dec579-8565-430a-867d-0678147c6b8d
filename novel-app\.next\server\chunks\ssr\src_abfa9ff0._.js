module.exports=[62573,a=>{"use strict";a.s(["chapterContextDb",()=>y,"chapterDb",()=>s,"characterDb",()=>v,"jobDb",()=>u,"novelContextDb",()=>x,"novelDb",()=>r,"presetDb",()=>w,"ruleDb",()=>t]);var b=a.i(22734),c=a.i(14747),d=a.i(54799);let e=c.default.join(process.cwd(),"data"),f=c.default.join(e,"novels.json"),g=c.default.join(e,"chapters.json"),h=c.default.join(e,"rewrite_rules.json"),i=c.default.join(e,"rewrite_jobs.json"),j=c.default.join(e,"characters.json"),k=c.default.join(e,"presets.json"),l=c.default.join(e,"novel-contexts.json"),m=c.default.join(e,"chapter-contexts.json");function n(){b.default.existsSync(e)||b.default.mkdirSync(e,{recursive:!0})}function o(a){if(n(),!b.default.existsSync(a))return[];try{let c=b.default.readFileSync(a,"utf-8");return JSON.parse(c)}catch(b){return console.error(`Error reading ${a}:`,b),[]}}function p(a,c){n();try{b.default.writeFileSync(a,JSON.stringify(c,null,2),"utf-8")}catch(b){throw console.error(`Error writing ${a}:`,b),b}}function q(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}let r={getAll:()=>o(f),getById:a=>o(f).find(b=>b.id===a),create:a=>{var b;let c=o(f),e=(b=a.title,d.default.createHash("md5").update(b).digest("hex").substring(0,18)),g=c.find(a=>a.id===e);if(g)return g.filename=a.filename,g.chapterCount=a.chapterCount,p(f,c),g;let h={...a,id:e,createdAt:new Date().toISOString()};return c.push(h),p(f,c),h},update:(a,b)=>{let c=o(f),d=c.findIndex(b=>b.id===a);return -1===d?null:(c[d]={...c[d],...b},p(f,c),c[d])},delete:a=>{let b=o(f),c=b.findIndex(b=>b.id===a);return -1!==c&&(b.splice(c,1),p(f,b),!0)}},s={getAll:()=>o(g),getByNovelId:a=>o(g).filter(b=>b.novelId===a),getById:a=>o(g).find(b=>b.id===a),create:a=>{let b=o(g),c={...a,id:q(),createdAt:new Date().toISOString()};return b.push(c),p(g,b),c},createBatch:a=>{let b=o(g),c=a.map(a=>({...a,id:q(),createdAt:new Date().toISOString()}));return b.push(...c),p(g,b),c},delete:a=>{let b=o(g),c=b.findIndex(b=>b.id===a);return -1!==c&&(b.splice(c,1),p(g,b),!0)},deleteByNovelId:a=>{let b=o(g).filter(b=>b.novelId!==a);return p(g,b),!0}},t={getAll:()=>o(h),getById:a=>o(h).find(b=>b.id===a),create:a=>{let b=o(h),c={...a,id:q(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return b.push(c),p(h,b),c},update:(a,b)=>{let c=o(h),d=c.findIndex(b=>b.id===a);return -1===d?null:(c[d]={...c[d],...b,updatedAt:new Date().toISOString()},p(h,c),c[d])},delete:a=>{let b=o(h),c=b.findIndex(b=>b.id===a);return -1!==c&&(b.splice(c,1),p(h,b),!0)}},u={getAll:()=>o(i),getById:a=>o(i).find(b=>b.id===a),create:a=>{let b=o(i),c={...a,id:q(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return b.push(c),p(i,b),c},update:(a,b)=>{let c=o(i),d=c.findIndex(b=>b.id===a);return -1===d?null:(c[d]={...c[d],...b,updatedAt:new Date().toISOString()},p(i,c),c[d])},delete:a=>{let b=o(i),c=b.findIndex(b=>b.id===a);return -1!==c&&(b.splice(c,1),p(i,b),!0)}},v={getAll:()=>o(j),getByNovelId:a=>o(j).filter(b=>b.novelId===a),getById:a=>o(j).find(b=>b.id===a),create:a=>{let b=o(j),c={...a,id:q(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return b.push(c),p(j,b),c},update:(a,b)=>{let c=o(j),d=c.findIndex(b=>b.id===a);return -1===d?null:(c[d]={...c[d],...b,updatedAt:new Date().toISOString()},p(j,c),c[d])},delete:a=>{let b=o(j),c=b.findIndex(b=>b.id===a);return -1!==c&&(b.splice(c,1),p(j,b),!0)},deleteByNovelId:a=>{let b=o(j).filter(b=>b.novelId!==a);return p(j,b),!0}},w={getAll:()=>o(k),getById:a=>o(k).find(b=>b.id===a),create:a=>{let b=o(k),c={...a,id:q(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return b.push(c),p(k,b),c},update:(a,b)=>{let c=o(k),d=c.findIndex(b=>b.id===a);return -1===d?null:(c[d]={...c[d],...b,updatedAt:new Date().toISOString()},p(k,c),c[d])},delete:a=>{let b=o(k),c=b.findIndex(b=>b.id===a);return -1!==c&&(b.splice(c,1),p(k,b),!0)}},x={getAll:()=>o(l),getByNovelId:a=>o(l).find(b=>b.novelId===a),getById:a=>o(l).find(b=>b.id===a),create:a=>{let b=o(l),c={...a,id:q(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return b.push(c),p(l,b),c},update:(a,b)=>{let c=o(l),d=c.findIndex(b=>b.id===a);if(-1!==d)return c[d]={...c[d],...b,updatedAt:new Date().toISOString()},p(l,c),c[d]},delete:a=>{let b=o(l),c=b.findIndex(b=>b.id===a);return -1!==c&&(b.splice(c,1),p(l,b),!0)}},y={getAll:()=>o(m),getByNovelId:a=>o(m).filter(b=>b.novelId===a),getByChapter:(a,b)=>o(m).find(c=>c.novelId===a&&c.chapterNumber===b),getById:a=>o(m).find(b=>b.id===a),create:a=>{let b=o(m),c={...a,id:q(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return b.push(c),p(m,b),c},update:(a,b)=>{let c=o(m),d=c.findIndex(b=>b.id===a);if(-1!==d)return c[d]={...c[d],...b,updatedAt:new Date().toISOString()},p(m,c),c[d]},delete:a=>{let b=o(m),c=b.findIndex(b=>b.id===a);return -1!==c&&(b.splice(c,1),p(m,b),!0)},getContextWindow:(a,b,c=2)=>{let d=o(m).filter(b=>b.novelId===a),e=Math.max(1,b-c),f=b+c;return d.filter(a=>a.chapterNumber>=e&&a.chapterNumber<=f).sort((a,b)=>a.chapterNumber-b.chapterNumber)}}},40777,a=>{"use strict";a.s(["default",()=>U],40777);var b=a.i(87924),c=a.i(72131),d=a.i(38246),e=a.i(53722),f=a.i(70106);let g=(0,f.default)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),h=(0,f.default)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);function i({selectedNovel:a,onNovelSelect:d,disabled:f}){let[i,j]=(0,c.useState)([]),[k,l]=(0,c.useState)([]),[m,n]=(0,c.useState)(!1),[o,p]=(0,c.useState)(null);(0,c.useEffect)(()=>{q()},[]);let q=async()=>{n(!0);try{let a=await fetch("/api/novels"),b=await a.json();b.success?(j(b.data.novels),l(b.data.availableFiles)):console.error("加载小说列表失败:",b.error)}catch(a){console.error("加载小说列表失败:",a)}finally{n(!1)}},r=async(a,b=!1)=>{p(a);try{let c=await fetch("/api/novels",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filename:a,reparse:b})}),d=await c.json();d.success?(await q(),alert(d.message)):alert(`解析失败: ${d.error}`)}catch(a){console.error("解析小说失败:",a),alert("解析小说失败")}finally{p(null)}};return(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,b.jsxs)("h2",{className:"text-lg font-semibold text-gray-800 flex items-center",children:[(0,b.jsx)(e.BookOpen,{className:"mr-2",size:18}),"选择小说"]}),(0,b.jsx)("button",{onClick:q,disabled:m||f,className:"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50",title:"刷新列表",children:(0,b.jsx)(g,{className:`${m?"animate-spin":""}`,size:16})})]}),m?(0,b.jsx)("div",{className:"text-center py-8 text-gray-500",children:"加载中..."}):(0,b.jsxs)("div",{className:"space-y-2",children:[i.length>0&&(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"已解析的小说"}),(0,b.jsx)("div",{className:"space-y-1",children:i.map(c=>(0,b.jsxs)("div",{onClick:()=>{!f&&d(c)},className:`p-2 border rounded cursor-pointer transition-colors ${a?.id===c.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"} ${f?"opacity-50 cursor-not-allowed":""}`,children:[(0,b.jsx)("div",{className:"font-medium text-gray-800 text-sm",children:c.title}),(0,b.jsxs)("div",{className:"text-xs text-gray-500",children:[c.chapterCount||0," 章节 • ",c.filename]})]},c.id))})]}),k.filter(a=>!a.parsed).length>0&&(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"未解析的文件"}),(0,b.jsx)("div",{className:"space-y-1",children:k.filter(a=>!a.parsed).map(a=>(0,b.jsx)("div",{className:"p-2 border border-gray-200 rounded bg-gray-50",children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,b.jsx)("div",{className:"font-medium text-gray-800 text-sm truncate",children:a.filename}),(0,b.jsx)("div",{className:"text-xs text-gray-500",children:"未解析"})]}),(0,b.jsx)("button",{onClick:()=>r(a.filename),disabled:o===a.filename||f,className:"flex items-center px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 ml-2",children:o===a.filename?(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(g,{className:"animate-spin mr-1",size:12}),"解析中"]}):(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(h,{className:"mr-1",size:12}),"解析"]})})]})},a.filename))})]}),0===k.length&&(0,b.jsxs)("div",{className:"text-center py-6 text-gray-500",children:[(0,b.jsx)(e.BookOpen,{className:"mx-auto mb-2",size:32}),(0,b.jsx)("p",{className:"text-sm",children:"novels 文件夹中没有找到小说文件"}),(0,b.jsx)("p",{className:"text-xs",children:"请将 .txt 或 .md 文件放入 novels 文件夹"})]})]}),a&&(0,b.jsxs)("div",{className:"mt-3 p-2 bg-blue-50 border border-blue-200 rounded",children:[(0,b.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,b.jsx)("strong",{children:"已选择:"})," ",a.title]}),(0,b.jsxs)("div",{className:"text-xs text-blue-600",children:[a.chapterCount||0," 章节"]})]})]})}let j=(0,f.default)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),k=(0,f.default)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);function l({novel:a,selectedChapters:d,onChaptersChange:e,disabled:f}){let[g,h]=(0,c.useState)([]),[i,l]=(0,c.useState)(!1),[m,n]=(0,c.useState)([]);(0,c.useEffect)(()=>{a?o(a.id):(h([]),n([]))},[a]),(0,c.useEffect)(()=>{d&&g.length>0?n(p(d,g.length)):n([])},[d,g]);let o=async a=>{l(!0);try{let b=await fetch(`/api/chapters?novelId=${a}`),c=await b.json();c.success?h(c.data):console.error("加载章节列表失败:",c.error)}catch(a){console.error("加载章节列表失败:",a)}finally{l(!1)}},p=(a,b)=>{let c=[];for(let d of a.split(",").map(a=>a.trim()))if(d.includes("-")){let[a,e]=d.split("-").map(a=>parseInt(a.trim()));if(!isNaN(a)&&!isNaN(e)&&a<=e)for(let d=a;d<=Math.min(e,b);d++)d>0&&!c.includes(d)&&c.push(d)}else{let a=parseInt(d);!isNaN(a)&&a>0&&a<=b&&!c.includes(a)&&c.push(a)}return c.sort((a,b)=>a-b)},q=a=>{if(f||0===g.length)return;let b="";switch(a){case"all":b=`1-${g.length}`;break;case"first10":b=`1-${Math.min(10,g.length)}`;break;case"last10":b=`${Math.max(1,g.length-9)}-${g.length}`}e(b)};return(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,b.jsxs)("h2",{className:"text-lg font-semibold text-gray-800 mb-3 flex items-center",children:[(0,b.jsx)(j,{className:"mr-2",size:18}),"选择章节 ",a&&(0,b.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["共 ",g.length," 章"]})]}),a?i?(0,b.jsx)("div",{className:"text-center py-6 text-gray-500 text-sm",children:"加载中..."}):(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"章节范围"}),(0,b.jsx)("input",{type:"text",value:d,onChange:a=>e(a.target.value),disabled:f,placeholder:"例如: 1-5,7,10-12",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"}),(0,b.jsxs)("div",{className:"mt-1 flex items-center text-xs text-gray-500",children:[(0,b.jsx)(k,{className:"mr-1",size:12}),"支持范围(1-5)、单个章节(7)、组合(1-5,7,10-12)"]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"快速选择"}),(0,b.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,b.jsxs)("button",{onClick:()=>q("all"),disabled:f,className:"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50",children:["全部章节 (1-",g.length,")"]}),(0,b.jsx)("button",{onClick:()=>q("first10"),disabled:f,className:"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50",children:"前10章"}),(0,b.jsx)("button",{onClick:()=>q("last10"),disabled:f,className:"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50",children:"后10章"})]})]}),m.length>0&&(0,b.jsxs)("div",{children:[(0,b.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["将要改写的章节 (",m.length," 章)"]}),(0,b.jsx)("div",{className:"max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3 bg-gray-50",children:(0,b.jsx)("div",{className:"grid grid-cols-1 gap-1 text-sm",children:m.map(a=>{let c=g.find(b=>b.chapterNumber===a);return(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsxs)("span",{className:"font-medium text-blue-600 w-12",children:["第",a,"章"]}),(0,b.jsx)("span",{className:"text-gray-700 truncate",children:c?.title||"未知标题"})]},a)})})})]}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["所有章节 (",g.length," 章)"]}),(0,b.jsx)("div",{className:"max-h-60 overflow-y-auto border border-gray-200 rounded-md",children:g.map(a=>(0,b.jsx)("div",{className:`p-2 border-b border-gray-100 last:border-b-0 ${m.includes(a.chapterNumber)?"bg-blue-50 border-l-4 border-l-blue-500":"hover:bg-gray-50"}`,children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,b.jsxs)("div",{className:"font-medium text-gray-800 truncate",children:["第",a.chapterNumber,"章 ",a.title]}),(0,b.jsxs)("div",{className:"text-xs text-gray-500",children:[a.content.length," 字符"]})]}),m.includes(a.chapterNumber)&&(0,b.jsx)("div",{className:"ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:"已选择"})]})},a.id))})]})]}):(0,b.jsxs)("div",{className:"text-center py-6 text-gray-500",children:[(0,b.jsx)(j,{className:"mx-auto mb-2",size:32}),(0,b.jsx)("p",{className:"text-sm",children:"请先选择一部小说"})]})]})}let m=(0,f.default)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var n=a.i(54933);let o=(0,f.default)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),p=[{key:"AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw",name:"My First Project",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y",name:"ankibot",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY",name:"Generative Language Client",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc",name:"In The Novel",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk",name:"chat",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0}];class q{keys=[...p];getBestAvailableKey(){let a=Date.now(),b=this.keys.filter(b=>b.cooldownUntil<=a);return 0===b.length?this.keys.reduce((a,b)=>b.cooldownUntil<a.cooldownUntil?b:a):b.reduce((a,b)=>b.weight/(b.requestCount+1)>a.weight/(a.requestCount+1)?b:a)}recordUsage(a,b){let c=this.keys.find(b=>b.name===a);c&&(c.requestCount++,c.lastUsed=Date.now(),b||(c.cooldownUntil=Date.now()+6e4))}getStats(){return this.keys.map(a=>({name:a.name,requestCount:a.requestCount,weight:a.weight,isAvailable:a.cooldownUntil<=Date.now(),cooldownRemaining:Math.max(0,a.cooldownUntil-Date.now())}))}}new q;let r={romance_focus:{name:"感情戏增强",description:"扩写男女主互动内容，对非感情戏部分一笔带过",rules:`请按照以下规则改写：
1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节
2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过
3. 增加角色间的情感张力和暧昧氛围
4. 保持故事主线不变，但重点突出感情发展`},character_fix:{name:"人设修正",description:"修正主角人设和对话风格",rules:`请按照以下规则改写：
1. 修正主角的性格设定，使其更加立体和讨喜
2. 改善对话风格，使其更加自然流畅
3. 去除过于中二或不合理的行为描写
4. 保持角色的核心特征，但优化表现方式`},toxic_content_removal:{name:"毒点清除",description:"移除送女、绿帽等毒点情节",rules:`请按照以下规则改写：
1. 完全移除或修改送女、绿帽、圣母等毒点情节
2. 删除或改写让读者不适的桥段
3. 保持故事逻辑的完整性
4. 用更合理的情节替代被删除的内容`},pacing_improvement:{name:"节奏优化",description:"优化故事节奏，删除拖沓内容",rules:`请按照以下规则改写：
1. 删除重复和拖沓的描写
2. 加快故事节奏，突出重点情节
3. 简化过于冗长的对话和心理描写
4. 保持故事的紧凑性和可读性`},custom:{name:"自定义规则",description:"用户自定义的改写规则",rules:""}};function s({rules:a,onRulesChange:d,disabled:e,onSaveToPreset:f}){let[g,h]=(0,c.useState)(!1),[i,j]=(0,c.useState)([]),[k,l]=(0,c.useState)({});(0,c.useEffect)(()=>{p()},[]);let p=async()=>{try{let a=await fetch("/api/presets"),b=await a.json();if(b.success){j(b.data);let a={...r};b.data.forEach(b=>{a[`custom_${b.id}`]={name:b.name,description:b.description,rules:b.rules}}),l(a)}}catch(a){console.error("加载自定义预设失败:",a),l(r)}},q=async()=>{a.trim()&&f&&(await f(a),await p())},s=Object.entries(k).filter(([a])=>"custom"!==a);return(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,b.jsxs)("h2",{className:"text-xl font-semibold text-gray-800 flex items-center",children:[(0,b.jsx)(m,{className:"mr-2",size:20}),"改写规则"]}),(0,b.jsxs)("div",{className:"flex space-x-2",children:[(0,b.jsx)("button",{onClick:q,disabled:e||!a.trim(),className:"p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50",title:"保存为预设",children:(0,b.jsx)(o,{size:16})}),(0,b.jsx)("button",{onClick:()=>h(!g),disabled:e,className:"p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50",title:"预设规则",children:(0,b.jsx)(n.Wand2,{size:16})})]})]}),g&&(0,b.jsxs)("div",{className:"mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg",children:[(0,b.jsx)("h3",{className:"font-medium text-gray-800 mb-2 text-sm",children:"选择预设规则"}),(0,b.jsx)("div",{className:"grid grid-cols-1 gap-1",children:s.map(([a,c])=>(0,b.jsxs)("button",{onClick:()=>(a=>{let b=k[a];b&&(d(b.rules),h(!1))})(a),disabled:e,className:"text-left p-2 border border-gray-200 rounded hover:border-blue-300 hover:bg-blue-50 disabled:opacity-50 transition-colors",children:[(0,b.jsx)("div",{className:"font-medium text-gray-800 text-sm",children:c.name}),(0,b.jsx)("div",{className:"text-xs text-gray-600",children:c.description})]},a))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"改写规则内容"}),(0,b.jsx)("textarea",{value:a,onChange:a=>d(a.target.value),disabled:e,placeholder:"请输入详细的改写规则，例如：  1. 扩写男女主角之间的互动情节 2. 对战斗场面一笔带过 3. 增加情感描写和心理活动 4. 修改不合理的人物行为 ...",className:"w-full h-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none disabled:bg-gray-100"}),(0,b.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:[a.length," 字符 • 建议详细描述改写要求以获得更好的效果"]})]})]})}let t=(0,f.default)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),u=(0,f.default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),v=(0,f.default)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function w({novelId:a,characters:d,onCharactersChange:e,disabled:f}){let[g,h]=(0,c.useState)(!1),[i,j]=(0,c.useState)(!1),[k,l]=(0,c.useState)({name:"",role:"其他",description:""});(0,c.useEffect)(()=>{a?m():e([])},[a]);let m=async()=>{if(a)try{let b=await fetch(`/api/characters?novelId=${a}`),c=await b.json();c.success&&e(c.data)}catch(a){console.error("加载人物设定失败:",a)}},n=async()=>{if(k.name.trim()&&a){j(!0);try{let b=await fetch("/api/characters",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({novelId:a,name:k.name,role:k.role,description:k.description})}),c=await b.json();c.success?(await m(),l({name:"",role:"其他",description:""}),h(!1)):alert(`添加失败: ${c.error}`)}catch(a){console.error("添加人物失败:",a),alert("添加人物失败")}finally{j(!1)}}},o=async a=>{if(confirm("确定要删除这个人物设定吗？"))try{let b=await fetch(`/api/characters?id=${a}`,{method:"DELETE"}),c=await b.json();c.success?await m():alert(`删除失败: ${c.error}`)}catch(a){console.error("删除人物失败:",a),alert("删除人物失败")}};return(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,b.jsxs)("h2",{className:"text-lg font-semibold text-gray-800 flex items-center",children:[(0,b.jsx)(t,{className:"mr-2",size:18}),"人物设定"]}),(0,b.jsx)("button",{onClick:()=>h(!g),disabled:f,className:"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50",title:"添加人物",children:(0,b.jsx)(u,{size:16})})]}),g&&(0,b.jsx)("div",{className:"mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg",children:(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsxs)("div",{className:"flex space-x-2",children:[(0,b.jsx)("input",{type:"text",placeholder:"人物名称",value:k.name,onChange:a=>l({...k,name:a.target.value}),className:"flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"}),(0,b.jsx)("select",{value:k.role,onChange:a=>l({...k,role:a.target.value}),className:"px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500",children:["男主","女主","配角","反派","其他"].map(a=>(0,b.jsx)("option",{value:a,children:a},a))})]}),(0,b.jsx)("input",{type:"text",placeholder:"备注描述",value:k.description,onChange:a=>l({...k,description:a.target.value}),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"}),(0,b.jsxs)("div",{className:"flex space-x-2",children:[(0,b.jsx)("button",{onClick:n,className:"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700",children:"添加"}),(0,b.jsx)("button",{onClick:()=>h(!1),className:"px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600",children:"取消"})]})]})}),(0,b.jsx)("div",{className:"space-y-2",children:0===d.length?(0,b.jsx)("div",{className:"text-center py-4 text-gray-500 text-sm",children:"暂无人物设定"}):d.map(a=>(0,b.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,b.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("span",{className:"font-medium text-gray-800 text-sm",children:a.name}),(0,b.jsx)("span",{className:`px-2 py-0.5 text-xs rounded ${"男主"===a.role?"bg-blue-100 text-blue-800":"女主"===a.role?"bg-pink-100 text-pink-800":"配角"===a.role?"bg-green-100 text-green-800":"反派"===a.role?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:a.role})]}),a.description&&(0,b.jsx)("div",{className:"text-xs text-gray-600 mt-1 truncate",children:a.description})]}),(0,b.jsx)("button",{onClick:()=>o(a.id),disabled:f,className:"p-1 text-gray-400 hover:text-red-600 disabled:opacity-50",title:"删除",children:(0,b.jsx)(v,{size:14})})]},a.id))})]})}var x=a.i(16201),y=a.i(62722);let z=(0,f.default)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),A=(0,f.default)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),B=(0,f.default)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);function C({jobId:a,failedChapters:d,rules:e,model:f="gemini-2.5-flash-lite",onRetryStart:g,onRetryComplete:h}){let[i,j]=(0,c.useState)([]),[k,l]=(0,c.useState)(!1),[m,n]=(0,c.useState)(""),o=async()=>{if(0===i.length)return void alert("请选择要重试的章节");l(!0),n("正在重试失败的章节..."),g&&g();try{let b=await fetch("/api/rewrite/retry",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({jobId:a,chapterNumbers:i,rules:e,model:f})}),c=await b.json();c.success?(n(`重试任务已创建，正在处理 ${i.length} 个章节...`),h&&h(!0,c.data.message)):(n(`重试失败: ${c.error}`),h&&h(!1,c.error))}catch(b){let a=`重试请求失败: ${b instanceof Error?b.message:"未知错误"}`;n(a),h&&h(!1,a)}finally{l(!1)}};return 0===d.length?null:(0,b.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mt-4",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,b.jsxs)("h3",{className:"text-lg font-semibold text-red-800",children:["失败章节 (",d.length," 个)"]}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)("button",{onClick:()=>{i.length===d.length?j([]):j(d.map(a=>a.chapterNumber))},className:"px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded transition-colors",children:i.length===d.length?"取消全选":"全选"}),(0,b.jsx)("button",{onClick:o,disabled:k||0===i.length,className:"px-4 py-1 text-sm bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded transition-colors",children:k?"重试中...":`重试选中章节 (${i.length})`})]})]}),m&&(0,b.jsx)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded text-blue-800",children:m}),(0,b.jsx)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:d.map(a=>(0,b.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-white border border-red-200 rounded",children:[(0,b.jsx)("input",{type:"checkbox",checked:i.includes(a.chapterNumber),onChange:()=>{var b;return b=a.chapterNumber,void j(a=>a.includes(b)?a.filter(a=>a!==b):[...a,b])},className:"mt-1"}),(0,b.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,b.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,b.jsxs)("span",{className:"font-medium text-gray-900",children:["第 ",a.chapterNumber," 章"]}),(0,b.jsx)("span",{className:"text-gray-600 truncate",children:a.chapterTitle})]}),(0,b.jsxs)("div",{className:"text-sm text-red-600 mb-1",children:["错误: ",a.error||"未知错误"]}),a.detailedError&&(0,b.jsxs)("details",{className:"text-xs text-gray-500",children:[(0,b.jsx)("summary",{className:"cursor-pointer hover:text-gray-700",children:"详细错误信息"}),(0,b.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded border text-xs font-mono whitespace-pre-wrap",children:a.detailedError})]}),(0,b.jsxs)("div",{className:"flex gap-4 text-xs text-gray-500 mt-1",children:[a.apiKeyUsed&&(0,b.jsxs)("span",{children:["API Key: ",a.apiKeyUsed]}),a.processingTime&&(0,b.jsxs)("span",{children:["处理时间: ",a.processingTime,"ms"]})]})]})]},a.chapterNumber))}),(0,b.jsxs)("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded",children:[(0,b.jsx)("h4",{className:"font-medium text-yellow-800 mb-2",children:"重试建议:"}),(0,b.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,b.jsx)("li",{children:"• 重试将使用更保守的策略，串行处理章节以避免API限制"}),(0,b.jsx)("li",{children:"• 如果仍然失败，可能需要检查API key配额或调整改写规则"}),(0,b.jsx)("li",{children:"• 建议在API使用量较低的时间段进行重试"})]})]})]})}let D=(0,f.default)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);function E({jobId:a,isOpen:d,onClose:e}){let[f,h]=(0,c.useState)(null),[i,j]=(0,c.useState)(!1),[l,m]=(0,c.useState)(null),n=async()=>{j(!0),m(null);try{let b=await fetch(`/api/rewrite/diagnostics?jobId=${a}`),c=await b.json();c.success?h(c.data):m(c.error)}catch(a){m(`获取诊断信息失败: ${a instanceof Error?a.message:"未知错误"}`)}finally{j(!1)}};return((0,c.useEffect)(()=>{d&&a&&n()},[d,a]),d)?(0,b.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,b.jsx)("h2",{className:"text-xl font-semibold",children:"任务诊断报告"}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)("button",{onClick:n,disabled:i,className:"p-2 text-gray-500 hover:text-gray-700 disabled:opacity-50",children:(0,b.jsx)(g,{className:`w-4 h-4 ${i?"animate-spin":""}`})}),(0,b.jsx)("button",{onClick:e,className:"p-2 text-gray-500 hover:text-gray-700",children:"✕"})]})]}),(0,b.jsxs)("div",{className:"p-4 overflow-y-auto max-h-[calc(90vh-80px)]",children:[i&&(0,b.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),(0,b.jsx)("span",{className:"ml-2",children:"正在获取诊断信息..."})]}),l&&(0,b.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-4",children:(0,b.jsxs)("div",{className:"flex items-center text-red-700",children:[(0,b.jsx)(y.XCircle,{className:"w-5 h-5 mr-2"}),(0,b.jsx)("span",{children:l})]})}),f&&(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,b.jsx)("h3",{className:"font-semibold mb-3",children:"任务概览"}),(0,b.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"text-gray-600",children:"状态:"}),(0,b.jsx)("span",{className:"ml-2 font-medium",children:f.jobInfo.status})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"text-gray-600",children:"进度:"}),(0,b.jsxs)("span",{className:"ml-2 font-medium",children:[f.jobInfo.progress,"%"]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"text-gray-600",children:"成功:"}),(0,b.jsxs)("span",{className:"ml-2 font-medium text-green-600",children:[f.jobInfo.completedChapters,"/",f.jobInfo.totalChapters]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"text-gray-600",children:"失败:"}),(0,b.jsx)("span",{className:"ml-2 font-medium text-red-600",children:f.jobInfo.failedChapters})]})]})]}),(0,b.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,b.jsx)("h3",{className:"font-semibold mb-3",children:"API Key状态"}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium mb-2",children:"连接测试"}),(0,b.jsxs)("div",{className:`flex items-center ${f.apiKeyStatus.connectionTest.success?"text-green-600":"text-red-600"}`,children:[f.apiKeyStatus.connectionTest.success?(0,b.jsx)(x.CheckCircle,{className:"w-4 h-4 mr-2"}):(0,b.jsx)(y.XCircle,{className:"w-4 h-4 mr-2"}),(0,b.jsx)("span",{className:"text-sm",children:f.apiKeyStatus.connectionTest.success?"连接正常":"连接失败"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium mb-2",children:"Key统计"}),(0,b.jsxs)("div",{className:"text-sm text-gray-600",children:["可用: ",f.systemHealth.availableKeys,"/",f.systemHealth.totalKeys," | 总调用: ",f.systemHealth.totalApiCalls]})]})]}),(0,b.jsxs)("div",{className:"mt-3",children:[(0,b.jsx)("h4",{className:"font-medium mb-2",children:"详细状态"}),(0,b.jsx)("div",{className:"space-y-1 text-sm",children:f.apiKeyStatus.stats.map((a,c)=>(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsx)("span",{children:a.name}),(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsxs)("span",{className:"text-gray-600",children:[a.requestCount," 次调用"]}),(0,b.jsx)("span",{className:`px-2 py-1 rounded text-xs ${a.isAvailable?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}`,children:a.isAvailable?"可用":"冷却中"})]})]},c))})]})]}),f.errorAnalysis.totalFailures>0&&(0,b.jsxs)("div",{className:"bg-red-50 rounded-lg p-4",children:[(0,b.jsx)("h3",{className:"font-semibold mb-3",children:"错误分析"}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium mb-2",children:"错误类型分布"}),(0,b.jsx)("div",{className:"space-y-1 text-sm",children:Object.entries(f.errorAnalysis.errorTypes).map(([a,c])=>(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{children:a}),(0,b.jsx)("span",{className:"font-medium",children:c})]},a))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium mb-2",children:"关键信息"}),(0,b.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,b.jsxs)("div",{children:["最常见错误: ",(0,b.jsx)("span",{className:"font-medium",children:f.errorAnalysis.mostCommonError})]}),(0,b.jsxs)("div",{children:["问题API Key: ",(0,b.jsx)("span",{className:"font-medium",children:f.errorAnalysis.problematicApiKey})]}),(0,b.jsxs)("div",{children:["超时错误: ",(0,b.jsx)("span",{className:"font-medium",children:f.errorAnalysis.patterns.timeoutErrors})]}),(0,b.jsxs)("div",{children:["内容错误: ",(0,b.jsx)("span",{className:"font-medium",children:f.errorAnalysis.patterns.contentErrors})]})]})]})]})]}),(0,b.jsxs)("div",{className:"bg-yellow-50 rounded-lg p-4",children:[(0,b.jsx)("h3",{className:"font-semibold mb-3",children:"改进建议"}),(0,b.jsx)("div",{className:"space-y-3",children:f.recommendations.map((a,c)=>(0,b.jsxs)("div",{className:"border border-yellow-200 rounded p-3",children:[(0,b.jsxs)("div",{className:"flex items-center mb-2",children:[(a=>{switch(a){case"error":return(0,b.jsx)(y.XCircle,{className:"w-4 h-4 text-red-500"});case"warning":return(0,b.jsx)(D,{className:"w-4 h-4 text-yellow-500"});case"info":return(0,b.jsx)(k,{className:"w-4 h-4 text-blue-500"});default:return(0,b.jsx)(x.CheckCircle,{className:"w-4 h-4 text-green-500"})}})(a.type),(0,b.jsx)("span",{className:"ml-2 font-medium",children:a.category})]}),(0,b.jsx)("p",{className:"text-sm text-gray-700 mb-2",children:a.message}),(0,b.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:a.actions.map((a,c)=>(0,b.jsxs)("li",{className:"flex items-start",children:[(0,b.jsx)("span",{className:"mr-2",children:"•"}),(0,b.jsx)("span",{children:a})]},c))})]},c))})]})]})]})]})}):null}function F({jobId:a,onComplete:d}){let[e,f]=(0,c.useState)(null),[g,h]=(0,c.useState)(!0),[i,j]=(0,c.useState)(!1),[k,l]=(0,c.useState)(null),[m,n]=(0,c.useState)(!1);(0,c.useEffect)(()=>{j(!1),l(null),h(!0),f(null);let a=setInterval(o,2e3);return o(),()=>clearInterval(a)},[a]);let o=async()=>{try{let b=await fetch(`/api/jobs?jobId=${a}`);if(!b.ok)throw Error(`HTTP error! status: ${b.status}`);let c=await b.json();if(c&&c.success&&c.data){let a=c.data;null===k&&l(a.status),f(a),h(!1),"completed"!==a.status&&"failed"!==a.status||null===k||"completed"===k||"failed"===k||i||(j(!0),setTimeout(()=>{d()},2e3))}else console.error("获取任务状态失败:",c?.error||"响应格式错误")}catch(a){console.error("获取任务状态失败:",a)}};return g?(0,b.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,b.jsxs)("div",{className:"text-center py-4",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"}),(0,b.jsx)("p",{className:"text-gray-600",children:"获取任务状态中..."})]})}):e?(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"改写进度"}),("completed"===e.status||"failed"===e.status)&&(0,b.jsxs)("button",{onClick:()=>n(!0),className:"flex items-center gap-1 px-3 py-1 text-sm bg-blue-100 hover:bg-blue-200 text-blue-700 rounded transition-colors",children:[(0,b.jsx)(B,{className:"w-4 h-4"}),"诊断"]})]}),(0,b.jsx)("div",{className:`p-4 rounded-lg border ${(a=>{switch(a){case"pending":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"processing":return"text-blue-600 bg-blue-50 border-blue-200";case"completed":return"text-green-600 bg-green-50 border-green-200";case"failed":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}})(e.status)} mb-4`,children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center",children:[(a=>{switch(a){case"pending":return(0,b.jsx)(z,{className:"text-yellow-500",size:20});case"processing":return(0,b.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"});case"completed":return(0,b.jsx)(x.CheckCircle,{className:"text-green-500",size:20});case"failed":return(0,b.jsx)(y.XCircle,{className:"text-red-500",size:20});default:return(0,b.jsx)(A,{className:"text-gray-500",size:20})}})(e.status),(0,b.jsx)("span",{className:"ml-2 font-medium",children:(a=>{switch(a){case"pending":return"等待处理";case"processing":return"正在改写";case"completed":return"改写完成";case"failed":return"改写失败";default:return"未知状态"}})(e.status)})]}),(0,b.jsxs)("span",{className:"text-sm",children:[e.progress,"%"]})]})}),(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-1",children:[(0,b.jsx)("span",{children:"进度"}),(0,b.jsxs)("span",{children:[e.progress,"%"]})]}),(0,b.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,b.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${"completed"===e.status?"bg-green-500":"failed"===e.status?"bg-red-500":"bg-blue-500"}`,style:{width:`${e.progress}%`}})})]}),e.details&&(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,b.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[(0,b.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"章节统计"}),(0,b.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,b.jsxs)("div",{children:["总章节: ",e.details.totalChapters]}),(0,b.jsxs)("div",{children:["已完成: ",e.details.completedChapters]}),(0,b.jsxs)("div",{children:["失败: ",e.details.failedChapters]}),(0,b.jsxs)("div",{children:["剩余: ",e.details.totalChapters-e.details.completedChapters-e.details.failedChapters]})]})]}),(0,b.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[(0,b.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"性能统计"}),(0,b.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,b.jsxs)("div",{children:["总耗时: ",Math.round(e.details.totalProcessingTime/1e3),"秒"]}),(0,b.jsxs)("div",{children:["平均每章: ",Math.round(e.details.averageTimePerChapter/1e3),"秒"]}),(0,b.jsxs)("div",{children:["Token消耗: ",e.details.totalTokensUsed.toLocaleString()]}),(0,b.jsxs)("div",{children:["模型: ",e.details.model]})]})]})]}),e.details?.apiKeyStats&&e.details.apiKeyStats.length>0&&(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"API Key 使用状态"}),(0,b.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2",children:e.details.apiKeyStats.map((a,c)=>(0,b.jsxs)("div",{className:`p-2 rounded border text-xs ${a.isAvailable?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:[(0,b.jsx)("div",{className:"font-medium",children:a.name}),(0,b.jsxs)("div",{children:["权重: ",a.weight,"x"]}),(0,b.jsxs)("div",{children:["使用次数: ",a.requestCount]}),(0,b.jsx)("div",{className:a.isAvailable?"text-green-600":"text-red-600",children:a.isAvailable?"可用":"冷却中"}),a.cooldownRemaining&&a.cooldownRemaining>0&&(0,b.jsxs)("div",{className:"text-red-500",children:["冷却: ",Math.round(a.cooldownRemaining/1e3),"s"]})]},c))})]}),(0,b.jsxs)("div",{className:"text-sm text-gray-500 space-y-1",children:[(0,b.jsxs)("div",{children:["开始时间: ",new Date(e.createdAt).toLocaleString()]}),(0,b.jsxs)("div",{children:["更新时间: ",new Date(e.updatedAt).toLocaleString()]}),e.details?.totalProcessingTime&&"completed"===e.status&&(0,b.jsxs)("div",{children:["总耗时: ",Math.round(e.details.totalProcessingTime/1e3)," 秒"]})]}),e.result&&(0,b.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg",children:[(0,b.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"结果信息"}),(0,b.jsx)("p",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:e.result})]}),"completed"===e.status&&(0,b.jsx)("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,b.jsxs)("div",{className:"flex items-center text-green-700",children:[(0,b.jsx)(x.CheckCircle,{className:"mr-2",size:16}),(0,b.jsx)("span",{className:"text-sm",children:"改写完成！改写后的文件已保存到 data/rewritten 目录中。"})]})}),"failed"===e.status&&(0,b.jsx)("div",{className:"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,b.jsxs)("div",{className:"flex items-center text-red-700",children:[(0,b.jsx)(y.XCircle,{className:"mr-2",size:16}),(0,b.jsx)("span",{className:"text-sm",children:"改写失败，请检查错误信息并重试。"})]})}),"processing"===e.status&&(0,b.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,b.jsxs)("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,b.jsxs)("div",{className:"flex items-center text-blue-700",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"}),(0,b.jsx)("span",{className:"text-sm",children:"正在使用 Gemini AI 改写章节，请耐心等待..."})]}),e.details&&(0,b.jsxs)("div",{className:"mt-2 text-xs text-blue-600",children:["并发数: ",e.details.concurrency," | 模型: ",e.details.model]})]}),e.details?.chapterResults&&e.details.chapterResults.length>0&&(0,b.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[(0,b.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"最近完成的章节"}),(0,b.jsx)("div",{className:"max-h-32 overflow-y-auto space-y-1",children:e.details.chapterResults.filter(a=>a&&a.completedAt).sort((a,b)=>new Date(b.completedAt).getTime()-new Date(a.completedAt).getTime()).slice(0,5).map((a,c)=>(0,b.jsxs)("div",{className:`text-xs p-2 rounded ${a.success?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}`,children:[(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsxs)("span",{children:["第",a.chapterNumber,"章: ",a.chapterTitle]}),(0,b.jsx)("span",{children:a.success?"✓":"✗"})]}),(0,b.jsxs)("div",{className:"flex justify-between text-xs opacity-75",children:[(0,b.jsx)("span",{children:a.apiKeyUsed}),(0,b.jsx)("span",{children:a.processingTime?Math.round(a.processingTime/1e3)+"s":""}),(0,b.jsx)("span",{children:a.tokensUsed?a.tokensUsed+" tokens":""})]}),a.error&&(0,b.jsx)("div",{className:"text-red-600 text-xs mt-1",children:a.error})]},c))})]})]}),"completed"===e.status&&e.details?.chapterResults&&(0,b.jsx)(C,{jobId:e.id,failedChapters:e.details.chapterResults.filter(a=>a&&!a.success).map(a=>({chapterNumber:a.chapterNumber,chapterTitle:a.chapterTitle,error:a.error,apiKeyUsed:a.apiKeyUsed,processingTime:a.processingTime,detailedError:a.detailedError})),rules:"",model:e.details.model,onRetryStart:()=>{console.log("开始重试失败章节")},onRetryComplete:(a,b)=>{console.log("重试完成:",a,b),a&&fetchJobStatus()}}),(0,b.jsx)(E,{jobId:e.id,isOpen:m,onClose:()=>n(!1)})]}):(0,b.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,b.jsxs)("div",{className:"text-center py-4 text-red-600",children:[(0,b.jsx)(y.XCircle,{className:"mx-auto mb-2",size:32}),(0,b.jsx)("p",{children:"无法获取任务状态"})]})})}let G=(0,f.default)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),H=(0,f.default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]]);function I({onJobSelect:a}){let[d,e]=(0,c.useState)([]),[f,h]=(0,c.useState)(!0),[i,j]=(0,c.useState)({});(0,c.useEffect)(()=>{k(),l()},[]);let k=async()=>{try{let a=await fetch("/api/jobs"),b=await a.json();if(b.success){let a=b.data.sort((a,b)=>new Date(b.createdAt).getTime()-new Date(a.createdAt).getTime());e(a)}}catch(a){console.error("加载任务历史失败:",a)}finally{h(!1)}},l=async()=>{try{let a=await fetch("/api/novels"),b=await a.json();if(b.success){let a={},c=b.data.novels||[];Array.isArray(c)&&c.forEach(b=>{a[b.id]=b.title}),j(a)}}catch(a){console.error("加载小说列表失败:",a)}},m=async a=>{if(confirm("确定要删除这个任务记录吗？"))try{(await fetch(`/api/jobs?jobId=${a}`,{method:"DELETE"})).ok&&e(d.filter(b=>b.id!==a))}catch(a){console.error("删除任务失败:",a)}};return f?(0,b.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,b.jsxs)("div",{className:"text-center py-8",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"}),(0,b.jsx)("p",{className:"text-gray-600",children:"加载任务历史中..."})]})}):(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,b.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"任务历史"}),(0,b.jsxs)("button",{onClick:k,className:"flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800",children:[(0,b.jsx)(g,{size:14,className:"mr-1"}),"刷新"]})]})}),(0,b.jsx)("div",{className:"max-h-96 overflow-y-auto",children:0===d.length?(0,b.jsx)("div",{className:"p-6 text-center text-gray-500",children:"暂无任务记录"}):(0,b.jsx)("div",{className:"divide-y divide-gray-200",children:d.map(c=>(0,b.jsx)("div",{className:"p-4 hover:bg-gray-50",children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsxs)("div",{className:"flex items-center mb-2",children:[(a=>{switch(a){case"pending":return(0,b.jsx)(z,{className:"text-yellow-500",size:16});case"processing":return(0,b.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"});case"completed":return(0,b.jsx)(x.CheckCircle,{className:"text-green-500",size:16});case"failed":return(0,b.jsx)(y.XCircle,{className:"text-red-500",size:16});default:return(0,b.jsx)(A,{className:"text-gray-500",size:16})}})(c.status),(0,b.jsx)("span",{className:"ml-2 font-medium text-gray-800",children:i[c.novelId]||"未知小说"}),(0,b.jsx)("span",{className:`ml-2 px-2 py-1 text-xs rounded border ${(a=>{switch(a){case"pending":return"bg-yellow-50 border-yellow-200";case"processing":return"bg-blue-50 border-blue-200";case"completed":return"bg-green-50 border-green-200";case"failed":return"bg-red-50 border-red-200";default:return"bg-gray-50 border-gray-200"}})(c.status)}`,children:(a=>{switch(a){case"pending":return"等待中";case"processing":return"处理中";case"completed":return"已完成";case"failed":return"失败";default:return"未知"}})(c.status)})]}),(0,b.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,b.jsxs)("div",{children:["创建时间: ",new Date(c.createdAt).toLocaleString()]}),c.details&&(0,b.jsxs)("div",{className:"flex space-x-4",children:[(0,b.jsxs)("span",{children:["章节: ",c.details.completedChapters,"/",c.details.totalChapters]}),c.details.totalTokensUsed>0&&(0,b.jsxs)("span",{children:["Token: ",c.details.totalTokensUsed.toLocaleString()]}),c.details.totalProcessingTime>0&&(0,b.jsxs)("span",{children:["耗时: ",(a=>{let b=Math.round(a/1e3);if(b<60)return`${b}秒`;let c=Math.round(b/60);if(c<60)return`${c}分钟`;let d=Math.round(c/60);return`${d}小时`})(c.details.totalProcessingTime)]}),c.details.model&&(0,b.jsxs)("span",{children:["模型: ",c.details.model]})]}),"completed"!==c.status&&"failed"!==c.status&&(0,b.jsxs)("div",{children:["进度: ",c.progress,"%"]})]})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[a&&(0,b.jsx)("button",{onClick:()=>a(c.id),className:"p-1 text-blue-600 hover:text-blue-800",title:"查看详情",children:(0,b.jsx)(G,{size:16})}),(0,b.jsx)("button",{onClick:()=>m(c.id),className:"p-1 text-red-600 hover:text-red-800",title:"删除记录",children:(0,b.jsx)(H,{size:16})})]})]})},c.id))})})]})}let J=(0,f.default)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);function K({refreshInterval:a=5e3}){let[d,e]=(0,c.useState)([]),[f,h]=(0,c.useState)(!0),[i,j]=(0,c.useState)(null);(0,c.useEffect)(()=>{if(k(),a>0){let b=setInterval(k,a);return()=>clearInterval(b)}},[a]);let k=async()=>{try{let a=await fetch("/api/gemini/stats"),b=await a.json();b.success&&(e(b.data),j(new Date))}catch(a){console.error("加载API统计失败:",a)}finally{h(!1)}},l=async()=>{h(!0);try{let a=await fetch("/api/gemini/test"),b=await a.json();b.success?alert(`连接测试成功！
使用的API Key: ${b.details?.apiKeyUsed}
Token消耗: ${b.details?.tokensUsed}
处理时间: ${b.details?.processingTime}ms`):alert(`连接测试失败: ${b.error}`),await k()}catch(a){alert(`连接测试失败: ${a instanceof Error?a.message:"未知错误"}`)}finally{h(!1)}},m=async()=>{if(confirm("确定要重置所有API Key统计吗？"))try{(await fetch("/api/gemini/reset",{method:"POST"})).ok&&(await k(),alert("统计已重置"))}catch(a){alert(`重置失败: ${a instanceof Error?a.message:"未知错误"}`)}};return f&&0===d.length?(0,b.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,b.jsxs)("div",{className:"text-center py-4",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"}),(0,b.jsx)("p",{className:"text-gray-600",children:"加载API统计中..."})]})}):(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,b.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)(J,{className:"mr-2 text-blue-600",size:20}),(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"API Key 状态"})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[i&&(0,b.jsxs)("span",{className:"text-xs text-gray-500",children:["更新于 ",i.toLocaleTimeString()]}),(0,b.jsxs)("button",{onClick:k,disabled:f,className:"flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50",children:[(0,b.jsx)(g,{size:14,className:`mr-1 ${f?"animate-spin":""}`}),"刷新"]})]})]})}),(0,b.jsxs)("div",{className:"p-6",children:[0===d.length?(0,b.jsx)("div",{className:"text-center text-gray-500 py-4",children:"暂无API Key统计数据"}):(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,b.jsx)("div",{className:"bg-blue-50 p-3 rounded-lg",children:(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)(B,{className:"text-blue-600 mr-2",size:16}),(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"text-sm text-blue-600",children:"总请求数"}),(0,b.jsx)("div",{className:"text-lg font-semibold text-blue-800",children:d.reduce((a,b)=>a+b.requestCount,0)})]})]})}),(0,b.jsx)("div",{className:"bg-green-50 p-3 rounded-lg",children:(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)(x.CheckCircle,{className:"text-green-600 mr-2",size:16}),(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"text-sm text-green-600",children:"可用Key"}),(0,b.jsx)("div",{className:"text-lg font-semibold text-green-800",children:d.filter(a=>a.isAvailable).length})]})]})}),(0,b.jsx)("div",{className:"bg-red-50 p-3 rounded-lg",children:(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)(y.XCircle,{className:"text-red-600 mr-2",size:16}),(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"text-sm text-red-600",children:"冷却中"}),(0,b.jsx)("div",{className:"text-lg font-semibold text-red-800",children:d.filter(a=>!a.isAvailable).length})]})]})}),(0,b.jsx)("div",{className:"bg-purple-50 p-3 rounded-lg",children:(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)(J,{className:"text-purple-600 mr-2",size:16}),(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"text-sm text-purple-600",children:"总权重"}),(0,b.jsxs)("div",{className:"text-lg font-semibold text-purple-800",children:[d.reduce((a,b)=>a+b.weight,0),"x"]})]})]})})]}),(0,b.jsx)("div",{className:"space-y-3",children:d.map((a,c)=>(0,b.jsx)("div",{className:`p-4 rounded-lg border ${a.isAvailable?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("div",{className:`w-3 h-3 rounded-full mr-3 ${a.isAvailable?"bg-green-500":"bg-red-500"}`}),(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"font-medium text-gray-800",children:a.name}),(0,b.jsxs)("div",{className:"text-sm text-gray-600",children:["权重: ",a.weight,"x | 使用次数: ",a.requestCount]})]})]}),(0,b.jsxs)("div",{className:"text-right",children:[(0,b.jsx)("div",{className:`text-sm font-medium ${a.isAvailable?"text-green-600":"text-red-600"}`,children:a.isAvailable?"可用":"冷却中"}),!a.isAvailable&&a.cooldownRemaining&&a.cooldownRemaining>0&&(0,b.jsxs)("div",{className:"text-xs text-red-500 flex items-center",children:[(0,b.jsx)(z,{size:12,className:"mr-1"}),(a=>{let b=Math.ceil(a/1e3);if(b<60)return`${b}秒`;let c=Math.ceil(b/60);return`${c}分钟`})(a.cooldownRemaining)]})]})]})},c))})]}),(0,b.jsxs)("div",{className:"flex justify-center space-x-4 mt-6 pt-4 border-t border-gray-200",children:[(0,b.jsx)("button",{onClick:l,disabled:f,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"测试中...":"测试连接"}),(0,b.jsx)("button",{onClick:m,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700",children:"重置统计"})]})]})]})}let L=(0,f.default)("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);function M({currentJobId:a,onJobComplete:d}){let[e,f]=(0,c.useState)(a||null),[g,h]=(0,c.useState)(a?"current":"history"),i=[{id:"current",label:"当前任务",icon:B},{id:"history",label:"任务历史",icon:L},{id:"stats",label:"API状态",icon:J}];return(0,b.jsxs)("div",{className:"w-full max-w-6xl mx-auto",children:[(0,b.jsx)("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6",children:i.map(a=>{let c=a.icon;return(0,b.jsxs)("button",{onClick:()=>h(a.id),className:`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors flex-1 justify-center ${g===a.id?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-800"}`,children:[(0,b.jsx)(c,{className:"mr-2",size:16}),a.label]},a.id)})}),(0,b.jsxs)("div",{className:"mt-6",children:["current"===g&&(0,b.jsx)("div",{children:e?(0,b.jsx)(F,{jobId:e,onComplete:()=>{d&&d(),setTimeout(()=>{h("history")},2e3)}}):(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[(0,b.jsx)(G,{className:"mx-auto mb-4 text-gray-400",size:48}),(0,b.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-2",children:"没有正在进行的任务"}),(0,b.jsx)("p",{className:"text-gray-600 mb-4",children:"当前没有正在执行的改写任务。你可以："}),(0,b.jsxs)("div",{className:"space-y-2 text-sm text-gray-500",children:[(0,b.jsx)("p",{children:"• 从任务历史中选择一个任务查看详情"}),(0,b.jsx)("p",{children:"• 创建新的改写任务"}),(0,b.jsx)("p",{children:"• 查看API Key使用状态"})]})]})}),"history"===g&&(0,b.jsx)(I,{onJobSelect:a=>{f(a),h("current")}}),"stats"===g&&(0,b.jsx)(K,{refreshInterval:5e3})]})]})}let N=(0,f.default)("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]]),O=(0,f.default)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),P=[{id:"gemini-2.5-flash-lite",name:"Gemini 2.5 Flash Lite",description:"快速、轻量级模型，适合大批量处理",speed:"fast",quality:"good"},{id:"gemini-2.5-flash",name:"Gemini 2.5 Flash",description:"平衡速度和质量的标准模型",speed:"medium",quality:"excellent"},{id:"gemini-2.5-pro",name:"Gemini 2.5 Pro",description:"高质量模型，处理复杂内容",speed:"slow",quality:"premium"}],Q=[{value:1,label:"1 (最安全)",description:"单线程处理，最稳定"},{value:2,label:"2 (保守)",description:"低并发，适合API限制严格的情况"},{value:3,label:"3 (推荐)",description:"默认设置，平衡速度和稳定性"},{value:4,label:"4 (积极)",description:"较高并发，需要充足的API配额"},{value:5,label:"5 (激进)",description:"高并发，适合API配额充足的情况"},{value:6,label:"6 (极限)",description:"最高并发，可能触发API限制"}];function R({selectedModel:a,selectedConcurrency:d,onModelChange:e,onConcurrencyChange:f,disabled:g=!1}){let[h,i]=(0,c.useState)(!1),j=P.find(b=>b.id===a);return Q.find(a=>a.value===d),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,b.jsxs)("h2",{className:"text-lg font-semibold text-gray-800 flex items-center",children:[(0,b.jsx)(m,{className:"mr-2",size:18}),"模型配置"]}),(0,b.jsx)("button",{onClick:()=>i(!h),disabled:g,className:"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50",title:h?"收起配置":"展开配置",children:(0,b.jsx)(O,{className:`transition-transform ${h?"rotate-180":""}`,size:16})})]}),!h&&(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,b.jsx)("span",{className:"text-gray-600",children:"当前模型:"}),(0,b.jsx)("span",{className:"font-medium",children:j?.name||a})]}),(0,b.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,b.jsx)("span",{className:"text-gray-600",children:"并发数:"}),(0,b.jsx)("span",{className:"font-medium",children:d})]})]}),h&&(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,b.jsx)(N,{className:"inline mr-1",size:14}),"AI 模型"]}),(0,b.jsx)("div",{className:"space-y-2",children:P.map(c=>(0,b.jsxs)("label",{className:"flex items-start space-x-3 cursor-pointer",children:[(0,b.jsx)("input",{type:"radio",name:"model",value:c.id,checked:a===c.id,onChange:a=>e(a.target.value),disabled:g,className:"mt-1"}),(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("span",{className:"font-medium text-gray-800",children:c.name}),(0,b.jsx)("span",{className:`px-2 py-1 rounded-full text-xs ${(a=>{switch(a){case"fast":return"text-green-600 bg-green-100";case"medium":return"text-yellow-600 bg-yellow-100";case"slow":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(c.speed)}`,children:"fast"===c.speed?"快速":"medium"===c.speed?"中等":"慢速"}),(0,b.jsx)("span",{className:`px-2 py-1 rounded-full text-xs ${(a=>{switch(a){case"good":return"text-blue-600 bg-blue-100";case"excellent":return"text-purple-600 bg-purple-100";case"premium":return"text-indigo-600 bg-indigo-100";default:return"text-gray-600 bg-gray-100"}})(c.quality)}`,children:"good"===c.quality?"良好":"excellent"===c.quality?"优秀":"顶级"})]}),(0,b.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:c.description})]})]},c.id))})]}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,b.jsx)(O,{className:"inline mr-1",size:14}),"并发数量"]}),(0,b.jsx)("div",{className:"space-y-2",children:Q.map(a=>(0,b.jsxs)("label",{className:"flex items-start space-x-3 cursor-pointer",children:[(0,b.jsx)("input",{type:"radio",name:"concurrency",value:a.value,checked:d===a.value,onChange:a=>f(parseInt(a.target.value)),disabled:g,className:"mt-1"}),(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("span",{className:"font-medium text-gray-800",children:a.label}),3===a.value&&(0,b.jsx)("span",{className:"px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-600",children:"推荐"})]}),(0,b.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:a.description})]})]},a.value))})]}),(0,b.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:(0,b.jsxs)("div",{className:"flex items-start",children:[(0,b.jsx)("div",{className:"text-yellow-600 mr-2",children:"⚠️"}),(0,b.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,b.jsx)("p",{className:"font-medium mb-1",children:"配置建议:"}),(0,b.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,b.jsx)("li",{children:'• 首次使用建议选择 "Gemini 2.5 Flash Lite" + 并发数 3'}),(0,b.jsx)("li",{children:"• 如果遇到 429 错误，请降低并发数"}),(0,b.jsx)("li",{children:"• API 配额充足时可以提高并发数以加快处理速度"}),(0,b.jsx)("li",{children:'• 高质量要求的内容建议使用 "Gemini 1.5 Pro"'})]})]})]})})]})]})}function S({message:a,type:d,onClose:e,duration:f=3e3}){return(0,c.useEffect)(()=>{if(f>0){let a=setTimeout(e,f);return()=>clearTimeout(a)}},[f,e]),(0,b.jsx)("div",{className:`fixed top-4 right-4 z-50 max-w-sm w-full ${(()=>{switch(d){case"success":return"bg-green-50 border-green-200";case"error":return"bg-red-50 border-red-200";case"info":return"bg-blue-50 border-blue-200"}})()} border rounded-lg shadow-lg p-4 animate-in slide-in-from-right-full duration-300`,children:(0,b.jsxs)("div",{className:"flex items-start space-x-3",children:[(()=>{switch(d){case"success":return(0,b.jsx)(x.CheckCircle,{className:"text-green-500",size:20});case"error":return(0,b.jsx)(y.XCircle,{className:"text-red-500",size:20});case"info":return(0,b.jsx)(A,{className:"text-blue-500",size:20})}})(),(0,b.jsx)("div",{className:"flex-1 text-sm text-gray-800",children:a}),(0,b.jsx)("button",{onClick:e,className:"text-gray-400 hover:text-gray-600",children:(0,b.jsx)(v,{size:16})})]})})}let T=(0,f.default)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);function U(){let[a,e]=(0,c.useState)(null),[f,g]=(0,c.useState)(""),[h,j]=(0,c.useState)(""),[k,m]=(0,c.useState)([]),[n,o]=(0,c.useState)(!1),[p,q]=(0,c.useState)(null),[r,t]=(0,c.useState)(!1),[u,v]=(0,c.useState)("gemini-2.5-flash"),[x,y]=(0,c.useState)(4),[z,A]=(0,c.useState)({show:!1,message:"",type:"info"}),B=(a,b)=>{A({show:!0,message:a,type:b})},C=async a=>{let b=prompt("请输入预设名称:");if(!b)return;let c=prompt("请输入预设描述 (可选):")||"";try{let d=await fetch("/api/presets",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:b,description:c,rules:a})}),e=await d.json();e.success?B("规则已保存到预设","success"):B(`保存失败: ${e.error}`,"error")}catch(a){console.error("保存预设失败:",a),B("保存预设失败","error")}},D=async()=>{if(!a||!f||!h)return void B("请完整填写所有信息","error");o(!0);try{let b=h;if(k.length>0){let a=k.map(a=>`${a.name}(${a.role}${a.description?": "+a.description:""})`).join("、");b=`人物设定：${a}

${h}`}let c=await fetch("/api/rewrite",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({novelId:a.id,chapterRange:f,rules:b,model:u,concurrency:x})});if(!c.ok)throw Error(`HTTP error! status: ${c.status}`);let d=await c.json();d&&d.success?(q(d.data.jobId),B("改写任务已开始","info")):(B(`改写失败: ${d?.error||"未知错误"}`,"error"),o(!1))}catch(a){console.error("改写请求失败:",a),B("改写请求失败，请检查网络连接","error"),o(!1)}},E=()=>{o(!1),q(null),B("改写完成！","success")};return(0,b.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,b.jsxs)("div",{className:"container mx-auto px-4 py-4",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,b.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:"小说改写工具"}),(0,b.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,b.jsxs)("button",{onClick:()=>t(!r),className:"flex items-center px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,b.jsxs)("svg",{className:"mr-1",width:18,height:18,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,b.jsx)("rect",{x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"}),(0,b.jsx)("path",{d:"M9 9h6v6H9z"})]}),"任务管理"]}),(0,b.jsx)("button",{onClick:D,disabled:n||!a||!f||!h,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-6 rounded-lg transition-colors",title:a?f?h?"开始改写任务":"请输入改写规则":"请选择章节范围":"请先选择小说",children:n?"改写中...":"开始改写"}),(0,b.jsxs)(d.default,{href:"/merge",className:"flex items-center px-3 py-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors",children:[(0,b.jsxs)("svg",{className:"mr-1",width:18,height:18,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,b.jsx)("path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}),(0,b.jsx)("polyline",{points:"7,10 12,15 17,10"}),(0,b.jsx)("line",{x1:"12",y1:"15",x2:"12",y2:"3"})]}),"合并章节"]}),(0,b.jsxs)(d.default,{href:"/help",className:"flex items-center px-3 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors",children:[(0,b.jsx)(T,{className:"mr-1",size:18}),"帮助"]})]})]}),r&&(0,b.jsx)("div",{className:"mb-6",children:(0,b.jsx)(M,{currentJobId:p||void 0,onJobComplete:E})}),n&&p&&!r&&(0,b.jsx)("div",{className:"mb-4",children:(0,b.jsx)(F,{jobId:p,onComplete:E})}),!r&&(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-4",children:[(0,b.jsx)("div",{className:"lg:col-span-1",children:(0,b.jsx)(s,{rules:h,onRulesChange:j,onSaveToPreset:C,disabled:n})}),(0,b.jsxs)("div",{className:"lg:col-span-1 space-y-4",children:[(0,b.jsx)(i,{selectedNovel:a,onNovelSelect:e,disabled:n}),(0,b.jsx)(w,{novelId:a?.id,characters:k,onCharactersChange:m,disabled:n})]}),(0,b.jsx)("div",{className:"lg:col-span-1",children:(0,b.jsx)(l,{novel:a,selectedChapters:f,onChaptersChange:g,disabled:n})}),(0,b.jsx)("div",{className:"lg:col-span-1",children:(0,b.jsx)(R,{selectedModel:u,selectedConcurrency:x,onModelChange:v,onConcurrencyChange:y,disabled:n})})]})]}),z.show&&(0,b.jsx)(S,{message:z.message,type:z.type,onClose:()=>{A({show:!1,message:"",type:"info"})}})]})}}];

//# sourceMappingURL=src_abfa9ff0._.js.map