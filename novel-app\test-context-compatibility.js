// 测试上下文系统的兼容性
// 这个脚本测试服务器端的上下文系统是否仍然正常工作

const { contextAnalyzer } = require('./src/lib/context-analyzer.ts');

async function testServerSideContext() {
  console.log('=== 服务器端上下文系统兼容性测试 ===\n');

  // 1. 测试 contextAnalyzer 实例
  console.log('1. 测试 contextAnalyzer 实例');
  console.log('类型:', typeof contextAnalyzer);
  console.log('是否为 null:', contextAnalyzer === null);
  console.log('构造函数:', contextAnalyzer?.constructor?.name || 'N/A');
  
  if (contextAnalyzer) {
    console.log('可用方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(contextAnalyzer))
      .filter(name => name !== 'constructor')
      .join(', '));
  }
  console.log('✓ contextAnalyzer 实例测试完成\n');

  // 2. 测试环境检测
  console.log('2. 测试环境检测');
  console.log('window 是否存在:', typeof window !== 'undefined');
  console.log('当前环境:', typeof window === 'undefined' ? '服务器端' : '客户端');
  console.log('✓ 环境检测完成\n');

  // 3. 测试导入
  console.log('3. 测试模块导入');
  try {
    const contextUtils = require('./src/lib/context-utils.ts');
    console.log('context-utils 导入成功');
    
    const contextClient = require('./src/lib/context-client.ts');
    console.log('context-client 导入成功');
    
    console.log('✓ 模块导入测试完成\n');
  } catch (error) {
    console.error('✗ 模块导入失败:', error.message);
  }

  // 4. 测试类型检查
  console.log('4. 测试类型检查');
  if (contextAnalyzer) {
    const hasAnalyzeNovel = typeof contextAnalyzer.analyzeNovel === 'function';
    const hasAnalyzeChapter = typeof contextAnalyzer.analyzeChapter === 'function';
    const hasAnalyzeAllChapters = typeof contextAnalyzer.analyzeAllChapters === 'function';
    
    console.log('analyzeNovel 方法:', hasAnalyzeNovel ? '✓' : '✗');
    console.log('analyzeChapter 方法:', hasAnalyzeChapter ? '✓' : '✗');
    console.log('analyzeAllChapters 方法:', hasAnalyzeAllChapters ? '✓' : '✗');
    
    if (hasAnalyzeNovel && hasAnalyzeChapter && hasAnalyzeAllChapters) {
      console.log('✓ 所有必需方法都存在');
    } else {
      console.log('✗ 缺少必需方法');
    }
  } else {
    console.log('✗ contextAnalyzer 为 null，无法测试方法');
  }
  console.log('✓ 类型检查完成\n');

  console.log('=== 兼容性测试完成 ===');
  console.log('结论: 服务器端上下文系统', contextAnalyzer ? '正常工作' : '存在问题');
}

// 运行测试
testServerSideContext().catch(console.error);
