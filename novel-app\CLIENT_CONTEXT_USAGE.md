# 客户端上下文系统使用指南

## 概述

现在上下文系统已经支持在浏览器端使用！通过 API 调用，客户端可以享受与服务器端相同的上下文分析功能。

## 主要改进

### 1. 统一的 contextAnalyzer 实例

```javascript
import { contextAnalyzer } from '@/lib/context-analyzer';

// 现在在浏览器和服务器端都可以使用
console.log(contextAnalyzer); // 不再是 null！

// 在浏览器端，这是一个 ClientContextAnalyzer 实例
// 在服务器端，这是一个 ContextAnalyzer 实例
```

### 2. 客户端上下文分析器 (ClientContextAnalyzer)

```javascript
// 分析小说
const novelContext = await contextAnalyzer.analyzeNovel(novelId);

// 分析章节
const chapterContext = await contextAnalyzer.analyzeChapter(novelId, chapterNumber);

// 批量分析所有章节
const allChapterContexts = await contextAnalyzer.analyzeAllChapters(novelId);

// 获取上下文
const novelContext = await contextAnalyzer.getNovelContext(novelId);
const chapterContext = await contextAnalyzer.getChapterContext(novelId, chapterNumber);
const contextWindow = await contextAnalyzer.getChapterContextWindow(novelId, chapterNumber, 2);
```

### 3. 通用的上下文工具函数

```javascript
import { 
  getNovelContext, 
  getChapterContext, 
  getChapterContextWindow,
  analyzeNovelClient,
  rewriteTextWithContextClient
} from '@/lib/context-client';

// 这些函数会自动检测环境并选择合适的实现
const novelContext = await getNovelContext(novelId);
const chapterContext = await getChapterContext(novelId, chapterNumber);
```

## 使用示例

### 在 React 组件中使用

```jsx
import React, { useState, useEffect } from 'react';
import { contextAnalyzer } from '@/lib/context-analyzer';
import { getNovelContext, analyzeNovelClient } from '@/lib/context-client';

function NovelContextViewer({ novelId }) {
  const [context, setContext] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadContext();
  }, [novelId]);

  const loadContext = async () => {
    if (!novelId) return;
    
    setLoading(true);
    try {
      // 尝试获取现有上下文
      let novelContext = await getNovelContext(novelId);
      
      if (!novelContext) {
        // 如果不存在，则分析小说
        console.log('分析小说上下文...');
        const result = await analyzeNovelClient(novelId, false);
        novelContext = result.novelContext;
      }
      
      setContext(novelContext);
    } catch (error) {
      console.error('加载上下文失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const analyzeChapters = async () => {
    setLoading(true);
    try {
      await contextAnalyzer.analyzeAllChapters(novelId);
      console.log('章节分析完成');
    } catch (error) {
      console.error('章节分析失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>加载中...</div>;

  return (
    <div>
      <h3>小说上下文</h3>
      {context ? (
        <div>
          <p><strong>摘要:</strong> {context.summary}</p>
          <p><strong>写作风格:</strong> {context.writingStyle}</p>
          <p><strong>世界观:</strong> {context.worldSetting}</p>
          <button onClick={analyzeChapters}>分析所有章节</button>
        </div>
      ) : (
        <div>
          <p>未找到上下文</p>
          <button onClick={loadContext}>分析小说</button>
        </div>
      )}
    </div>
  );
}
```

### 在普通 JavaScript 中使用

```javascript
// 在浏览器中直接使用
async function initializeContext(novelId) {
  try {
    // 检查是否已有上下文
    const existingContext = await contextAnalyzer.getNovelContext(novelId);
    
    if (!existingContext) {
      console.log('开始分析小说...');
      const novelContext = await contextAnalyzer.analyzeNovel(novelId);
      console.log('小说分析完成:', novelContext);
      
      console.log('开始分析章节...');
      const chapterContexts = await contextAnalyzer.analyzeAllChapters(novelId);
      console.log('章节分析完成，共分析', chapterContexts.length, '个章节');
    } else {
      console.log('使用现有上下文:', existingContext);
    }
  } catch (error) {
    console.error('初始化上下文失败:', error);
  }
}

// 获取特定章节的上下文窗口
async function getChapterInfo(novelId, chapterNumber) {
  try {
    const contextWindow = await contextAnalyzer.getChapterContextWindow(
      novelId, 
      chapterNumber, 
      2 // 前后2章的上下文
    );
    
    console.log(`第${chapterNumber}章的上下文窗口:`, contextWindow);
    return contextWindow;
  } catch (error) {
    console.error('获取章节信息失败:', error);
    return [];
  }
}
```

## 测试

1. 启动开发服务器：
   ```bash
   cd novel-app
   npm run dev
   ```

2. 打开测试页面：
   ```
   http://localhost:3000/test-client-context.html
   ```

3. 输入一个有效的小说ID，测试各种功能。

## API 兼容性

客户端上下文分析器提供与服务器端相同的接口：

- `analyzeNovel(novelId)` - 分析小说整体上下文
- `analyzeChapter(novelId, chapterNumber)` - 分析单个章节
- `analyzeAllChapters(novelId)` - 批量分析所有章节
- `getNovelContext(novelId)` - 获取小说上下文
- `getChapterContext(novelId, chapterNumber)` - 获取章节上下文
- `getChapterContextWindow(novelId, chapterNumber, windowSize)` - 获取章节上下文窗口

## 注意事项

1. **网络依赖**: 客户端版本通过 HTTP API 调用实现，需要网络连接。
2. **错误处理**: 客户端版本包含完整的错误处理，会抛出有意义的错误信息。
3. **性能**: 客户端调用会有网络延迟，但接口保持一致。
4. **缓存**: 可以考虑在客户端添加缓存机制来提高性能。

## 故障排除

如果遇到问题：

1. 检查开发服务器是否正在运行
2. 检查浏览器控制台的错误信息
3. 确认小说ID是否有效
4. 检查网络连接
5. 查看服务器端日志
