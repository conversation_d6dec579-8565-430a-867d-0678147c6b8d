import { NextRequest, NextResponse } from 'next/server';
import { novelDb } from '@/lib/database';
import { ContextAnalyzer } from '@/lib/context-analyzer';

// POST - 分析小说并生成上下文
export async function POST(request: NextRequest) {
  try {
    const { novelId, analyzeChapters = false } = await request.json();

    if (!novelId) {
      return NextResponse.json(
        { success: false, error: '小说ID不能为空' },
        { status: 400 }
      );
    }

    // 检查小说是否存在
    const novel = novelDb.getById(novelId);
    if (!novel) {
      return NextResponse.json(
        { success: false, error: '小说不存在' },
        { status: 404 }
      );
    }

    try {
      // 创建分析器实例
      const contextAnalyzer = new ContextAnalyzer();

      // 分析小说整体上下文
      console.log(`开始分析小说: ${novel.title}`);
      const novelContext = await contextAnalyzer.analyzeNovel(novelId);

      let chapterContexts: any[] = [];

      // 如果需要分析章节上下文
      if (analyzeChapters) {
        console.log('开始分析章节上下文...');
        chapterContexts = await contextAnalyzer.analyzeAllChapters(novelId);
      }

      return NextResponse.json({
        success: true,
        data: {
          novelContext,
          chapterContexts,
          message: `成功分析小说《${novel.title}》${analyzeChapters ? `，共分析 ${chapterContexts.length} 个章节` : ''}`
        }
      });

    } catch (analysisError) {
      console.error('上下文分析失败:', analysisError);
      return NextResponse.json(
        {
          success: false,
          error: `上下文分析失败: ${analysisError instanceof Error ? analysisError.message : '未知错误'}`
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('分析请求处理失败:', error);
    return NextResponse.json(
      { success: false, error: '分析请求处理失败' },
      { status: 500 }
    );
  }
}
