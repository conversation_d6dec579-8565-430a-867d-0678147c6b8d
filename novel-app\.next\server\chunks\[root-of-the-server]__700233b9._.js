module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},22734,(e,t,r)=>{t.exports=e.x("fs",()=>require("fs"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},84168,e=>{"use strict";e.s(["chapterContextDb",()=>b,"chapterDb",()=>x,"characterDb",()=>v,"jobDb",()=>S,"novelContextDb",()=>I,"novelDb",()=>y,"presetDb",()=>D,"ruleDb",()=>w]);var t=e.i(22734),r=e.i(14747),n=e.i(54799);let a=r.default.join(process.cwd(),"data"),i=r.default.join(a,"novels.json"),s=r.default.join(a,"chapters.json"),o=r.default.join(a,"rewrite_rules.json"),l=r.default.join(a,"rewrite_jobs.json"),u=r.default.join(a,"characters.json"),d=r.default.join(a,"presets.json"),c=r.default.join(a,"novel-contexts.json"),p=r.default.join(a,"chapter-contexts.json");function h(){t.default.existsSync(a)||t.default.mkdirSync(a,{recursive:!0})}function f(e){if(h(),!t.default.existsSync(e))return[];try{let r=t.default.readFileSync(e,"utf-8");return JSON.parse(r)}catch(t){return console.error(`Error reading ${e}:`,t),[]}}function g(e,r){h();try{t.default.writeFileSync(e,JSON.stringify(r,null,2),"utf-8")}catch(t){throw console.error(`Error writing ${e}:`,t),t}}function m(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}let y={getAll:()=>f(i),getById:e=>f(i).find(t=>t.id===e),create:e=>{var t;let r=f(i),a=(t=e.title,n.default.createHash("md5").update(t).digest("hex").substring(0,18)),s=r.find(e=>e.id===a);if(s)return s.filename=e.filename,s.chapterCount=e.chapterCount,g(i,r),s;let o={...e,id:a,createdAt:new Date().toISOString()};return r.push(o),g(i,r),o},update:(e,t)=>{let r=f(i),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t},g(i,r),r[n])},delete:e=>{let t=f(i),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(i,t),!0)}},x={getAll:()=>f(s),getByNovelId:e=>f(s).filter(t=>t.novelId===e),getById:e=>f(s).find(t=>t.id===e),create:e=>{let t=f(s),r={...e,id:m(),createdAt:new Date().toISOString()};return t.push(r),g(s,t),r},createBatch:e=>{let t=f(s),r=e.map(e=>({...e,id:m(),createdAt:new Date().toISOString()}));return t.push(...r),g(s,t),r},delete:e=>{let t=f(s),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(s,t),!0)},deleteByNovelId:e=>{let t=f(s).filter(t=>t.novelId!==e);return g(s,t),!0}},w={getAll:()=>f(o),getById:e=>f(o).find(t=>t.id===e),create:e=>{let t=f(o),r={...e,id:m(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(o,t),r},update:(e,t)=>{let r=f(o),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(o,r),r[n])},delete:e=>{let t=f(o),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(o,t),!0)}},S={getAll:()=>f(l),getById:e=>f(l).find(t=>t.id===e),create:e=>{let t=f(l),r={...e,id:m(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(l,t),r},update:(e,t)=>{let r=f(l),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(l,r),r[n])},delete:e=>{let t=f(l),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(l,t),!0)}},v={getAll:()=>f(u),getByNovelId:e=>f(u).filter(t=>t.novelId===e),getById:e=>f(u).find(t=>t.id===e),create:e=>{let t=f(u),r={...e,id:m(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(u,t),r},update:(e,t)=>{let r=f(u),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(u,r),r[n])},delete:e=>{let t=f(u),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(u,t),!0)},deleteByNovelId:e=>{let t=f(u).filter(t=>t.novelId!==e);return g(u,t),!0}},D={getAll:()=>f(d),getById:e=>f(d).find(t=>t.id===e),create:e=>{let t=f(d),r={...e,id:m(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(d,t),r},update:(e,t)=>{let r=f(d),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(d,r),r[n])},delete:e=>{let t=f(d),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(d,t),!0)}},I={getAll:()=>f(c),getByNovelId:e=>f(c).find(t=>t.novelId===e),getById:e=>f(c).find(t=>t.id===e),create:e=>{let t=f(c),r={...e,id:m(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(c,t),r},update:(e,t)=>{let r=f(c),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(c,r),r[n]},delete:e=>{let t=f(c),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(c,t),!0)}},b={getAll:()=>f(p),getByNovelId:e=>f(p).filter(t=>t.novelId===e),getByChapter:(e,t)=>f(p).find(r=>r.novelId===e&&r.chapterNumber===t),getById:e=>f(p).find(t=>t.id===e),create:e=>{let t=f(p),r={...e,id:m(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(p,t),r},update:(e,t)=>{let r=f(p),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(p,r),r[n]},delete:e=>{let t=f(p),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(p,t),!0)},getContextWindow:(e,t,r=2)=>{let n=f(p).filter(t=>t.novelId===e),a=Math.max(1,t-r),i=t+r;return n.filter(e=>e.chapterNumber>=a&&e.chapterNumber<=i).sort((e,t)=>e.chapterNumber-t.chapterNumber)}}},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},23122,e=>{"use strict";e.s(["fileManager",()=>a]);var t=e.i(22734),r=e.i(14747);class n{static instance;baseDir;constructor(){this.baseDir=process.cwd()}static getInstance(){return n.instance||(n.instance=new n),n.instance}ensureDir(e){t.default.existsSync(e)||t.default.mkdirSync(e,{recursive:!0})}getNovelsDir(){return r.default.join(this.baseDir,"..","novels")}getChaptersDir(){return r.default.join(this.baseDir,"..","chapters")}getDataDir(){let e=r.default.join(this.baseDir,"data");return this.ensureDir(e),e}getRewrittenDir(){let e=r.default.join(this.getDataDir(),"rewritten");return this.ensureDir(e),e}getNovelRewrittenDir(e){let t=r.default.join(this.getRewrittenDir(),this.sanitizeFilename(e));return this.ensureDir(t),t}getDoneNovelsDir(){let e=r.default.join(this.baseDir,"..","done-novels");return this.ensureDir(e),e}getNovelChaptersDir(e){let t=this.getChaptersDir();this.ensureDir(t);let n=r.default.join(t,this.sanitizeFilename(e));return this.ensureDir(n),n}sanitizeFilename(e){return e.replace(/[<>:"/\\|?*]/g,"_").trim()}readFile(e){try{return t.default.readFileSync(e,"utf-8")}catch(t){throw console.error(`读取文件失败: ${e}`,t),t}}writeFile(e,n){try{let a=r.default.dirname(e);this.ensureDir(a),t.default.writeFileSync(e,n,"utf-8")}catch(t){throw console.error(`写入文件失败: ${e}`,t),t}}fileExists(e){return t.default.existsSync(e)}listFiles(e,n){try{if(!t.default.existsSync(e))return[];let a=t.default.readdirSync(e);if(n)return a.filter(e=>{let t=r.default.extname(e).toLowerCase();return n.includes(t)});return a}catch(t){return console.error(`读取目录失败: ${e}`,t),[]}}getFileStats(e){try{return t.default.statSync(e)}catch(t){return console.error(`获取文件信息失败: ${e}`,t),null}}deleteFile(e){try{if(t.default.existsSync(e))return t.default.unlinkSync(e),!0;return!1}catch(t){return console.error(`删除文件失败: ${e}`,t),!1}}deleteDir(e){try{if(t.default.existsSync(e))return t.default.rmSync(e,{recursive:!0,force:!0}),!0;return!1}catch(t){return console.error(`删除目录失败: ${e}`,t),!1}}copyFile(e,n){try{let a=r.default.dirname(n);return this.ensureDir(a),t.default.copyFileSync(e,n),!0}catch(t){return console.error(`复制文件失败: ${e} -> ${n}`,t),!1}}moveFile(e,n){try{let a=r.default.dirname(n);return this.ensureDir(a),t.default.renameSync(e,n),!0}catch(t){return console.error(`移动文件失败: ${e} -> ${n}`,t),!1}}getDirSize(e){let n=0;try{if(!t.default.existsSync(e))return 0;for(let a of t.default.readdirSync(e)){let i=r.default.join(e,a),s=t.default.statSync(i);s.isDirectory()?n+=this.getDirSize(i):n+=s.size}}catch(t){console.error(`计算目录大小失败: ${e}`,t)}return n}formatFileSize(e){if(0===e)return"0 B";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB"][t]}createBackup(e){try{let t=new Date().toISOString().replace(/[:.]/g,"-"),n=r.default.extname(e),a=r.default.basename(e,n),i=r.default.dirname(e),s=r.default.join(i,`${a}_backup_${t}${n}`);if(this.copyFile(e,s))return s;return null}catch(t){return console.error(`创建备份失败: ${e}`,t),null}}mergeRewrittenChapters(e){try{let t=this.getNovelRewrittenDir(e),n=this.getDoneNovelsDir(),a=this.listFiles(t,[".txt"]).filter(e=>e.startsWith("chapter_")&&e.includes("_rewritten")).sort((e,t)=>{let r=parseInt(e.match(/chapter_(\d+)/)?.[1]||"0"),n=parseInt(t.match(/chapter_(\d+)/)?.[1]||"0");return r-n});if(0===a.length)return{success:!1,error:"没有找到改写的章节文件"};let i="";for(let e of a){let n=r.default.join(t,e),a=this.readFile(n);i+=a+"\n\n"}let s=new Date().toISOString().replace(/[:.]/g,"-").substring(0,19),o=`${this.sanitizeFilename(e)}_merged_${s}.txt`,l=r.default.join(n,o);return this.writeFile(l,i.trim()),{success:!0,filePath:l}}catch(t){return console.error(`合并章节失败: ${e}`,t),{success:!1,error:t instanceof Error?t.message:"未知错误"}}}cleanupBackups(e,t=5){try{let n=this.listFiles(e).filter(e=>e.includes("_backup_")).map(t=>({name:t,path:r.default.join(e,t),stats:this.getFileStats(r.default.join(e,t))})).filter(e=>null!==e.stats).sort((e,t)=>t.stats.mtime.getTime()-e.stats.mtime.getTime());if(n.length>t)for(let e of n.slice(t))this.deleteFile(e.path)}catch(t){console.error(`清理备份文件失败: ${e}`,t)}}}let a=n.getInstance()},96550,e=>{"use strict";async function t(t,r,n,a,i,s){try{let{rewriteText:o}=await e.A(30017),{novelContextDb:l,chapterContextDb:u}=await e.A(58566),d=l.getByNovelId(t),c=u.getByChapter(t,r),p={originalText:n,rules:a,chapterTitle:i,chapterNumber:r,model:s,novelContext:d?{summary:d.summary,mainCharacters:d.mainCharacters,worldSetting:d.worldSetting,writingStyle:d.writingStyle,tone:d.tone}:void 0,chapterContext:c?{previousChapterSummary:c.previousChapterSummary,keyEvents:c.keyEvents,characterStates:c.characterStates,plotProgress:c.plotProgress,contextualNotes:c.contextualNotes}:void 0};return await o(p)}catch(o){console.error("带上下文重写失败:",o);let{rewriteText:t}=await e.A(30017);return await t({originalText:n,rules:a,chapterTitle:i,chapterNumber:r,model:s})}}async function r(t,n,a,i,s,o=3,l="gemini-2.5-flash-lite",u=!0){try{let{novelContextDb:r,chapterContextDb:u}=await e.A(58566),{rewriteText:d}=await e.A(30017),c=r.getByNovelId(t),p=Array(n.length),h=0,f=0,g=Date.now(),m={count:o,waiting:[]},y=async(e,r)=>{await new Promise(e=>{m.count>0?(m.count--,e()):m.waiting.push(e)});let o=Date.now();try{let m=u.getByChapter(t,e.number),y={originalText:e.content,rules:a,chapterTitle:e.title,chapterNumber:e.number,model:l,novelContext:c?{summary:c.summary,mainCharacters:c.mainCharacters,worldSetting:c.worldSetting,writingStyle:c.writingStyle,tone:c.tone}:void 0,chapterContext:m?{previousChapterSummary:m.previousChapterSummary,keyEvents:m.keyEvents,characterStates:m.characterStates,plotProgress:m.plotProgress,contextualNotes:m.contextualNotes}:void 0},x=await d(y),w=Date.now()-o;x.tokensUsed&&(f+=x.tokensUsed);let S={success:x.success,content:x.rewrittenText,error:x.error,details:{apiKeyUsed:x.apiKeyUsed,tokensUsed:x.tokensUsed,model:x.model,processingTime:w,chapterNumber:e.number,chapterTitle:e.title,hasContext:!!(c||m)}};p[r]=S,h++,s&&s(r,S);let v=h/n.length*100,D=Date.now()-g,I=D/h;i&&i(v,e.number,{completed:h,totalTokensUsed:f,totalTime:D,averageTimePerChapter:I,hasContext:!!(c||m)}),console.log(`第 ${e.number} 章重写${x.success?"成功":"失败"}${c||m?"（使用上下文）":""}: ${x.error||"完成"}`)}catch(a){let t=Date.now()-o,n={success:!1,content:"",error:`重写异常: ${a instanceof Error?a.message:"未知错误"}`,details:{processingTime:t,chapterNumber:e.number,chapterTitle:e.title,hasContext:!!(c||chapterContext)}};p[r]=n,h++,s&&s(r,n),console.error(`第 ${e.number} 章重写异常:`,a)}finally{if(m.count++,m.waiting.length>0){let e=m.waiting.shift();e&&(m.count--,e())}}},x=n.map((e,t)=>y(e,t));return await Promise.all(x),p}catch(r){console.error("批量重写失败，回退到普通重写:",r);let{rewriteChapters:t}=await e.A(30017);return await t(n,a,i,s,o,l,u)}}e.s(["rewriteChaptersWithContext",()=>r,"rewriteTextWithContextServer",()=>t])},33502,(e,t,r)=>{},80830,e=>{"use strict";e.s(["handler",()=>E,"patchFetch",()=>A,"routeModule",()=>C,"serverHooks",()=>j,"workAsyncStorage",()=>N,"workUnitAsyncStorage",()=>R],80830);var t=e.i(47909),r=e.i(74017),n=e.i(96250),a=e.i(59756),i=e.i(61916),s=e.i(69741),o=e.i(16795),l=e.i(87718),u=e.i(95169),d=e.i(47587),c=e.i(66012),p=e.i(70101),h=e.i(26937),f=e.i(10372),g=e.i(93695);e.i(52474);var m=e.i(220);e.s(["POST",()=>D],86165);var y=e.i(89171),x=e.i(84168),w=e.i(96550),S=e.i(23122),v=e.i(14747);async function D(e){try{let{jobId:t,chapterNumbers:r,rules:n,model:a}=await e.json();if(!t||!r||!Array.isArray(r)||!n)return y.NextResponse.json({success:!1,error:"参数不完整"},{status:400});let i=x.jobDb.getById(t);if(!i)return y.NextResponse.json({success:!1,error:"任务不存在"},{status:404});let s=x.novelDb.getById(i.novelId);if(!s)return y.NextResponse.json({success:!1,error:"小说不存在"},{status:404});let o=x.chapterDb.getByNovelId(i.novelId).filter(e=>r.includes(e.chapterNumber));if(0===o.length)return y.NextResponse.json({success:!1,error:"没有找到指定的章节"},{status:404});return x.jobDb.update(t,{status:"processing",details:{...i.details,retryInProgress:!0,retryChapters:r}}),I(t,o,n,s.title,a||"gemini-2.5-flash-lite"),y.NextResponse.json({success:!0,data:{jobId:t,retryChaptersCount:o.length,message:"重试任务已创建，正在处理中..."}})}catch(e){return console.error("创建重试任务失败:",e),y.NextResponse.json({success:!1,error:"创建重试任务失败"},{status:500})}}async function I(e,t,r,n,a="gemini-2.5-flash-lite"){let i=Date.now(),s=S.fileManager.getNovelRewrittenDir(n),o=0,l=[];try{for(let e of t){console.log(`正在重试第 ${e.chapterNumber} 章: ${e.title}`);let t=Date.now();try{l.length>0&&await new Promise(e=>setTimeout(e,3e3));let n=await (0,w.rewriteTextWithContextServer)(e.novelId,e.chapterNumber,e.content,r,e.title,a),i=Date.now()-t;if(n.success){let t=`chapter_${e.chapterNumber}_rewritten.txt`,r=v.default.join(s,t);S.fileManager.writeFile(r,n.rewrittenText),o++}l.push({chapterNumber:e.chapterNumber,chapterTitle:e.title,success:n.success,error:n.error,apiKeyUsed:n.apiKeyUsed,tokensUsed:n.tokensUsed,processingTime:i}),console.log(`第 ${e.chapterNumber} 章重试${n.success?"成功":"失败"}: ${n.error||"完成"}`)}catch(n){let r=Date.now()-t;l.push({chapterNumber:e.chapterNumber,chapterTitle:e.title,success:!1,error:`重试异常: ${n instanceof Error?n.message:"未知错误"}`,processingTime:r}),console.error(`第 ${e.chapterNumber} 章重试异常:`,n)}}let u=x.jobDb.getById(e);if(u?.details){let r=[...u.details.chapterResults||[]];l.forEach(e=>{let t=r.findIndex(t=>t&&t.chapterNumber===e.chapterNumber);t>=0&&(r[t]={...r[t],...e,completedAt:new Date().toISOString(),isRetried:!0})});let n=Date.now()-i,a=r.filter(e=>e&&e.success).length,s=r.filter(e=>e&&!e.success).length;x.jobDb.update(e,{status:"completed",progress:100,result:`重试完成: ${o}/${t.length} 章节成功，总计 ${a}/${u.details.totalChapters} 章节完成`,details:{...u.details,completedChapters:a,failedChapters:s,chapterResults:r,retryInProgress:!1,retryResults:l,lastRetryTime:new Date().toISOString(),retryProcessingTime:n}})}let d=v.default.join(s,`retry_summary_${Date.now()}.json`),c=JSON.stringify({jobId:e,novelTitle:n,retryChapters:t.map(e=>({number:e.chapterNumber,title:e.title})),successCount:o,failedCount:t.length-o,totalProcessingTime:Date.now()-i,results:l,completedAt:new Date().toISOString(),model:a},null,2);S.fileManager.writeFile(d,c)}catch(t){console.error("执行重试任务失败:",t),x.jobDb.update(e,{status:"failed",result:`重试失败: ${t instanceof Error?t.message:"未知错误"}`,details:{...x.jobDb.getById(e)?.details,retryInProgress:!1,retryError:t instanceof Error?t.message:"未知错误"}})}}var b=e.i(86165);let C=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/rewrite/retry/route",pathname:"/api/rewrite/retry",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/rewrite/retry/route.ts",nextConfigOutput:"",userland:b}),{workAsyncStorage:N,workUnitAsyncStorage:R,serverHooks:j}=C;function A(){return(0,n.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:R})}async function E(e,t,n){var y;let x="/api/rewrite/retry/route";x=x.replace(/\/index$/,"")||"/";let w=await C.prepare(e,t,{srcPage:x,multiZoneDraftMode:!1});if(!w)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:S,params:v,nextConfig:D,isDraftMode:I,prerenderManifest:b,routerServerContext:N,isOnDemandRevalidate:R,revalidateOnlyGenerated:j,resolvedPathname:A}=w,E=(0,s.normalizeAppPath)(x),O=!!(b.dynamicRoutes[E]||b.routes[A]);if(O&&!I){let e=!!b.routes[A],t=b.dynamicRoutes[E];if(t&&!1===t.fallback&&!e)throw new g.NoFallbackError}let $=null;!O||C.isDev||I||($="/index"===($=A)?"/":$);let T=!0===C.isDev||!O,_=O&&!T,k=e.method||"GET",P=(0,i.getTracer)(),B=P.getActiveScopeSpan(),F={params:v,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!D.experimental.cacheComponents,authInterrupts:!!D.experimental.authInterrupts},supportsDynamicResponse:T,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(y=D.experimental)?void 0:y.cacheLife,isRevalidate:_,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>C.onRequestError(e,t,n,N)},sharedContext:{buildId:S}},U=new o.NodeNextRequest(e),q=new o.NodeNextResponse(t),M=l.NextRequestAdapter.fromNodeNextRequest(U,(0,l.signalFromNodeResponse)(t));try{let s=async r=>C.handle(M,F).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=P.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==u.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${k} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${k} ${e.url}`)}),o=async i=>{var o,l;let u=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&R&&j&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await s(i);e.fetchMetrics=F.renderOpts.fetchMetrics;let l=F.renderOpts.pendingWaitUntil;l&&n.waitUntil&&(n.waitUntil(l),l=void 0);let u=F.renderOpts.collectedTags;if(!O)return await (0,c.sendResponse)(U,q,o,F.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(o.headers);u&&(t[f.NEXT_CACHE_TAGS_HEADER]=u),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==F.renderOpts.collectedRevalidate&&!(F.renderOpts.collectedRevalidate>=f.INFINITE_CACHE)&&F.renderOpts.collectedRevalidate,n=void 0===F.renderOpts.collectedExpire||F.renderOpts.collectedExpire>=f.INFINITE_CACHE?void 0:F.renderOpts.collectedExpire;return{value:{kind:m.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await C.onRequestError(e,t,{routerKind:"App Router",routePath:x,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:R})},N),t}},g=await C.handleResponse({req:e,nextConfig:D,cacheKey:$,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:R,revalidateOnlyGenerated:j,responseGenerator:u,waitUntil:n.waitUntil});if(!O)return null;if((null==g||null==(o=g.value)?void 0:o.kind)!==m.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==g||null==(l=g.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",R?"REVALIDATED":g.isMiss?"MISS":g.isStale?"STALE":"HIT"),I&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let y=(0,p.fromNodeOutgoingHttpHeaders)(g.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&O||y.delete(f.NEXT_CACHE_TAGS_HEADER),!g.cacheControl||t.getHeader("Cache-Control")||y.get("Cache-Control")||y.set("Cache-Control",(0,h.getCacheControlHeader)(g.cacheControl)),await (0,c.sendResponse)(U,q,new Response(g.value.body,{headers:y,status:g.value.status||200})),null};B?await o(B):await P.withPropagatedContext(e.headers,()=>P.trace(u.BaseServerSpan.handleRequest,{spanName:`${k} ${e.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":k,"http.target":e.url}},o))}catch(t){if(B||t instanceof g.NoFallbackError||await C.onRequestError(e,t,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:R})}),O)throw t;return await (0,c.sendResponse)(U,q,new Response(null,{status:500})),null}}},30017,e=>{e.v(t=>Promise.all(["server/chunks/src_lib_gemini_ts_7aaf7081._.js"].map(t=>e.l(t))).then(()=>t(71994)))},58566,e=>{e.v(e=>Promise.resolve().then(()=>e(84168)))}];

//# sourceMappingURL=%5Broot-of-the-server%5D__700233b9._.js.map