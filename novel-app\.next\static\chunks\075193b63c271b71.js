(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,33525,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return s}});let s=e=>{}},98183,(e,r,t)=>{"use strict";function s(e){let r={};for(let[t,s]of e.entries()){let e=r[t];void 0===e?r[t]=s:Array.isArray(e)?e.push(s):r[t]=[e,s]}return r}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function l(e){let r=new URLSearchParams;for(let[t,s]of Object.entries(e))if(Array.isArray(s))for(let e of s)r.append(t,n(e));else r.set(t,n(s));return r}function i(e){for(var r=arguments.length,t=Array(r>1?r-1:0),s=1;s<r;s++)t[s-1]=arguments[s];for(let r of t){for(let t of r.keys())e.delete(t);for(let[t,s]of r.entries())e.append(t,s)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return s},urlQueryToSearchParams:function(){return l}})},95057,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(t,{formatUrl:function(){return l},formatWithValidation:function(){return a},urlObjectKeys:function(){return i}});let s=e.r(90809)._(e.r(98183)),n=/https?|ftp|gopher|file/;function l(e){let{auth:r,hostname:t}=e,l=e.protocol||"",i=e.pathname||"",a=e.hash||"",c=e.query||"",d=!1;r=r?encodeURIComponent(r).replace(/%3A/i,":")+"@":"",e.host?d=r+e.host:t&&(d=r+(~t.indexOf(":")?"["+t+"]":t),e.port&&(d+=":"+e.port)),c&&"object"==typeof c&&(c=String(s.urlQueryToSearchParams(c)));let o=e.search||c&&"?"+c||"";return l&&!l.endsWith(":")&&(l+=":"),e.slashes||(!l||n.test(l))&&!1!==d?(d="//"+(d||""),i&&"/"!==i[0]&&(i="/"+i)):d||(d=""),a&&"#"!==a[0]&&(a="#"+a),o&&"?"!==o[0]&&(o="?"+o),""+l+d+(i=i.replace(/[?#]/g,encodeURIComponent))+(o=o.replace("#","%23"))+a}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(e){return l(e)}},18581,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return n}});let s=e.r(71645);function n(e,r){let t=(0,s.useRef)(null),n=(0,s.useRef)(null);return(0,s.useCallback)(s=>{if(null===s){let e=t.current;e&&(t.current=null,e());let r=n.current;r&&(n.current=null,r())}else e&&(t.current=l(e,s)),r&&(n.current=l(r,s))},[e,r])}function l(e,r){if("function"!=typeof e)return e.current=r,()=>{e.current=null};{let t=e(r);return"function"==typeof t?t:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),r.exports=t.default)},18967,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(t,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return j},NormalizeError:function(){return p},PageNotFoundError:function(){return g},SP:function(){return m},ST:function(){return x},WEB_VITALS:function(){return s},execOnce:function(){return n},getDisplayName:function(){return d},getLocationOrigin:function(){return a},getURL:function(){return c},isAbsoluteUrl:function(){return i},isResSent:function(){return o},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return b}});let s=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let r,t=!1;return function(){for(var s=arguments.length,n=Array(s),l=0;l<s;l++)n[l]=arguments[l];return t||(t=!0,r=e(...n)),r}}let l=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>l.test(e);function a(){let{protocol:e,hostname:r,port:t}=window.location;return e+"//"+r+(t?":"+t:"")}function c(){let{href:e}=window.location,r=a();return e.substring(r.length)}function d(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function o(e){return e.finished||e.headersSent}function u(e){let r=e.split("?");return r[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(r[1]?"?"+r.slice(1).join("?"):"")}async function h(e,r){let t=r.res||r.ctx&&r.ctx.res;if(!e.getInitialProps)return r.ctx&&r.Component?{pageProps:await h(r.Component,r.ctx)}:{};let s=await e.getInitialProps(r);if(t&&o(t))return s;if(!s)throw Object.defineProperty(Error('"'+d(e)+'.getInitialProps()" should resolve to an object. But found "'+s+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return s}let m="undefined"!=typeof performance,x=m&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class p extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class j extends Error{constructor(e,r){super(),this.message="Failed to load static file for page: "+e+" "+r}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},73668,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return l}});let s=e.r(18967),n=e.r(52817);function l(e){if(!(0,s.isAbsoluteUrl)(e))return!0;try{let r=(0,s.getLocationOrigin)(),t=new URL(e,r);return t.origin===r&&(0,n.hasBasePath)(t.pathname)}catch(e){return!1}}},84508,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return s}});let s=e=>{}},22016,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(t,{default:function(){return p},useLinkStatus:function(){return j}});let s=e.r(90809),n=e.r(43476),l=s._(e.r(71645)),i=e.r(95057),a=e.r(8372),c=e.r(18581),d=e.r(18967),o=e.r(5550);e.r(33525);let u=e.r(91949),h=e.r(73668),m=e.r(99781);e.r(84508);let x=e.r(65165);function f(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}function p(e){var r;let t,s,i,[p,j]=(0,l.useOptimistic)(u.IDLE_LINK_STATUS),y=(0,l.useRef)(null),{href:b,as:N,children:v,prefetch:w=null,passHref:k,replace:P,shallow:O,scroll:_,onClick:C,onMouseEnter:E,onTouchStart:M,legacyBehavior:A=!1,onNavigate:S,ref:T,unstable_dynamicOnHover:L,...R}=e;t=v,A&&("string"==typeof t||"number"==typeof t)&&(t=(0,n.jsx)("a",{children:t}));let U=l.default.useContext(a.AppRouterContext),I=!1!==w,z=!1!==w?null===(r=w)||"auto"===r?x.FetchStrategy.PPR:x.FetchStrategy.Full:x.FetchStrategy.PPR,{href:F,as:B}=l.default.useMemo(()=>{let e=f(b);return{href:e,as:N?f(N):e}},[b,N]);A&&(s=l.default.Children.only(t));let D=A?s&&"object"==typeof s&&s.ref:T,K=l.default.useCallback(e=>(null!==U&&(y.current=(0,u.mountLinkInstance)(e,F,U,z,I,j)),()=>{y.current&&((0,u.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,u.unmountPrefetchableInstance)(e)}),[I,F,U,z,j]),W={ref:(0,c.useMergedRef)(K,D),onClick(e){A||"function"!=typeof C||C(e),A&&s.props&&"function"==typeof s.props.onClick&&s.props.onClick(e),U&&(e.defaultPrevented||function(e,r,t,s,n,i,a){let{nodeName:c}=e.currentTarget;if(!("A"===c.toUpperCase()&&function(e){let r=e.currentTarget.getAttribute("target");return r&&"_self"!==r||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,h.isLocalURL)(r)){n&&(e.preventDefault(),location.replace(r));return}if(e.preventDefault(),a){let e=!1;if(a({preventDefault:()=>{e=!0}}),e)return}l.default.startTransition(()=>{(0,m.dispatchNavigateAction)(t||r,n?"replace":"push",null==i||i,s.current)})}}(e,F,B,y,P,_,S))},onMouseEnter(e){A||"function"!=typeof E||E(e),A&&s.props&&"function"==typeof s.props.onMouseEnter&&s.props.onMouseEnter(e),U&&I&&(0,u.onNavigationIntent)(e.currentTarget,!0===L)},onTouchStart:function(e){A||"function"!=typeof M||M(e),A&&s.props&&"function"==typeof s.props.onTouchStart&&s.props.onTouchStart(e),U&&I&&(0,u.onNavigationIntent)(e.currentTarget,!0===L)}};return(0,d.isAbsoluteUrl)(B)?W.href=B:A&&!k&&("a"!==s.type||"href"in s.props)||(W.href=(0,o.addBasePath)(B)),i=A?l.default.cloneElement(s,W):(0,n.jsx)("a",{...R,...W,children:t}),(0,n.jsx)(g.Provider,{value:p,children:i})}let g=(0,l.createContext)(u.IDLE_LINK_STATUS),j=()=>(0,l.useContext)(g);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),r.exports=t.default)},10980,75254,33060,69638,73884,e=>{"use strict";e.s(["BookOpen",()=>a],10980),e.s(["default",()=>i],75254);var r=e.i(71645);let t=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},s=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,r.forwardRef)((e,t)=>{let{color:l="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:c,className:d="",children:o,iconNode:u,...h}=e;return(0,r.createElement)("svg",{ref:t,...n,width:i,height:i,stroke:l,strokeWidth:c?24*Number(a)/Number(i):a,className:s("lucide",d),...!o&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(h)&&{"aria-hidden":"true"},...h},[...u.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(o)?o:[o]])}),i=(e,n)=>{let i=(0,r.forwardRef)((i,a)=>{let{className:c,...d}=i;return(0,r.createElement)(l,{ref:a,iconNode:n,className:s("lucide-".concat(t(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),c),...d})});return i.displayName=t(e),i},a=i("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);e.s(["Wand2",()=>c],33060);let c=i("wand-sparkles",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72",key:"ul74o6"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]]);e.s(["CheckCircle",()=>d],69638);let d=i("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);e.s(["XCircle",()=>o],73884);let o=i("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},55874,e=>{"use strict";e.s(["default",()=>h],55874);var r=e.i(43476),t=e.i(71645),s=e.i(22016),n=e.i(75254);let l=(0,n.default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var i=e.i(10980),a=e.i(33060);let c=(0,n.default)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),d=(0,n.default)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);var o=e.i(69638),u=e.i(73884);function h(){let[e,n]=(0,t.useState)("basics"),h={basics:{title:"基础概念",icon:i.BookOpen,content:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"什么是改写规则？"}),(0,r.jsx)("p",{className:"text-gray-700 mb-4",children:"改写规则是告诉AI如何修改小说内容的指令。通过详细的规则描述，AI可以按照你的要求对小说进行个性化改写。"}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"规则的作用"}),(0,r.jsxs)("ul",{className:"text-blue-700 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• 指导AI理解你的改写需求"}),(0,r.jsx)("li",{children:"• 确保改写结果符合你的期望"}),(0,r.jsx)("li",{children:"• 保持故事的连贯性和逻辑性"}),(0,r.jsx)("li",{children:"• 个性化定制阅读体验"})]})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"如何编写有效的规则？"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(o.CheckCircle,{className:"text-green-600 mr-2",size:16}),(0,r.jsx)("h4",{className:"font-medium text-green-800",children:"好的规则"})]}),(0,r.jsxs)("ul",{className:"text-green-700 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• 具体明确的描述"}),(0,r.jsx)("li",{children:"• 包含具体的改写方向"}),(0,r.jsx)("li",{children:"• 考虑故事的整体性"}),(0,r.jsx)("li",{children:"• 适度的改写幅度"})]})]}),(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(u.XCircle,{className:"text-red-600 mr-2",size:16}),(0,r.jsx)("h4",{className:"font-medium text-red-800",children:"避免的问题"})]}),(0,r.jsxs)("ul",{className:"text-red-700 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• 过于模糊的描述"}),(0,r.jsx)("li",{children:"• 相互矛盾的要求"}),(0,r.jsx)("li",{children:"• 过度的改写要求"}),(0,r.jsx)("li",{children:"• 忽略故事逻辑"})]})]})]})]})]})},examples:{title:"规则示例",icon:a.Wand2,content:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"感情戏增强"}),(0,r.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,r.jsx)("pre",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:"请按照以下规则改写：\n1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节\n2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过\n3. 增加角色间的情感张力和暧昧氛围\n4. 保持故事主线不变，但重点突出感情发展\n5. 添加更多的日常生活场景和温馨互动"})})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"毒点清除"}),(0,r.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,r.jsx)("pre",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:"请按照以下规则改写：\n1. 完全移除或修改送女、绿帽、圣母等毒点情节\n2. 删除或改写让读者不适的桥段\n3. 修正主角的三观和行为逻辑\n4. 用更合理的情节替代被删除的内容\n5. 确保故事逻辑的完整性和连贯性"})})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"人设优化"}),(0,r.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,r.jsx)("pre",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:"请按照以下规则改写：\n1. 修正主角的性格设定，使其更加立体和讨喜\n2. 改善对话风格，使其更加自然流畅\n3. 去除过于中二或不合理的行为描写\n4. 增强角色的智商和情商表现\n5. 保持角色的核心特征，但优化表现方式"})})]})]})},tips:{title:"写作技巧",icon:c,content:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"规则编写技巧"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"1. 使用具体的动词"}),(0,r.jsx)("p",{className:"text-gray-700 text-sm mb-2",children:'用"扩写"、"删除"、"修改"等具体动词，而不是"优化"、"改善"等模糊词汇。'}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm",children:[(0,r.jsxs)("div",{className:"bg-green-50 p-2 rounded",children:[(0,r.jsx)("span",{className:"text-green-600 font-medium",children:"好："})," 扩写男女主对话"]}),(0,r.jsxs)("div",{className:"bg-red-50 p-2 rounded",children:[(0,r.jsx)("span",{className:"text-red-600 font-medium",children:"差："})," 优化感情戏"]})]})]}),(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"2. 设定优先级"}),(0,r.jsx)("p",{className:"text-gray-700 text-sm mb-2",children:"用数字标号明确各项规则的重要程度。"}),(0,r.jsx)("div",{className:"bg-blue-50 p-3 rounded text-sm",children:(0,r.jsxs)("div",{className:"text-blue-800",children:["1. 首要任务：删除毒点情节",(0,r.jsx)("br",{}),"2. 次要任务：扩写感情戏",(0,r.jsx)("br",{}),"3. 可选任务：优化对话风格"]})})]}),(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"3. 提供具体例子"}),(0,r.jsx)("p",{className:"text-gray-700 text-sm mb-2",children:"在规则中包含具体的例子或场景描述。"}),(0,r.jsx)("div",{className:"bg-yellow-50 p-3 rounded text-sm",children:(0,r.jsx)("div",{className:"text-yellow-800",children:'例如：将"他很强"改写为具体的实力展现，如"他一剑斩断了千年古树"'})})]})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"常见问题解决"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("details",{className:"border border-gray-200 rounded-lg",children:[(0,r.jsx)("summary",{className:"p-3 cursor-pointer font-medium text-gray-800 hover:bg-gray-50",children:"改写结果不符合预期怎么办？"}),(0,r.jsx)("div",{className:"p-3 border-t border-gray-200 text-sm text-gray-700",children:(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:"• 检查规则是否足够具体和明确"}),(0,r.jsx)("li",{children:"• 避免相互矛盾的要求"}),(0,r.jsx)("li",{children:"• 尝试分步骤进行改写"}),(0,r.jsx)("li",{children:"• 参考预设规则模板"})]})})]}),(0,r.jsxs)("details",{className:"border border-gray-200 rounded-lg",children:[(0,r.jsx)("summary",{className:"p-3 cursor-pointer font-medium text-gray-800 hover:bg-gray-50",children:"如何保持故事的连贯性？"}),(0,r.jsx)("div",{className:"p-3 border-t border-gray-200 text-sm text-gray-700",children:(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:"• 在规则中强调保持主线剧情"}),(0,r.jsx)("li",{children:"• 避免大幅修改关键情节"}),(0,r.jsx)("li",{children:"• 注意角色性格的一致性"}),(0,r.jsx)("li",{children:"• 考虑前后章节的衔接"})]})})]}),(0,r.jsxs)("details",{className:"border border-gray-200 rounded-lg",children:[(0,r.jsx)("summary",{className:"p-3 cursor-pointer font-medium text-gray-800 hover:bg-gray-50",children:"改写速度很慢怎么办？"}),(0,r.jsx)("div",{className:"p-3 border-t border-gray-200 text-sm text-gray-700",children:(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:"• 减少单次改写的章节数量"}),(0,r.jsx)("li",{children:"• 简化规则描述"}),(0,r.jsx)("li",{children:"• 避免在高峰时段使用"}),(0,r.jsx)("li",{children:"• 检查网络连接状态"})]})})]})]})]})]})},advanced:{title:"高级技巧",icon:d,content:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"组合规则策略"}),(0,r.jsx)("p",{className:"text-gray-700 mb-4",children:"对于复杂的改写需求，可以将多个简单规则组合使用，分阶段完成改写。"}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:"分阶段改写示例"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"bg-white rounded p-3 border-l-4 border-blue-500",children:[(0,r.jsx)("div",{className:"font-medium text-blue-800",children:"第一阶段：清理内容"}),(0,r.jsx)("div",{className:"text-sm text-gray-700",children:"删除毒点情节，修正逻辑错误"})]}),(0,r.jsxs)("div",{className:"bg-white rounded p-3 border-l-4 border-green-500",children:[(0,r.jsx)("div",{className:"font-medium text-green-800",children:"第二阶段：增强内容"}),(0,r.jsx)("div",{className:"text-sm text-gray-700",children:"扩写感情戏，增加互动描写"})]}),(0,r.jsxs)("div",{className:"bg-white rounded p-3 border-l-4 border-purple-500",children:[(0,r.jsx)("div",{className:"font-medium text-purple-800",children:"第三阶段：优化细节"}),(0,r.jsx)("div",{className:"text-sm text-gray-700",children:"改善对话风格，调整节奏"})]})]})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"针对性改写"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"都市小说"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1",children:[(0,r.jsx)("li",{children:"• 现代化语言表达"}),(0,r.jsx)("li",{children:"• 职场/校园场景描写"}),(0,r.jsx)("li",{children:"• 现实感情发展"}),(0,r.jsx)("li",{children:"• 社会背景融入"})]})]}),(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"玄幻小说"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1",children:[(0,r.jsx)("li",{children:"• 修炼体系完善"}),(0,r.jsx)("li",{children:"• 世界观构建"}),(0,r.jsx)("li",{children:"• 战斗场面描写"}),(0,r.jsx)("li",{children:"• 境界提升逻辑"})]})]}),(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"历史小说"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1",children:[(0,r.jsx)("li",{children:"• 历史背景考证"}),(0,r.jsx)("li",{children:"• 古代语言风格"}),(0,r.jsx)("li",{children:"• 政治军事描写"}),(0,r.jsx)("li",{children:"• 文化细节还原"})]})]}),(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"言情小说"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1",children:[(0,r.jsx)("li",{children:"• 情感细腻描写"}),(0,r.jsx)("li",{children:"• 心理活动刻画"}),(0,r.jsx)("li",{children:"• 浪漫场景营造"}),(0,r.jsx)("li",{children:"• 角色魅力塑造"})]})]})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"质量控制"}),(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-yellow-800 mb-2",children:"改写质量检查清单"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-yellow-800 mb-1",children:"内容质量"}),(0,r.jsxs)("ul",{className:"text-yellow-700 space-y-1",children:[(0,r.jsx)("li",{children:"□ 故事逻辑完整"}),(0,r.jsx)("li",{children:"□ 角色行为合理"}),(0,r.jsx)("li",{children:"□ 情节发展自然"}),(0,r.jsx)("li",{children:"□ 语言表达流畅"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-yellow-800 mb-1",children:"规则执行"}),(0,r.jsxs)("ul",{className:"text-yellow-700 space-y-1",children:[(0,r.jsx)("li",{children:"□ 改写目标达成"}),(0,r.jsx)("li",{children:"□ 重点内容突出"}),(0,r.jsx)("li",{children:"□ 不需要内容简化"}),(0,r.jsx)("li",{children:"□ 整体风格统一"})]})]})]})]})]})]})}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)(s.default,{href:"/",className:"inline-flex items-center text-blue-600 hover:text-blue-800 mb-4",children:[(0,r.jsx)(l,{className:"mr-2",size:20}),"返回主页"]}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"改写规则帮助"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"学习如何编写有效的改写规则，获得更好的改写效果"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4 sticky top-4",children:[(0,r.jsx)("h2",{className:"font-semibold text-gray-800 mb-4",children:"目录"}),(0,r.jsx)("nav",{className:"space-y-2",children:Object.entries(h).map(t=>{let[s,l]=t,i=l.icon;return(0,r.jsxs)("button",{onClick:()=>n(s),className:"w-full text-left p-3 rounded-lg transition-colors flex items-center ".concat(e===s?"bg-blue-100 text-blue-800 border border-blue-200":"hover:bg-gray-100 text-gray-700"),children:[(0,r.jsx)(i,{className:"mr-3",size:18}),l.title]},s)})})]})}),(0,r.jsx)("div",{className:"lg:col-span-3",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[t.default.createElement(h[e].icon,{className:"mr-3 text-blue-600",size:24}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:h[e].title})]}),h[e].content]})})]})]})})}}]);