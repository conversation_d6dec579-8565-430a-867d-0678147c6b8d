module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},22734,(e,t,r)=>{t.exports=e.x("fs",()=>require("fs"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},84168,e=>{"use strict";e.s(["chapterContextDb",()=>C,"chapterDb",()=>y,"characterDb",()=>v,"jobDb",()=>S,"novelContextDb",()=>I,"novelDb",()=>f,"presetDb",()=>A,"ruleDb",()=>x]);var t=e.i(22734),r=e.i(14747),n=e.i(54799);let a=r.default.join(process.cwd(),"data"),i=r.default.join(a,"novels.json"),o=r.default.join(a,"chapters.json"),s=r.default.join(a,"rewrite_rules.json"),l=r.default.join(a,"rewrite_jobs.json"),d=r.default.join(a,"characters.json"),u=r.default.join(a,"presets.json"),c=r.default.join(a,"novel-contexts.json"),p=r.default.join(a,"chapter-contexts.json");function m(){t.default.existsSync(a)||t.default.mkdirSync(a,{recursive:!0})}function g(e){if(m(),!t.default.existsSync(e))return[];try{let r=t.default.readFileSync(e,"utf-8");return JSON.parse(r)}catch(t){return console.error(`Error reading ${e}:`,t),[]}}function h(e,r){m();try{t.default.writeFileSync(e,JSON.stringify(r,null,2),"utf-8")}catch(t){throw console.error(`Error writing ${e}:`,t),t}}function w(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}let f={getAll:()=>g(i),getById:e=>g(i).find(t=>t.id===e),create:e=>{var t;let r=g(i),a=(t=e.title,n.default.createHash("md5").update(t).digest("hex").substring(0,18)),o=r.find(e=>e.id===a);if(o)return o.filename=e.filename,o.chapterCount=e.chapterCount,h(i,r),o;let s={...e,id:a,createdAt:new Date().toISOString()};return r.push(s),h(i,r),s},update:(e,t)=>{let r=g(i),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t},h(i,r),r[n])},delete:e=>{let t=g(i),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(i,t),!0)}},y={getAll:()=>g(o),getByNovelId:e=>g(o).filter(t=>t.novelId===e),getById:e=>g(o).find(t=>t.id===e),create:e=>{let t=g(o),r={...e,id:w(),createdAt:new Date().toISOString()};return t.push(r),h(o,t),r},createBatch:e=>{let t=g(o),r=e.map(e=>({...e,id:w(),createdAt:new Date().toISOString()}));return t.push(...r),h(o,t),r},delete:e=>{let t=g(o),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(o,t),!0)},deleteByNovelId:e=>{let t=g(o).filter(t=>t.novelId!==e);return h(o,t),!0}},x={getAll:()=>g(s),getById:e=>g(s).find(t=>t.id===e),create:e=>{let t=g(s),r={...e,id:w(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(s,t),r},update:(e,t)=>{let r=g(s),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(s,r),r[n])},delete:e=>{let t=g(s),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(s,t),!0)}},S={getAll:()=>g(l),getById:e=>g(l).find(t=>t.id===e),create:e=>{let t=g(l),r={...e,id:w(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(l,t),r},update:(e,t)=>{let r=g(l),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(l,r),r[n])},delete:e=>{let t=g(l),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(l,t),!0)}},v={getAll:()=>g(d),getByNovelId:e=>g(d).filter(t=>t.novelId===e),getById:e=>g(d).find(t=>t.id===e),create:e=>{let t=g(d),r={...e,id:w(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(d,t),r},update:(e,t)=>{let r=g(d),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(d,r),r[n])},delete:e=>{let t=g(d),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(d,t),!0)},deleteByNovelId:e=>{let t=g(d).filter(t=>t.novelId!==e);return h(d,t),!0}},A={getAll:()=>g(u),getById:e=>g(u).find(t=>t.id===e),create:e=>{let t=g(u),r={...e,id:w(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(u,t),r},update:(e,t)=>{let r=g(u),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(u,r),r[n])},delete:e=>{let t=g(u),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(u,t),!0)}},I={getAll:()=>g(c),getByNovelId:e=>g(c).find(t=>t.novelId===e),getById:e=>g(c).find(t=>t.id===e),create:e=>{let t=g(c),r={...e,id:w(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(c,t),r},update:(e,t)=>{let r=g(c),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(c,r),r[n]},delete:e=>{let t=g(c),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(c,t),!0)}},C={getAll:()=>g(p),getByNovelId:e=>g(p).filter(t=>t.novelId===e),getByChapter:(e,t)=>g(p).find(r=>r.novelId===e&&r.chapterNumber===t),getById:e=>g(p).find(t=>t.id===e),create:e=>{let t=g(p),r={...e,id:w(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),h(p,t),r},update:(e,t)=>{let r=g(p),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},h(p,r),r[n]},delete:e=>{let t=g(p),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),h(p,t),!0)},getContextWindow:(e,t,r=2)=>{let n=g(p).filter(t=>t.novelId===e),a=Math.max(1,t-r),i=t+r;return n.filter(e=>e.chapterNumber>=a&&e.chapterNumber<=i).sort((e,t)=>e.chapterNumber-t.chapterNumber)}}},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},71994,e=>{"use strict";e.s(["PRESET_RULES",()=>c,"addCustomPreset",()=>m,"getApiKeyStats",()=>d,"loadCustomPresets",()=>p,"resetApiKeyStats",()=>u,"rewriteChapters",()=>o,"rewriteText",()=>i,"rewriteTextWithContext",()=>g,"testGeminiConnection",()=>l]);let t=[{key:"AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw",name:"My First Project",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y",name:"ankibot",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY",name:"Generative Language Client",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc",name:"In The Novel",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk",name:"chat",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0}],r=(e="gemini-2.5-flash-lite")=>`https://generativelanguage.googleapis.com/v1beta/models/${e}:generateContent`;class n{keys=[...t];getBestAvailableKey(){let e=Date.now(),t=this.keys.filter(t=>t.cooldownUntil<=e);return 0===t.length?this.keys.reduce((e,t)=>t.cooldownUntil<e.cooldownUntil?t:e):t.reduce((e,t)=>t.weight/(t.requestCount+1)>e.weight/(e.requestCount+1)?t:e)}recordUsage(e,t){let r=this.keys.find(t=>t.name===e);r&&(r.requestCount++,r.lastUsed=Date.now(),t||(r.cooldownUntil=Date.now()+6e4))}getStats(){return this.keys.map(e=>({name:e.name,requestCount:e.requestCount,weight:e.weight,isAvailable:e.cooldownUntil<=Date.now(),cooldownRemaining:Math.max(0,e.cooldownUntil-Date.now())}))}}let a=new n;async function i(e){let t=Date.now(),n="",i="";for(let o=0;o<5;o++)try{let s,l=a.getBestAvailableKey();if(l.cooldownUntil>Date.now()){let e=Math.min(l.cooldownUntil-Date.now(),3e4),t=Math.min(2e3*Math.pow(2,o),3e4),r=Math.max(e,t);console.log(`等待 ${r}ms (尝试 ${o+1}/5, API Key: ${l.name})`),await new Promise(e=>setTimeout(e,r))}let d=function(e){let{originalText:t,rules:r,chapterTitle:n,chapterNumber:a,novelContext:i,chapterContext:o}=e,s=`你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：

改写规则：
${r}

${n?`当前章节：${n}`:""}`;return i&&(s+=`

【小说背景信息】
小说摘要：${i.summary}

主要人物：
${i.mainCharacters.map(e=>`- ${e.name}(${e.role}): ${e.description}${e.relationships?` | 关系：${e.relationships}`:""}`).join("\n")}

世界观设定：${i.worldSetting}

写作风格：${i.writingStyle}

整体语调：${i.tone}`),o&&(s+=`

【章节上下文信息】`,o.previousChapterSummary&&(s+=`
前一章摘要：${o.previousChapterSummary}`),o.keyEvents.length>0&&(s+=`
本章关键事件：${o.keyEvents.join("、")}`),o.characterStates.length>0&&(s+=`
人物状态：
${o.characterStates.map(e=>`- ${e.name}: ${e.status} | 情感：${e.emotions} | 关系：${e.relationships}`).join("\n")}`),s+=`
情节推进：${o.plotProgress}`,o.contextualNotes&&(s+=`
重要注释：${o.contextualNotes}`)),s+=`

原文内容：
${t}

请严格按照改写规则进行改写，同时保持与小说整体背景和上下文的连贯性。改写后的内容应该：
1. 遵循所有指定的改写规则
2. 保持与小说整体背景的一致性
3. 确保人物性格和关系的连贯性
4. 保持情节发展的逻辑性
5. 维持原有的写作风格和语调
6. 确保文字流畅自然

请直接输出改写后的内容，不要添加任何解释或说明：`}(e),u=new AbortController,c=setTimeout(()=>u.abort(),6e4),p=r(e.model),m=await fetch(`${p}?key=${l.key}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:d}]}],generationConfig:{temperature:.6,topK:10,topP:.8,thinkingConfig:{thinkingBudget:0}}}),signal:u.signal});clearTimeout(c);let g=Date.now()-t;if(429===m.status&&(a.recordUsage(l.name,!1),n=`API限流 (${l.name})`,i=`第${o+1}次尝试: API Key "${l.name}" 遇到限流，状态码: 429`,o<4)){let e=2e3*Math.pow(2,o);console.log(`API限流，${e}ms后重试...`),await new Promise(t=>setTimeout(t,e));continue}if(!m.ok){let e=await m.text();if(console.error("Gemini API error:",e),a.recordUsage(l.name,!1),n=`API请求失败: ${m.status} ${m.statusText}`,i=`第${o+1}次尝试: HTTP ${m.status} ${m.statusText}, 响应: ${e.substring(0,200)}`,o<4){let e=1e3*(o+1);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:g,detailedError:i}}try{s=await m.json()}catch(e){if(a.recordUsage(l.name,!1),n="JSON解析失败",i=`第${o+1}次尝试: 无法解析API响应为JSON, 错误: ${e instanceof Error?e.message:"未知错误"}`,o<4){let e=1e3*(o+1);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:g,detailedError:i,retryCount:o+1}}if(a.recordUsage(l.name,!0),!s.candidates||0===s.candidates.length){if(n="没有收到有效的响应内容",i=`第${o+1}次尝试: API响应中没有candidates字段或为空数组, 完整响应: ${JSON.stringify(s).substring(0,500)}`,o<4){a.recordUsage(l.name,!1);let e=2e3*Math.pow(2,o);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:g,detailedError:i,retryCount:o+1}}let h=s.candidates[0];if("SAFETY"===h.finishReason)return{rewrittenText:"",success:!1,error:"内容被安全过滤器拦截，请调整改写规则或原文内容",apiKeyUsed:l.name,processingTime:g,detailedError:`内容被安全过滤器拦截，finishReason: SAFETY`,retryCount:o+1};if(!h.content||!h.content.parts||0===h.content.parts.length){if(n="响应内容格式错误",i=`第${o+1}次尝试: candidate内容格式错误, candidate: ${JSON.stringify(h).substring(0,300)}`,o<4){a.recordUsage(l.name,!1);let e=1e3*(o+1);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:g,detailedError:i,retryCount:o+1}}let w=h.content.parts[0].text;if(!w||w.trim().length<10){if(n="生成的内容过短或为空",i=`第${o+1}次尝试: 生成的内容长度: ${w?.length||0}, 内容: "${w?.substring(0,100)||"null"}"`,o<4){a.recordUsage(l.name,!1);let e=1e3*(o+1);await new Promise(t=>setTimeout(t,e));continue}return{rewrittenText:"",success:!1,error:n,apiKeyUsed:l.name,processingTime:g,detailedError:i,retryCount:o+1}}let f=s.usageMetadata?.totalTokenCount||0;return{rewrittenText:w.trim(),success:!0,apiKeyUsed:l.name,tokensUsed:f,model:e.model||"gemini-2.5-flash-lite",processingTime:g,retryCount:o+1}}catch(e){if(console.error("Gemini API调用错误:",e),e instanceof Error&&"AbortError"===e.name?(n="请求超时",i=`第${o+1}次尝试: 请求超时 (60秒)`):(n=`网络错误: ${e instanceof Error?e.message:"未知错误"}`,i=`第${o+1}次尝试: ${e instanceof Error?e.stack||e.message:"未知网络错误"}`),o<4){let e=2e3*Math.pow(2,o);console.log(`网络错误，${e}ms后重试...`),await new Promise(t=>setTimeout(t,e))}}return{rewrittenText:"",success:!1,error:`重试5次后仍然失败: ${n}`,processingTime:Date.now()-t,detailedError:i,retryCount:5}}async function o(e,t,r,n,l=3,d="gemini-2.5-flash-lite",u=!0){let c=Array(e.length),p=0,m=0,g=Date.now(),h=new s(l),w=async(o,s)=>{await h.acquire();let l=Date.now();try{let u=await i({originalText:o.content,rules:t,chapterTitle:o.title,chapterNumber:o.number,model:d}),h=Date.now()-l;u.tokensUsed&&(m+=u.tokensUsed);let w={success:u.success,content:u.rewrittenText,error:u.error,details:{apiKeyUsed:u.apiKeyUsed,tokensUsed:u.tokensUsed,model:u.model,processingTime:h,chapterNumber:o.number,chapterTitle:o.title}};if(c[s]=w,p++,n&&n(s,w),r){let t={completed:p,total:e.length,totalTokensUsed:m,totalTime:Date.now()-g,averageTimePerChapter:(Date.now()-g)/p,apiKeyStats:a.getStats(),currentChapter:{number:o.number,title:o.title,processingTime:h,apiKey:u.apiKeyUsed,tokens:u.tokensUsed}};r(p/e.length*100,o.number,t)}return await new Promise(e=>setTimeout(e,1e3)),u}catch(d){let t=Date.now(),i={success:!1,content:"",error:`处理失败: ${d instanceof Error?d.message:"未知错误"}`,details:{chapterNumber:o.number,chapterTitle:o.title,processingTime:t-l}};if(c[s]=i,p++,n&&n(s,i),r){let t={completed:p,total:e.length,totalTokensUsed:m,totalTime:Date.now()-g,averageTimePerChapter:(Date.now()-g)/p,apiKeyStats:a.getStats(),currentChapter:{number:o.number,title:o.title,error:d instanceof Error?d.message:"未知错误"}};r(p/e.length*100,o.number,t)}return null}finally{h.release()}},f=e.map((e,t)=>w(e,t));if(await Promise.all(f),u){let o=c.map((t,r)=>({result:t,index:r,chapter:e[r]})).filter(e=>!e.result.success);if(o.length>0){console.log(`开始恢复 ${o.length} 个失败的章节...`);let l=new s(1);for(let{index:s,chapter:u}of o){await l.acquire();try{console.log(`正在恢复第 ${u.number} 章: ${u.title}`),await new Promise(e=>setTimeout(e,5e3));let o=await i({originalText:u.content,rules:t,chapterTitle:u.title,chapterNumber:u.number,model:d});if(o.success){console.log(`成功恢复第 ${u.number} 章`);let t={success:!0,content:o.rewrittenText,error:void 0,details:{...o,chapterNumber:u.number,chapterTitle:u.title,isRecovered:!0}};if(c[s]=t,p++,n&&n(s,t),r){let t={completed:p,total:e.length,totalTokensUsed:m+(o.tokensUsed||0),totalTime:Date.now()-g,averageTimePerChapter:(Date.now()-g)/p,apiKeyStats:a.getStats(),currentChapter:{number:u.number,title:u.title,processingTime:o.processingTime,apiKey:o.apiKeyUsed,tokens:o.tokensUsed,isRecovered:!0}};r(p/e.length*100,u.number,t)}}else console.log(`第 ${u.number} 章恢复失败: ${o.error}`),c[s]={...c[s],error:`原始失败: ${c[s].error}; 恢复失败: ${o.error}`,details:{...c[s].details,recoveryAttempted:!0,recoveryError:o.error,recoveryDetailedError:o.detailedError}}}catch(e){console.error(`恢复第 ${u.number} 章时发生异常:`,e),c[s]={...c[s],error:`${c[s].error}; 恢复异常: ${e instanceof Error?e.message:"未知错误"}`,details:{...c[s].details,recoveryAttempted:!0,recoveryException:e instanceof Error?e.message:"未知错误"}}}finally{l.release()}}}}return c}class s{permits;waitQueue=[];constructor(e){this.permits=e}async acquire(){return this.permits>0?(this.permits--,Promise.resolve()):new Promise(e=>{this.waitQueue.push(e)})}release(){if(this.permits++,this.waitQueue.length>0){let e=this.waitQueue.shift();e&&(this.permits--,e())}}}async function l(){try{let e=await i({originalText:"这是一个测试文本。",rules:"保持原文不变"});return{success:e.success,error:e.error,details:{apiKeyUsed:e.apiKeyUsed,tokensUsed:e.tokensUsed,model:e.model,processingTime:e.processingTime,apiKeyStats:a.getStats()}}}catch(e){return{success:!1,error:`连接测试失败: ${e instanceof Error?e.message:"未知错误"}`,details:{apiKeyStats:a.getStats()}}}}function d(){return a.getStats()}function u(){t.forEach(e=>{e.requestCount=0,e.lastUsed=0,e.cooldownUntil=0})}let c={romance_focus:{name:"感情戏增强",description:"扩写男女主互动内容，对非感情戏部分一笔带过",rules:`请按照以下规则改写：
1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节
2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过
3. 增加角色间的情感张力和暧昧氛围
4. 保持故事主线不变，但重点突出感情发展`},character_fix:{name:"人设修正",description:"修正主角人设和对话风格",rules:`请按照以下规则改写：
1. 修正主角的性格设定，使其更加立体和讨喜
2. 改善对话风格，使其更加自然流畅
3. 去除过于中二或不合理的行为描写
4. 保持角色的核心特征，但优化表现方式`},toxic_content_removal:{name:"毒点清除",description:"移除送女、绿帽等毒点情节",rules:`请按照以下规则改写：
1. 完全移除或修改送女、绿帽、圣母等毒点情节
2. 删除或改写让读者不适的桥段
3. 保持故事逻辑的完整性
4. 用更合理的情节替代被删除的内容`},pacing_improvement:{name:"节奏优化",description:"优化故事节奏，删除拖沓内容",rules:`请按照以下规则改写：
1. 删除重复和拖沓的描写
2. 加快故事节奏，突出重点情节
3. 简化过于冗长的对话和心理描写
4. 保持故事的紧凑性和可读性`},custom:{name:"自定义规则",description:"用户自定义的改写规则",rules:""}};function p(){try{let{presetDb:t}=e.r(84168);t.getAll().forEach(e=>{c[`custom_${e.id}`]={name:e.name,description:e.description,rules:e.rules}})}catch(e){console.error("加载自定义预设失败:",e)}}function m(e,t,r){let n=`custom_${Date.now()}`;return c={...c,[n]:{name:e,description:t,rules:r}},n}async function g(t,r,n,a,o,s){try{let{novelContextDb:l,chapterContextDb:d}=e.r(84168),u=l.getByNovelId(t),c=d.getByChapter(t,r),p={originalText:n,rules:a,chapterTitle:o,chapterNumber:r,model:s,novelContext:u?{summary:u.summary,mainCharacters:u.mainCharacters,worldSetting:u.worldSetting,writingStyle:u.writingStyle,tone:u.tone}:void 0,chapterContext:c?{previousChapterSummary:c.previousChapterSummary,keyEvents:c.keyEvents,characterStates:c.characterStates,plotProgress:c.plotProgress,contextualNotes:c.contextualNotes}:void 0};return await i(p)}catch(e){return console.error("带上下文重写失败:",e),await i({originalText:n,rules:a,chapterTitle:o,chapterNumber:r,model:s})}}},53084,(e,t,r)=>{},14916,e=>{"use strict";e.s(["handler",()=>T,"patchFetch",()=>b,"routeModule",()=>v,"serverHooks",()=>C,"workAsyncStorage",()=>A,"workUnitAsyncStorage",()=>I],14916);var t=e.i(47909),r=e.i(74017),n=e.i(96250),a=e.i(59756),i=e.i(61916),o=e.i(69741),s=e.i(16795),l=e.i(87718),d=e.i(95169),u=e.i(47587),c=e.i(66012),p=e.i(70101),m=e.i(26937),g=e.i(10372),h=e.i(93695);e.i(52474);var w=e.i(220);e.s(["POST",()=>x],14706);var f=e.i(89171),y=e.i(71994);async function x(){try{return(0,y.resetApiKeyStats)(),f.NextResponse.json({success:!0,message:"API Key统计已重置"})}catch(e){return console.error("重置API统计失败:",e),f.NextResponse.json({success:!1,error:"重置API统计失败"},{status:500})}}var S=e.i(14706);let v=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/gemini/reset/route",pathname:"/api/gemini/reset",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/gemini/reset/route.ts",nextConfigOutput:"",userland:S}),{workAsyncStorage:A,workUnitAsyncStorage:I,serverHooks:C}=v;function b(){return(0,n.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:I})}async function T(e,t,n){var f;let y="/api/gemini/reset/route";y=y.replace(/\/index$/,"")||"/";let x=await v.prepare(e,t,{srcPage:y,multiZoneDraftMode:!1});if(!x)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:S,params:A,nextConfig:I,isDraftMode:C,prerenderManifest:b,routerServerContext:T,isOnDemandRevalidate:$,revalidateOnlyGenerated:E,resolvedPathname:U}=x,D=(0,o.normalizeAppPath)(y),R=!!(b.dynamicRoutes[D]||b.routes[U]);if(R&&!C){let e=!!b.routes[U],t=b.dynamicRoutes[D];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let P=null;!R||v.isDev||C||(P="/index"===(P=U)?"/":P);let k=!0===v.isDev||!R,N=R&&!k,O=e.method||"GET",j=(0,i.getTracer)(),q=j.getActiveScopeSpan(),K={params:A,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!I.experimental.cacheComponents,authInterrupts:!!I.experimental.authInterrupts},supportsDynamicResponse:k,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=I.experimental)?void 0:f.cacheLife,isRevalidate:N,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>v.onRequestError(e,t,n,T)},sharedContext:{buildId:S}},_=new s.NodeNextRequest(e),B=new s.NodeNextResponse(t),M=l.NextRequestAdapter.fromNodeNextRequest(_,(0,l.signalFromNodeResponse)(t));try{let o=async r=>v.handle(M,K).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=j.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${O} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${O} ${e.url}`)}),s=async i=>{var s,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&$&&E&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let s=await o(i);e.fetchMetrics=K.renderOpts.fetchMetrics;let l=K.renderOpts.pendingWaitUntil;l&&n.waitUntil&&(n.waitUntil(l),l=void 0);let d=K.renderOpts.collectedTags;if(!R)return await (0,c.sendResponse)(_,B,s,K.renderOpts.pendingWaitUntil),null;{let e=await s.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(s.headers);d&&(t[g.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==K.renderOpts.collectedRevalidate&&!(K.renderOpts.collectedRevalidate>=g.INFINITE_CACHE)&&K.renderOpts.collectedRevalidate,n=void 0===K.renderOpts.collectedExpire||K.renderOpts.collectedExpire>=g.INFINITE_CACHE?void 0:K.renderOpts.collectedExpire;return{value:{kind:w.CachedRouteKind.APP_ROUTE,status:s.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await v.onRequestError(e,t,{routerKind:"App Router",routePath:y,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:N,isOnDemandRevalidate:$})},T),t}},h=await v.handleResponse({req:e,nextConfig:I,cacheKey:P,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:$,revalidateOnlyGenerated:E,responseGenerator:d,waitUntil:n.waitUntil});if(!R)return null;if((null==h||null==(s=h.value)?void 0:s.kind)!==w.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(l=h.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",$?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,p.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&R||f.delete(g.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,m.getCacheControlHeader)(h.cacheControl)),await (0,c.sendResponse)(_,B,new Response(h.value.body,{headers:f,status:h.value.status||200})),null};q?await s(q):await j.withPropagatedContext(e.headers,()=>j.trace(d.BaseServerSpan.handleRequest,{spanName:`${O} ${e.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":O,"http.target":e.url}},s))}catch(t){if(q||t instanceof h.NoFallbackError||await v.onRequestError(e,t,{routerKind:"App Router",routePath:D,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:N,isOnDemandRevalidate:$})}),R)throw t;return await (0,c.sendResponse)(_,B,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__7eafbccb._.js.map