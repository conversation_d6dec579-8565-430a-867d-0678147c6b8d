{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts", "turbopack:///[project]/src/components/MergeNovels.tsx"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxRuntime\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n", "'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface Novel {\n  id: string;\n  title: string;\n  filename: string;\n  createdAt: string;\n  chapterCount?: number;\n  rewrittenChaptersCount?: number;\n}\n\ninterface MergeResult {\n  filePath: string;\n  novelTitle: string;\n}\n\nexport default function MergeNovels() {\n  const [novels, setNovels] = useState<Novel[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [merging, setMerging] = useState<string | null>(null);\n  const [mergeResults, setMergeResults] = useState<MergeResult[]>([]);\n\n  // 获取可合并的小说列表\n  const fetchNovels = async () => {\n    try {\n      const response = await fetch('/api/merge');\n      const result = await response.json();\n      \n      if (result.success) {\n        setNovels(result.data);\n      } else {\n        console.error('获取小说列表失败:', result.error);\n      }\n    } catch (error) {\n      console.error('获取小说列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 合并小说章节\n  const mergeNovel = async (novelId: string) => {\n    setMerging(novelId);\n    \n    try {\n      const response = await fetch('/api/merge', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ novelId }),\n      });\n      \n      const result = await response.json();\n      \n      if (result.success) {\n        setMergeResults(prev => [...prev, result.data]);\n        // 刷新列表\n        await fetchNovels();\n      } else {\n        alert(`合并失败: ${result.error}`);\n      }\n    } catch (error) {\n      console.error('合并失败:', error);\n      alert('合并失败');\n    } finally {\n      setMerging(null);\n    }\n  };\n\n  useEffect(() => {\n    fetchNovels();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"text-lg\">加载中...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">合并改写章节</h1>\n        <p className=\"text-gray-600\">将改写完成的章节合并为完整的小说文件</p>\n      </div>\n\n      {/* 可合并的小说列表 */}\n      <div className=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n        <h2 className=\"text-xl font-semibold mb-4\">可合并的小说</h2>\n        \n        {novels.length === 0 ? (\n          <div className=\"text-center py-8 text-gray-500\">\n            暂无可合并的小说\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {novels.map((novel) => (\n              <div key={novel.id} className=\"border rounded-lg p-4 flex justify-between items-center\">\n                <div>\n                  <h3 className=\"font-medium text-lg\">{novel.title}</h3>\n                  <p className=\"text-sm text-gray-600\">\n                    已改写章节: {novel.rewrittenChaptersCount} / 总章节: {novel.chapterCount || 0}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    创建时间: {new Date(novel.createdAt).toLocaleString()}\n                  </p>\n                </div>\n                \n                <button\n                  onClick={() => mergeNovel(novel.id)}\n                  disabled={merging === novel.id}\n                  className={`px-4 py-2 rounded-md font-medium ${\n                    merging === novel.id\n                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                      : 'bg-blue-600 text-white hover:bg-blue-700'\n                  }`}\n                >\n                  {merging === novel.id ? '合并中...' : '合并章节'}\n                </button>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* 合并结果 */}\n      {mergeResults.length > 0 && (\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\n          <h2 className=\"text-xl font-semibold mb-4\">合并结果</h2>\n          \n          <div className=\"space-y-3\">\n            {mergeResults.map((result, index) => (\n              <div key={index} className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <svg className=\"h-5 w-5 text-green-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                  <div className=\"ml-3\">\n                    <h3 className=\"text-sm font-medium text-green-800\">\n                      成功合并《{result.novelTitle}》\n                    </h3>\n                    <p className=\"text-sm text-green-700 mt-1\">\n                      文件保存在: {result.filePath}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "vendored", "ReactJsxRuntime", "React"], "mappings": "0NA0BQG,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,iCC1BjCF,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEC,eAAe,+BCFxCP,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEE,KAAK,gECA9B,EAAA,EAAA,CAAA,CAAA,OAgBe,SAAS,IACtB,GAAM,CAAC,EAAQ,EAAU,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAU,EAAE,EAC1C,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAgB,MAChD,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAwB,EAAE,EAG5D,EAAc,UAClB,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,cACvB,EAAS,MAAM,EAAS,IAAI,GAE9B,EAAO,OAAO,CAChB,CADkB,CACR,EAAO,IAAI,EAErB,QAAQ,KAAK,CAAC,YAAa,EAAO,KAAK,CAE3C,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,YAAa,EAC7B,QAAU,CACR,GAAW,EACb,CACF,EAGM,EAAa,MAAO,IACxB,EAAW,GAEX,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,aAAc,CACzC,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,SAAE,CAAQ,EACjC,GAEM,EAAS,MAAM,EAAS,IAAI,GAE9B,EAAO,OAAO,EAAE,AAClB,EAAgB,GAAQ,IAAI,EAAM,EAAO,IAAI,CAAC,EAE9C,MAAM,KAEN,MAAM,CAAC,MAAM,EAAE,EAAO,KAAK,CAAA,CAAE,CAEjC,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,QAAS,GACvB,MAAM,OACR,QAAU,CACR,EAAW,KACb,CACF,QAMA,CAJA,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACR,GACF,EAAG,EAAE,EAED,GAEA,CAAA,EAAA,EAAA,CAFS,EAET,EAAC,MAAA,CAAI,UAAU,iDACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mBAAU,aAM7B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,iDAAwC,WACtD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,0BAI/B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,sCAA6B,WAExB,IAAlB,EAAO,MAAM,CACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0CAAiC,aAIhD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,EAAO,GAAG,CAAE,AAAD,GACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAmB,UAAU,oEAC5B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,+BAAuB,EAAM,KAAK,GAChD,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kCAAwB,UAC3B,EAAM,sBAAsB,CAAC,WAAS,EAAM,YAAY,EAAI,KAEtE,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kCAAwB,SAC5B,IAAI,KAAK,EAAM,SAAS,EAAE,cAAc,SAInD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAW,EAAM,EAAE,EAClC,SAAU,IAAY,EAAM,EAAE,CAC9B,UAAW,CAAC,iCAAiC,EAC3C,IAAY,EAAM,EAAE,CAChB,+CACA,2CAAA,CACJ,UAED,IAAY,EAAM,EAAE,CAAG,SAAW,WApB7B,EAAM,EAAE,QA6BzB,EAAa,MAAM,CAAG,GACrB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,sCAA6B,SAE3C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,EAAa,GAAG,CAAC,CAAC,EAAQ,IACzB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAgB,UAAU,8DACzB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAyB,QAAQ,YAAY,KAAK,wBAC/D,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,SAAS,UAAU,EAAE,wIAAwI,SAAS,gBAGhL,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,+CAAqC,QAC3C,EAAO,UAAU,CAAC,OAE1B,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,wCAA8B,UACjC,EAAO,QAAQ,WAZrB,WAuBxB", "ignoreList": [0, 1, 2]}