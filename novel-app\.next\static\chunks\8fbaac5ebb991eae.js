(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,52683,e=>{"use strict";e.s(["default",()=>F],52683);var s=e.i(43476),t=e.i(71645),a=e.i(22016),l=e.i(10980),r=e.i(75254);let i=(0,r.default)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),c=(0,r.default)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);function n(e){let{selectedNovel:a,onNovelSelect:r,disabled:n}=e,[d,o]=(0,t.useState)([]),[m,x]=(0,t.useState)([]),[h,u]=(0,t.useState)(!1),[p,g]=(0,t.useState)(null);(0,t.useEffect)(()=>{b()},[]);let b=async()=>{u(!0);try{let e=await fetch("/api/novels"),s=await e.json();s.success?(o(s.data.novels),x(s.data.availableFiles)):console.error("加载小说列表失败:",s.error)}catch(e){console.error("加载小说列表失败:",e)}finally{u(!1)}},j=async function(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];g(e);try{let t=await fetch("/api/novels",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filename:e,reparse:s})}),a=await t.json();a.success?(await b(),alert(a.message)):alert("解析失败: ".concat(a.error))}catch(e){console.error("解析小说失败:",e),alert("解析小说失败")}finally{g(null)}};return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold text-gray-800 flex items-center",children:[(0,s.jsx)(l.BookOpen,{className:"mr-2",size:18}),"选择小说"]}),(0,s.jsx)("button",{onClick:b,disabled:h||n,className:"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50",title:"刷新列表",children:(0,s.jsx)(i,{className:"".concat(h?"animate-spin":""),size:16})})]}),h?(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:"加载中..."}):(0,s.jsxs)("div",{className:"space-y-2",children:[d.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"已解析的小说"}),(0,s.jsx)("div",{className:"space-y-1",children:d.map(e=>(0,s.jsxs)("div",{onClick:()=>{!n&&r(e)},className:"p-2 border rounded cursor-pointer transition-colors ".concat((null==a?void 0:a.id)===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"," ").concat(n?"opacity-50 cursor-not-allowed":""),children:[(0,s.jsx)("div",{className:"font-medium text-gray-800 text-sm",children:e.title}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[e.chapterCount||0," 章节 • ",e.filename]})]},e.id))})]}),m.filter(e=>!e.parsed).length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"未解析的文件"}),(0,s.jsx)("div",{className:"space-y-1",children:m.filter(e=>!e.parsed).map(e=>(0,s.jsx)("div",{className:"p-2 border border-gray-200 rounded bg-gray-50",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("div",{className:"font-medium text-gray-800 text-sm truncate",children:e.filename}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:"未解析"})]}),(0,s.jsx)("button",{onClick:()=>j(e.filename),disabled:p===e.filename||n,className:"flex items-center px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 ml-2",children:p===e.filename?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i,{className:"animate-spin mr-1",size:12}),"解析中"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c,{className:"mr-1",size:12}),"解析"]})})]})},e.filename))})]}),0===m.length&&(0,s.jsxs)("div",{className:"text-center py-6 text-gray-500",children:[(0,s.jsx)(l.BookOpen,{className:"mx-auto mb-2",size:32}),(0,s.jsx)("p",{className:"text-sm",children:"novels 文件夹中没有找到小说文件"}),(0,s.jsx)("p",{className:"text-xs",children:"请将 .txt 或 .md 文件放入 novels 文件夹"})]})]}),a&&(0,s.jsxs)("div",{className:"mt-3 p-2 bg-blue-50 border border-blue-200 rounded",children:[(0,s.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,s.jsx)("strong",{children:"已选择:"})," ",a.title]}),(0,s.jsxs)("div",{className:"text-xs text-blue-600",children:[a.chapterCount||0," 章节"]})]})]})}let d=(0,r.default)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),o=(0,r.default)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);function m(e){let{novel:a,selectedChapters:l,onChaptersChange:r,disabled:i}=e,[c,n]=(0,t.useState)([]),[m,x]=(0,t.useState)(!1),[h,u]=(0,t.useState)([]);(0,t.useEffect)(()=>{a?p(a.id):(n([]),u([]))},[a]),(0,t.useEffect)(()=>{l&&c.length>0?u(g(l,c.length)):u([])},[l,c]);let p=async e=>{x(!0);try{let s=await fetch("/api/chapters?novelId=".concat(e)),t=await s.json();t.success?n(t.data):console.error("加载章节列表失败:",t.error)}catch(e){console.error("加载章节列表失败:",e)}finally{x(!1)}},g=(e,s)=>{let t=[];for(let a of e.split(",").map(e=>e.trim()))if(a.includes("-")){let[e,l]=a.split("-").map(e=>parseInt(e.trim()));if(!isNaN(e)&&!isNaN(l)&&e<=l)for(let a=e;a<=Math.min(l,s);a++)a>0&&!t.includes(a)&&t.push(a)}else{let e=parseInt(a);!isNaN(e)&&e>0&&e<=s&&!t.includes(e)&&t.push(e)}return t.sort((e,s)=>e-s)},b=e=>{if(i||0===c.length)return;let s="";switch(e){case"all":s="1-".concat(c.length);break;case"first10":s="1-".concat(Math.min(10,c.length));break;case"last10":s="".concat(Math.max(1,c.length-9),"-").concat(c.length)}r(s)};return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold text-gray-800 mb-3 flex items-center",children:[(0,s.jsx)(d,{className:"mr-2",size:18}),"选择章节 ",a&&(0,s.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["共 ",c.length," 章"]})]}),a?m?(0,s.jsx)("div",{className:"text-center py-6 text-gray-500 text-sm",children:"加载中..."}):(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"章节范围"}),(0,s.jsx)("input",{type:"text",value:l,onChange:e=>r(e.target.value),disabled:i,placeholder:"例如: 1-5,7,10-12",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"}),(0,s.jsxs)("div",{className:"mt-1 flex items-center text-xs text-gray-500",children:[(0,s.jsx)(o,{className:"mr-1",size:12}),"支持范围(1-5)、单个章节(7)、组合(1-5,7,10-12)"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"快速选择"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,s.jsxs)("button",{onClick:()=>b("all"),disabled:i,className:"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50",children:["全部章节 (1-",c.length,")"]}),(0,s.jsx)("button",{onClick:()=>b("first10"),disabled:i,className:"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50",children:"前10章"}),(0,s.jsx)("button",{onClick:()=>b("last10"),disabled:i,className:"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50",children:"后10章"})]})]}),h.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["将要改写的章节 (",h.length," 章)"]}),(0,s.jsx)("div",{className:"max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3 bg-gray-50",children:(0,s.jsx)("div",{className:"grid grid-cols-1 gap-1 text-sm",children:h.map(e=>{let t=c.find(s=>s.chapterNumber===e);return(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)("span",{className:"font-medium text-blue-600 w-12",children:["第",e,"章"]}),(0,s.jsx)("span",{className:"text-gray-700 truncate",children:(null==t?void 0:t.title)||"未知标题"})]},e)})})})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["所有章节 (",c.length," 章)"]}),(0,s.jsx)("div",{className:"max-h-60 overflow-y-auto border border-gray-200 rounded-md",children:c.map(e=>(0,s.jsx)("div",{className:"p-2 border-b border-gray-100 last:border-b-0 ".concat(h.includes(e.chapterNumber)?"bg-blue-50 border-l-4 border-l-blue-500":"hover:bg-gray-50"),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"font-medium text-gray-800 truncate",children:["第",e.chapterNumber,"章 ",e.title]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[e.content.length," 字符"]})]}),h.includes(e.chapterNumber)&&(0,s.jsx)("div",{className:"ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:"已选择"})]})},e.id))})]})]}):(0,s.jsxs)("div",{className:"text-center py-6 text-gray-500",children:[(0,s.jsx)(d,{className:"mx-auto mb-2",size:32}),(0,s.jsx)("p",{className:"text-sm",children:"请先选择一部小说"})]})]})}let x=(0,r.default)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var h=e.i(33060);let u=(0,r.default)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),p=[{key:"AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw",name:"My First Project",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y",name:"ankibot",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY",name:"Generative Language Client",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc",name:"In The Novel",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0},{key:"AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk",name:"chat",weight:1,requestCount:0,lastUsed:0,cooldownUntil:0}];new class{getBestAvailableKey(){let e=Date.now(),s=this.keys.filter(s=>s.cooldownUntil<=e);return 0===s.length?this.keys.reduce((e,s)=>s.cooldownUntil<e.cooldownUntil?s:e):s.reduce((e,s)=>s.weight/(s.requestCount+1)>e.weight/(e.requestCount+1)?s:e)}recordUsage(e,s){let t=this.keys.find(s=>s.name===e);t&&(t.requestCount++,t.lastUsed=Date.now(),s||(t.cooldownUntil=Date.now()+6e4))}getStats(){return this.keys.map(e=>({name:e.name,requestCount:e.requestCount,weight:e.weight,isAvailable:e.cooldownUntil<=Date.now(),cooldownRemaining:Math.max(0,e.cooldownUntil-Date.now())}))}constructor(){!function(e,s,t){s in e?Object.defineProperty(e,s,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[s]=t}(this,"keys",[...p])}};let g={romance_focus:{name:"感情戏增强",description:"扩写男女主互动内容，对非感情戏部分一笔带过",rules:"请按照以下规则改写：\n1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节\n2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过\n3. 增加角色间的情感张力和暧昧氛围\n4. 保持故事主线不变，但重点突出感情发展"},character_fix:{name:"人设修正",description:"修正主角人设和对话风格",rules:"请按照以下规则改写：\n1. 修正主角的性格设定，使其更加立体和讨喜\n2. 改善对话风格，使其更加自然流畅\n3. 去除过于中二或不合理的行为描写\n4. 保持角色的核心特征，但优化表现方式"},toxic_content_removal:{name:"毒点清除",description:"移除送女、绿帽等毒点情节",rules:"请按照以下规则改写：\n1. 完全移除或修改送女、绿帽、圣母等毒点情节\n2. 删除或改写让读者不适的桥段\n3. 保持故事逻辑的完整性\n4. 用更合理的情节替代被删除的内容"},pacing_improvement:{name:"节奏优化",description:"优化故事节奏，删除拖沓内容",rules:"请按照以下规则改写：\n1. 删除重复和拖沓的描写\n2. 加快故事节奏，突出重点情节\n3. 简化过于冗长的对话和心理描写\n4. 保持故事的紧凑性和可读性"},custom:{name:"自定义规则",description:"用户自定义的改写规则",rules:""}};function b(e){let{rules:a,onRulesChange:l,disabled:r,onSaveToPreset:i}=e,[c,n]=(0,t.useState)(!1),[d,o]=(0,t.useState)([]),[m,p]=(0,t.useState)({});(0,t.useEffect)(()=>{b()},[]);let b=async()=>{try{let e=await fetch("/api/presets"),s=await e.json();if(s.success){o(s.data);let e={...g};s.data.forEach(s=>{e["custom_".concat(s.id)]={name:s.name,description:s.description,rules:s.rules}}),p(e)}}catch(e){console.error("加载自定义预设失败:",e),p(g)}},j=async()=>{a.trim()&&i&&(await i(a),await b())},y=Object.entries(m).filter(e=>{let[s]=e;return"custom"!==s});return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-800 flex items-center",children:[(0,s.jsx)(x,{className:"mr-2",size:20}),"改写规则"]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:j,disabled:r||!a.trim(),className:"p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50",title:"保存为预设",children:(0,s.jsx)(u,{size:16})}),(0,s.jsx)("button",{onClick:()=>n(!c),disabled:r,className:"p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50",title:"预设规则",children:(0,s.jsx)(h.Wand2,{size:16})})]})]}),c&&(0,s.jsxs)("div",{className:"mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-800 mb-2 text-sm",children:"选择预设规则"}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-1",children:y.map(e=>{let[t,a]=e;return(0,s.jsxs)("button",{onClick:()=>(e=>{let s=m[e];s&&(l(s.rules),n(!1))})(t),disabled:r,className:"text-left p-2 border border-gray-200 rounded hover:border-blue-300 hover:bg-blue-50 disabled:opacity-50 transition-colors",children:[(0,s.jsx)("div",{className:"font-medium text-gray-800 text-sm",children:a.name}),(0,s.jsx)("div",{className:"text-xs text-gray-600",children:a.description})]},t)})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"改写规则内容"}),(0,s.jsx)("textarea",{value:a,onChange:e=>l(e.target.value),disabled:r,placeholder:"请输入详细的改写规则，例如：  1. 扩写男女主角之间的互动情节 2. 对战斗场面一笔带过 3. 增加情感描写和心理活动 4. 修改不合理的人物行为 ...",className:"w-full h-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none disabled:bg-gray-100"}),(0,s.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:[a.length," 字符 • 建议详细描述改写要求以获得更好的效果"]})]})]})}let j=(0,r.default)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),y=(0,r.default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),v=(0,r.default)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function f(e){let{novelId:a,characters:l,onCharactersChange:r,disabled:i}=e,[c,n]=(0,t.useState)(!1),[d,o]=(0,t.useState)(!1),[m,x]=(0,t.useState)({name:"",role:"其他",description:""});(0,t.useEffect)(()=>{a?h():r([])},[a]);let h=async()=>{if(a)try{let e=await fetch("/api/characters?novelId=".concat(a)),s=await e.json();s.success&&r(s.data)}catch(e){console.error("加载人物设定失败:",e)}},u=async()=>{if(m.name.trim()&&a){o(!0);try{let e=await fetch("/api/characters",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({novelId:a,name:m.name,role:m.role,description:m.description})}),s=await e.json();s.success?(await h(),x({name:"",role:"其他",description:""}),n(!1)):alert("添加失败: ".concat(s.error))}catch(e){console.error("添加人物失败:",e),alert("添加人物失败")}finally{o(!1)}}},p=async e=>{if(confirm("确定要删除这个人物设定吗？"))try{let s=await fetch("/api/characters?id=".concat(e),{method:"DELETE"}),t=await s.json();t.success?await h():alert("删除失败: ".concat(t.error))}catch(e){console.error("删除人物失败:",e),alert("删除人物失败")}};return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold text-gray-800 flex items-center",children:[(0,s.jsx)(j,{className:"mr-2",size:18}),"人物设定"]}),(0,s.jsx)("button",{onClick:()=>n(!c),disabled:i,className:"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50",title:"添加人物",children:(0,s.jsx)(y,{size:16})})]}),c&&(0,s.jsx)("div",{className:"mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("input",{type:"text",placeholder:"人物名称",value:m.name,onChange:e=>x({...m,name:e.target.value}),className:"flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"}),(0,s.jsx)("select",{value:m.role,onChange:e=>x({...m,role:e.target.value}),className:"px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500",children:["男主","女主","配角","反派","其他"].map(e=>(0,s.jsx)("option",{value:e,children:e},e))})]}),(0,s.jsx)("input",{type:"text",placeholder:"备注描述",value:m.description,onChange:e=>x({...m,description:e.target.value}),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:u,className:"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700",children:"添加"}),(0,s.jsx)("button",{onClick:()=>n(!1),className:"px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600",children:"取消"})]})]})}),(0,s.jsx)("div",{className:"space-y-2",children:0===l.length?(0,s.jsx)("div",{className:"text-center py-4 text-gray-500 text-sm",children:"暂无人物设定"}):l.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-medium text-gray-800 text-sm",children:e.name}),(0,s.jsx)("span",{className:"px-2 py-0.5 text-xs rounded ".concat("男主"===e.role?"bg-blue-100 text-blue-800":"女主"===e.role?"bg-pink-100 text-pink-800":"配角"===e.role?"bg-green-100 text-green-800":"反派"===e.role?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:e.role})]}),e.description&&(0,s.jsx)("div",{className:"text-xs text-gray-600 mt-1 truncate",children:e.description})]}),(0,s.jsx)("button",{onClick:()=>p(e.id),disabled:i,className:"p-1 text-gray-400 hover:text-red-600 disabled:opacity-50",title:"删除",children:(0,s.jsx)(v,{size:14})})]},e.id))})]})}var N=e.i(69638),w=e.i(73884);let k=(0,r.default)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),C=(0,r.default)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),S=(0,r.default)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);function M(e){let{jobId:a,failedChapters:l,rules:r,model:i="gemini-2.5-flash-lite",onRetryStart:c,onRetryComplete:n}=e,[d,o]=(0,t.useState)([]),[m,x]=(0,t.useState)(!1),[h,u]=(0,t.useState)(""),p=async()=>{if(0===d.length)return void alert("请选择要重试的章节");x(!0),u("正在重试失败的章节..."),c&&c();try{let e=await fetch("/api/rewrite/retry",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({jobId:a,chapterNumbers:d,rules:r,model:i})}),s=await e.json();s.success?(u("重试任务已创建，正在处理 ".concat(d.length," 个章节...")),n&&n(!0,s.data.message)):(u("重试失败: ".concat(s.error)),n&&n(!1,s.error))}catch(s){let e="重试请求失败: ".concat(s instanceof Error?s.message:"未知错误");u(e),n&&n(!1,e)}finally{x(!1)}};return 0===l.length?null:(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mt-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-red-800",children:["失败章节 (",l.length," 个)"]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("button",{onClick:()=>{d.length===l.length?o([]):o(l.map(e=>e.chapterNumber))},className:"px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded transition-colors",children:d.length===l.length?"取消全选":"全选"}),(0,s.jsx)("button",{onClick:p,disabled:m||0===d.length,className:"px-4 py-1 text-sm bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded transition-colors",children:m?"重试中...":"重试选中章节 (".concat(d.length,")")})]})]}),h&&(0,s.jsx)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded text-blue-800",children:h}),(0,s.jsx)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:l.map(e=>(0,s.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-white border border-red-200 rounded",children:[(0,s.jsx)("input",{type:"checkbox",checked:d.includes(e.chapterNumber),onChange:()=>{var s;return s=e.chapterNumber,void o(e=>e.includes(s)?e.filter(e=>e!==s):[...e,s])},className:"mt-1"}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,s.jsxs)("span",{className:"font-medium text-gray-900",children:["第 ",e.chapterNumber," 章"]}),(0,s.jsx)("span",{className:"text-gray-600 truncate",children:e.chapterTitle})]}),(0,s.jsxs)("div",{className:"text-sm text-red-600 mb-1",children:["错误: ",e.error||"未知错误"]}),e.detailedError&&(0,s.jsxs)("details",{className:"text-xs text-gray-500",children:[(0,s.jsx)("summary",{className:"cursor-pointer hover:text-gray-700",children:"详细错误信息"}),(0,s.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded border text-xs font-mono whitespace-pre-wrap",children:e.detailedError})]}),(0,s.jsxs)("div",{className:"flex gap-4 text-xs text-gray-500 mt-1",children:[e.apiKeyUsed&&(0,s.jsxs)("span",{children:["API Key: ",e.apiKeyUsed]}),e.processingTime&&(0,s.jsxs)("span",{children:["处理时间: ",e.processingTime,"ms"]})]})]})]},e.chapterNumber))}),(0,s.jsxs)("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded",children:[(0,s.jsx)("h4",{className:"font-medium text-yellow-800 mb-2",children:"重试建议:"}),(0,s.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,s.jsx)("li",{children:"• 重试将使用更保守的策略，串行处理章节以避免API限制"}),(0,s.jsx)("li",{children:"• 如果仍然失败，可能需要检查API key配额或调整改写规则"}),(0,s.jsx)("li",{children:"• 建议在API使用量较低的时间段进行重试"})]})]})]})}let A=(0,r.default)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);function z(e){let{jobId:a,isOpen:l,onClose:r}=e,[c,n]=(0,t.useState)(null),[d,m]=(0,t.useState)(!1),[x,h]=(0,t.useState)(null),u=async()=>{m(!0),h(null);try{let e=await fetch("/api/rewrite/diagnostics?jobId=".concat(a)),s=await e.json();s.success?n(s.data):h(s.error)}catch(e){h("获取诊断信息失败: ".concat(e instanceof Error?e.message:"未知错误"))}finally{m(!1)}};return((0,t.useEffect)(()=>{l&&a&&u()},[l,a]),l)?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold",children:"任务诊断报告"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("button",{onClick:u,disabled:d,className:"p-2 text-gray-500 hover:text-gray-700 disabled:opacity-50",children:(0,s.jsx)(i,{className:"w-4 h-4 ".concat(d?"animate-spin":"")})}),(0,s.jsx)("button",{onClick:r,className:"p-2 text-gray-500 hover:text-gray-700",children:"✕"})]})]}),(0,s.jsxs)("div",{className:"p-4 overflow-y-auto max-h-[calc(90vh-80px)]",children:[d&&(0,s.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),(0,s.jsx)("span",{className:"ml-2",children:"正在获取诊断信息..."})]}),x&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-4",children:(0,s.jsxs)("div",{className:"flex items-center text-red-700",children:[(0,s.jsx)(w.XCircle,{className:"w-5 h-5 mr-2"}),(0,s.jsx)("span",{children:x})]})}),c&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold mb-3",children:"任务概览"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"状态:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:c.jobInfo.status})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"进度:"}),(0,s.jsxs)("span",{className:"ml-2 font-medium",children:[c.jobInfo.progress,"%"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"成功:"}),(0,s.jsxs)("span",{className:"ml-2 font-medium text-green-600",children:[c.jobInfo.completedChapters,"/",c.jobInfo.totalChapters]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"失败:"}),(0,s.jsx)("span",{className:"ml-2 font-medium text-red-600",children:c.jobInfo.failedChapters})]})]})]}),(0,s.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold mb-3",children:"API Key状态"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"连接测试"}),(0,s.jsxs)("div",{className:"flex items-center ".concat(c.apiKeyStatus.connectionTest.success?"text-green-600":"text-red-600"),children:[c.apiKeyStatus.connectionTest.success?(0,s.jsx)(N.CheckCircle,{className:"w-4 h-4 mr-2"}):(0,s.jsx)(w.XCircle,{className:"w-4 h-4 mr-2"}),(0,s.jsx)("span",{className:"text-sm",children:c.apiKeyStatus.connectionTest.success?"连接正常":"连接失败"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"Key统计"}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:["可用: ",c.systemHealth.availableKeys,"/",c.systemHealth.totalKeys," | 总调用: ",c.systemHealth.totalApiCalls]})]})]}),(0,s.jsxs)("div",{className:"mt-3",children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"详细状态"}),(0,s.jsx)("div",{className:"space-y-1 text-sm",children:c.apiKeyStatus.stats.map((e,t)=>(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{children:e.name}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("span",{className:"text-gray-600",children:[e.requestCount," 次调用"]}),(0,s.jsx)("span",{className:"px-2 py-1 rounded text-xs ".concat(e.isAvailable?"bg-green-100 text-green-700":"bg-red-100 text-red-700"),children:e.isAvailable?"可用":"冷却中"})]})]},t))})]})]}),c.errorAnalysis.totalFailures>0&&(0,s.jsxs)("div",{className:"bg-red-50 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold mb-3",children:"错误分析"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"错误类型分布"}),(0,s.jsx)("div",{className:"space-y-1 text-sm",children:Object.entries(c.errorAnalysis.errorTypes).map(e=>{let[t,a]=e;return(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:t}),(0,s.jsx)("span",{className:"font-medium",children:a})]},t)})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"关键信息"}),(0,s.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,s.jsxs)("div",{children:["最常见错误: ",(0,s.jsx)("span",{className:"font-medium",children:c.errorAnalysis.mostCommonError})]}),(0,s.jsxs)("div",{children:["问题API Key: ",(0,s.jsx)("span",{className:"font-medium",children:c.errorAnalysis.problematicApiKey})]}),(0,s.jsxs)("div",{children:["超时错误: ",(0,s.jsx)("span",{className:"font-medium",children:c.errorAnalysis.patterns.timeoutErrors})]}),(0,s.jsxs)("div",{children:["内容错误: ",(0,s.jsx)("span",{className:"font-medium",children:c.errorAnalysis.patterns.contentErrors})]})]})]})]})]}),(0,s.jsxs)("div",{className:"bg-yellow-50 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold mb-3",children:"改进建议"}),(0,s.jsx)("div",{className:"space-y-3",children:c.recommendations.map((e,t)=>(0,s.jsxs)("div",{className:"border border-yellow-200 rounded p-3",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(e=>{switch(e){case"error":return(0,s.jsx)(w.XCircle,{className:"w-4 h-4 text-red-500"});case"warning":return(0,s.jsx)(A,{className:"w-4 h-4 text-yellow-500"});case"info":return(0,s.jsx)(o,{className:"w-4 h-4 text-blue-500"});default:return(0,s.jsx)(N.CheckCircle,{className:"w-4 h-4 text-green-500"})}})(e.type),(0,s.jsx)("span",{className:"ml-2 font-medium",children:e.category})]}),(0,s.jsx)("p",{className:"text-sm text-gray-700 mb-2",children:e.message}),(0,s.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:e.actions.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"mr-2",children:"•"}),(0,s.jsx)("span",{children:e})]},t))})]},t))})]})]})]})]})}):null}function T(e){var a,l,r,i;let{jobId:c,onComplete:n}=e,[d,o]=(0,t.useState)(null),[m,x]=(0,t.useState)(!0),[h,u]=(0,t.useState)(!1),[p,g]=(0,t.useState)(null),[b,j]=(0,t.useState)(!1);(0,t.useEffect)(()=>{u(!1),g(null),x(!0),o(null);let e=setInterval(y,2e3);return y(),()=>clearInterval(e)},[c]);let y=async()=>{try{let e=await fetch("/api/jobs?jobId=".concat(c));if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let s=await e.json();if(s&&s.success&&s.data){let e=s.data;null===p&&g(e.status),o(e),x(!1),"completed"!==e.status&&"failed"!==e.status||null===p||"completed"===p||"failed"===p||h||(u(!0),setTimeout(()=>{n()},2e3))}else console.error("获取任务状态失败:",(null==s?void 0:s.error)||"响应格式错误")}catch(e){console.error("获取任务状态失败:",e)}};return m?(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,s.jsxs)("div",{className:"text-center py-4",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-gray-600",children:"获取任务状态中..."})]})}):d?(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"改写进度"}),("completed"===d.status||"failed"===d.status)&&(0,s.jsxs)("button",{onClick:()=>j(!0),className:"flex items-center gap-1 px-3 py-1 text-sm bg-blue-100 hover:bg-blue-200 text-blue-700 rounded transition-colors",children:[(0,s.jsx)(S,{className:"w-4 h-4"}),"诊断"]})]}),(0,s.jsx)("div",{className:"p-4 rounded-lg border ".concat((e=>{switch(e){case"pending":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"processing":return"text-blue-600 bg-blue-50 border-blue-200";case"completed":return"text-green-600 bg-green-50 border-green-200";case"failed":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}})(d.status)," mb-4"),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(e=>{switch(e){case"pending":return(0,s.jsx)(k,{className:"text-yellow-500",size:20});case"processing":return(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"});case"completed":return(0,s.jsx)(N.CheckCircle,{className:"text-green-500",size:20});case"failed":return(0,s.jsx)(w.XCircle,{className:"text-red-500",size:20});default:return(0,s.jsx)(C,{className:"text-gray-500",size:20})}})(d.status),(0,s.jsx)("span",{className:"ml-2 font-medium",children:(e=>{switch(e){case"pending":return"等待处理";case"processing":return"正在改写";case"completed":return"改写完成";case"failed":return"改写失败";default:return"未知状态"}})(d.status)})]}),(0,s.jsxs)("span",{className:"text-sm",children:[d.progress,"%"]})]})}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-1",children:[(0,s.jsx)("span",{children:"进度"}),(0,s.jsxs)("span",{children:[d.progress,"%"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat("completed"===d.status?"bg-green-500":"failed"===d.status?"bg-red-500":"bg-blue-500"),style:{width:"".concat(d.progress,"%")}})})]}),d.details&&(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,s.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"章节统计"}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,s.jsxs)("div",{children:["总章节: ",d.details.totalChapters]}),(0,s.jsxs)("div",{children:["已完成: ",d.details.completedChapters]}),(0,s.jsxs)("div",{children:["失败: ",d.details.failedChapters]}),(0,s.jsxs)("div",{children:["剩余: ",d.details.totalChapters-d.details.completedChapters-d.details.failedChapters]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"性能统计"}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,s.jsxs)("div",{children:["总耗时: ",Math.round(d.details.totalProcessingTime/1e3),"秒"]}),(0,s.jsxs)("div",{children:["平均每章: ",Math.round(d.details.averageTimePerChapter/1e3),"秒"]}),(0,s.jsxs)("div",{children:["Token消耗: ",d.details.totalTokensUsed.toLocaleString()]}),(0,s.jsxs)("div",{children:["模型: ",d.details.model]})]})]})]}),(null==(a=d.details)?void 0:a.apiKeyStats)&&d.details.apiKeyStats.length>0&&(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"API Key 使用状态"}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2",children:d.details.apiKeyStats.map((e,t)=>(0,s.jsxs)("div",{className:"p-2 rounded border text-xs ".concat(e.isAvailable?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:[(0,s.jsx)("div",{className:"font-medium",children:e.name}),(0,s.jsxs)("div",{children:["权重: ",e.weight,"x"]}),(0,s.jsxs)("div",{children:["使用次数: ",e.requestCount]}),(0,s.jsx)("div",{className:e.isAvailable?"text-green-600":"text-red-600",children:e.isAvailable?"可用":"冷却中"}),e.cooldownRemaining&&e.cooldownRemaining>0&&(0,s.jsxs)("div",{className:"text-red-500",children:["冷却: ",Math.round(e.cooldownRemaining/1e3),"s"]})]},t))})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500 space-y-1",children:[(0,s.jsxs)("div",{children:["开始时间: ",new Date(d.createdAt).toLocaleString()]}),(0,s.jsxs)("div",{children:["更新时间: ",new Date(d.updatedAt).toLocaleString()]}),(null==(l=d.details)?void 0:l.totalProcessingTime)&&"completed"===d.status&&(0,s.jsxs)("div",{children:["总耗时: ",Math.round(d.details.totalProcessingTime/1e3)," 秒"]})]}),d.result&&(0,s.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"结果信息"}),(0,s.jsx)("p",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:d.result})]}),"completed"===d.status&&(0,s.jsx)("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center text-green-700",children:[(0,s.jsx)(N.CheckCircle,{className:"mr-2",size:16}),(0,s.jsx)("span",{className:"text-sm",children:"改写完成！改写后的文件已保存到 data/rewritten 目录中。"})]})}),"failed"===d.status&&(0,s.jsx)("div",{className:"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center text-red-700",children:[(0,s.jsx)(w.XCircle,{className:"mr-2",size:16}),(0,s.jsx)("span",{className:"text-sm",children:"改写失败，请检查错误信息并重试。"})]})}),"processing"===d.status&&(0,s.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,s.jsxs)("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center text-blue-700",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"}),(0,s.jsx)("span",{className:"text-sm",children:"正在使用 Gemini AI 改写章节，请耐心等待..."})]}),d.details&&(0,s.jsxs)("div",{className:"mt-2 text-xs text-blue-600",children:["并发数: ",d.details.concurrency," | 模型: ",d.details.model]})]}),(null==(r=d.details)?void 0:r.chapterResults)&&d.details.chapterResults.length>0&&(0,s.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"最近完成的章节"}),(0,s.jsx)("div",{className:"max-h-32 overflow-y-auto space-y-1",children:d.details.chapterResults.filter(e=>e&&e.completedAt).sort((e,s)=>new Date(s.completedAt).getTime()-new Date(e.completedAt).getTime()).slice(0,5).map((e,t)=>(0,s.jsxs)("div",{className:"text-xs p-2 rounded ".concat(e.success?"bg-green-100 text-green-700":"bg-red-100 text-red-700"),children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("span",{children:["第",e.chapterNumber,"章: ",e.chapterTitle]}),(0,s.jsx)("span",{children:e.success?"✓":"✗"})]}),(0,s.jsxs)("div",{className:"flex justify-between text-xs opacity-75",children:[(0,s.jsx)("span",{children:e.apiKeyUsed}),(0,s.jsx)("span",{children:e.processingTime?Math.round(e.processingTime/1e3)+"s":""}),(0,s.jsx)("span",{children:e.tokensUsed?e.tokensUsed+" tokens":""})]}),e.error&&(0,s.jsx)("div",{className:"text-red-600 text-xs mt-1",children:e.error})]},t))})]})]}),"completed"===d.status&&(null==(i=d.details)?void 0:i.chapterResults)&&(0,s.jsx)(M,{jobId:d.id,failedChapters:d.details.chapterResults.filter(e=>e&&!e.success).map(e=>({chapterNumber:e.chapterNumber,chapterTitle:e.chapterTitle,error:e.error,apiKeyUsed:e.apiKeyUsed,processingTime:e.processingTime,detailedError:e.detailedError})),rules:"",model:d.details.model,onRetryStart:()=>{console.log("开始重试失败章节")},onRetryComplete:(e,s)=>{console.log("重试完成:",e,s),e&&fetchJobStatus()}}),(0,s.jsx)(z,{jobId:d.id,isOpen:b,onClose:()=>j(!1)})]}):(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,s.jsxs)("div",{className:"text-center py-4 text-red-600",children:[(0,s.jsx)(w.XCircle,{className:"mx-auto mb-2",size:32}),(0,s.jsx)("p",{children:"无法获取任务状态"})]})})}let I=(0,r.default)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),P=(0,r.default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]]);function q(e){let{onJobSelect:a}=e,[l,r]=(0,t.useState)([]),[c,n]=(0,t.useState)(!0),[d,o]=(0,t.useState)({});(0,t.useEffect)(()=>{m(),x()},[]);let m=async()=>{try{let e=await fetch("/api/jobs"),s=await e.json();if(s.success){let e=s.data.sort((e,s)=>new Date(s.createdAt).getTime()-new Date(e.createdAt).getTime());r(e)}}catch(e){console.error("加载任务历史失败:",e)}finally{n(!1)}},x=async()=>{try{let e=await fetch("/api/novels"),s=await e.json();if(s.success){let e={},t=s.data.novels||[];Array.isArray(t)&&t.forEach(s=>{e[s.id]=s.title}),o(e)}}catch(e){console.error("加载小说列表失败:",e)}},h=async e=>{if(confirm("确定要删除这个任务记录吗？"))try{(await fetch("/api/jobs?jobId=".concat(e),{method:"DELETE"})).ok&&r(l.filter(s=>s.id!==e))}catch(e){console.error("删除任务失败:",e)}};return c?(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-gray-600",children:"加载任务历史中..."})]})}):(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,s.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"任务历史"}),(0,s.jsxs)("button",{onClick:m,className:"flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800",children:[(0,s.jsx)(i,{size:14,className:"mr-1"}),"刷新"]})]})}),(0,s.jsx)("div",{className:"max-h-96 overflow-y-auto",children:0===l.length?(0,s.jsx)("div",{className:"p-6 text-center text-gray-500",children:"暂无任务记录"}):(0,s.jsx)("div",{className:"divide-y divide-gray-200",children:l.map(e=>(0,s.jsx)("div",{className:"p-4 hover:bg-gray-50",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(e=>{switch(e){case"pending":return(0,s.jsx)(k,{className:"text-yellow-500",size:16});case"processing":return(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"});case"completed":return(0,s.jsx)(N.CheckCircle,{className:"text-green-500",size:16});case"failed":return(0,s.jsx)(w.XCircle,{className:"text-red-500",size:16});default:return(0,s.jsx)(C,{className:"text-gray-500",size:16})}})(e.status),(0,s.jsx)("span",{className:"ml-2 font-medium text-gray-800",children:d[e.novelId]||"未知小说"}),(0,s.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded border ".concat((e=>{switch(e){case"pending":return"bg-yellow-50 border-yellow-200";case"processing":return"bg-blue-50 border-blue-200";case"completed":return"bg-green-50 border-green-200";case"failed":return"bg-red-50 border-red-200";default:return"bg-gray-50 border-gray-200"}})(e.status)),children:(e=>{switch(e){case"pending":return"等待中";case"processing":return"处理中";case"completed":return"已完成";case"failed":return"失败";default:return"未知"}})(e.status)})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,s.jsxs)("div",{children:["创建时间: ",new Date(e.createdAt).toLocaleString()]}),e.details&&(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsxs)("span",{children:["章节: ",e.details.completedChapters,"/",e.details.totalChapters]}),e.details.totalTokensUsed>0&&(0,s.jsxs)("span",{children:["Token: ",e.details.totalTokensUsed.toLocaleString()]}),e.details.totalProcessingTime>0&&(0,s.jsxs)("span",{children:["耗时: ",(e=>{let s=Math.round(e/1e3);if(s<60)return"".concat(s,"秒");let t=Math.round(s/60);if(t<60)return"".concat(t,"分钟");let a=Math.round(t/60);return"".concat(a,"小时")})(e.details.totalProcessingTime)]}),e.details.model&&(0,s.jsxs)("span",{children:["模型: ",e.details.model]})]}),"completed"!==e.status&&"failed"!==e.status&&(0,s.jsxs)("div",{children:["进度: ",e.progress,"%"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[a&&(0,s.jsx)("button",{onClick:()=>a(e.id),className:"p-1 text-blue-600 hover:text-blue-800",title:"查看详情",children:(0,s.jsx)(I,{size:16})}),(0,s.jsx)("button",{onClick:()=>h(e.id),className:"p-1 text-red-600 hover:text-red-800",title:"删除记录",children:(0,s.jsx)(P,{size:16})})]})]})},e.id))})})]})}let U=(0,r.default)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);function E(e){let{refreshInterval:a=5e3}=e,[l,r]=(0,t.useState)([]),[c,n]=(0,t.useState)(!0),[d,o]=(0,t.useState)(null);(0,t.useEffect)(()=>{if(m(),a>0){let e=setInterval(m,a);return()=>clearInterval(e)}},[a]);let m=async()=>{try{let e=await fetch("/api/gemini/stats"),s=await e.json();s.success&&(r(s.data),o(new Date))}catch(e){console.error("加载API统计失败:",e)}finally{n(!1)}},x=async()=>{n(!0);try{let a=await fetch("/api/gemini/test"),l=await a.json();if(l.success){var e,s,t;alert("连接测试成功！\n使用的API Key: ".concat(null==(e=l.details)?void 0:e.apiKeyUsed,"\nToken消耗: ").concat(null==(s=l.details)?void 0:s.tokensUsed,"\n处理时间: ").concat(null==(t=l.details)?void 0:t.processingTime,"ms"))}else alert("连接测试失败: ".concat(l.error));await m()}catch(e){alert("连接测试失败: ".concat(e instanceof Error?e.message:"未知错误"))}finally{n(!1)}},h=async()=>{if(confirm("确定要重置所有API Key统计吗？"))try{(await fetch("/api/gemini/reset",{method:"POST"})).ok&&(await m(),alert("统计已重置"))}catch(e){alert("重置失败: ".concat(e instanceof Error?e.message:"未知错误"))}};return c&&0===l.length?(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,s.jsxs)("div",{className:"text-center py-4",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-gray-600",children:"加载API统计中..."})]})}):(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,s.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(U,{className:"mr-2 text-blue-600",size:20}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"API Key 状态"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[d&&(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:["更新于 ",d.toLocaleTimeString()]}),(0,s.jsxs)("button",{onClick:m,disabled:c,className:"flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50",children:[(0,s.jsx)(i,{size:14,className:"mr-1 ".concat(c?"animate-spin":"")}),"刷新"]})]})]})}),(0,s.jsxs)("div",{className:"p-6",children:[0===l.length?(0,s.jsx)("div",{className:"text-center text-gray-500 py-4",children:"暂无API Key统计数据"}):(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 p-3 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(S,{className:"text-blue-600 mr-2",size:16}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm text-blue-600",children:"总请求数"}),(0,s.jsx)("div",{className:"text-lg font-semibold text-blue-800",children:l.reduce((e,s)=>e+s.requestCount,0)})]})]})}),(0,s.jsx)("div",{className:"bg-green-50 p-3 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(N.CheckCircle,{className:"text-green-600 mr-2",size:16}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm text-green-600",children:"可用Key"}),(0,s.jsx)("div",{className:"text-lg font-semibold text-green-800",children:l.filter(e=>e.isAvailable).length})]})]})}),(0,s.jsx)("div",{className:"bg-red-50 p-3 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(w.XCircle,{className:"text-red-600 mr-2",size:16}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm text-red-600",children:"冷却中"}),(0,s.jsx)("div",{className:"text-lg font-semibold text-red-800",children:l.filter(e=>!e.isAvailable).length})]})]})}),(0,s.jsx)("div",{className:"bg-purple-50 p-3 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(U,{className:"text-purple-600 mr-2",size:16}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm text-purple-600",children:"总权重"}),(0,s.jsxs)("div",{className:"text-lg font-semibold text-purple-800",children:[l.reduce((e,s)=>e+s.weight,0),"x"]})]})]})})]}),(0,s.jsx)("div",{className:"space-y-3",children:l.map((e,t)=>(0,s.jsx)("div",{className:"p-4 rounded-lg border ".concat(e.isAvailable?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full mr-3 ".concat(e.isAvailable?"bg-green-500":"bg-red-500")}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-gray-800",children:e.name}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:["权重: ",e.weight,"x | 使用次数: ",e.requestCount]})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("div",{className:"text-sm font-medium ".concat(e.isAvailable?"text-green-600":"text-red-600"),children:e.isAvailable?"可用":"冷却中"}),!e.isAvailable&&e.cooldownRemaining&&e.cooldownRemaining>0&&(0,s.jsxs)("div",{className:"text-xs text-red-500 flex items-center",children:[(0,s.jsx)(k,{size:12,className:"mr-1"}),(e=>{let s=Math.ceil(e/1e3);if(s<60)return"".concat(s,"秒");let t=Math.ceil(s/60);return"".concat(t,"分钟")})(e.cooldownRemaining)]})]})]})},t))})]}),(0,s.jsxs)("div",{className:"flex justify-center space-x-4 mt-6 pt-4 border-t border-gray-200",children:[(0,s.jsx)("button",{onClick:x,disabled:c,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:c?"测试中...":"测试连接"}),(0,s.jsx)("button",{onClick:h,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700",children:"重置统计"})]})]})]})}let K=(0,r.default)("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);function O(e){let{currentJobId:a,onJobComplete:l}=e,[r,i]=(0,t.useState)(a||null),[c,n]=(0,t.useState)(a?"current":"history"),d=[{id:"current",label:"当前任务",icon:S},{id:"history",label:"任务历史",icon:K},{id:"stats",label:"API状态",icon:U}];return(0,s.jsxs)("div",{className:"w-full max-w-6xl mx-auto",children:[(0,s.jsx)("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6",children:d.map(e=>{let t=e.icon;return(0,s.jsxs)("button",{onClick:()=>n(e.id),className:"flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors flex-1 justify-center ".concat(c===e.id?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-800"),children:[(0,s.jsx)(t,{className:"mr-2",size:16}),e.label]},e.id)})}),(0,s.jsxs)("div",{className:"mt-6",children:["current"===c&&(0,s.jsx)("div",{children:r?(0,s.jsx)(T,{jobId:r,onComplete:()=>{l&&l(),setTimeout(()=>{n("history")},2e3)}}):(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[(0,s.jsx)(I,{className:"mx-auto mb-4 text-gray-400",size:48}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-2",children:"没有正在进行的任务"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"当前没有正在执行的改写任务。你可以："}),(0,s.jsxs)("div",{className:"space-y-2 text-sm text-gray-500",children:[(0,s.jsx)("p",{children:"• 从任务历史中选择一个任务查看详情"}),(0,s.jsx)("p",{children:"• 创建新的改写任务"}),(0,s.jsx)("p",{children:"• 查看API Key使用状态"})]})]})}),"history"===c&&(0,s.jsx)(q,{onJobSelect:e=>{i(e),n("current")}}),"stats"===c&&(0,s.jsx)(E,{refreshInterval:5e3})]})]})}let H=(0,r.default)("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]]),D=(0,r.default)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),R=[{id:"gemini-2.5-flash-lite",name:"Gemini 2.5 Flash Lite",description:"快速、轻量级模型，适合大批量处理",speed:"fast",quality:"good"},{id:"gemini-2.5-flash",name:"Gemini 2.5 Flash",description:"平衡速度和质量的标准模型",speed:"medium",quality:"excellent"},{id:"gemini-2.5-pro",name:"Gemini 2.5 Pro",description:"高质量模型，处理复杂内容",speed:"slow",quality:"premium"}],L=[{value:1,label:"1 (最安全)",description:"单线程处理，最稳定"},{value:2,label:"2 (保守)",description:"低并发，适合API限制严格的情况"},{value:3,label:"3 (推荐)",description:"默认设置，平衡速度和稳定性"},{value:4,label:"4 (积极)",description:"较高并发，需要充足的API配额"},{value:5,label:"5 (激进)",description:"高并发，适合API配额充足的情况"},{value:6,label:"6 (极限)",description:"最高并发，可能触发API限制"}];function X(e){let{selectedModel:a,selectedConcurrency:l,onModelChange:r,onConcurrencyChange:i,disabled:c=!1}=e,[n,d]=(0,t.useState)(!1),o=R.find(e=>e.id===a);return L.find(e=>e.value===l),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold text-gray-800 flex items-center",children:[(0,s.jsx)(x,{className:"mr-2",size:18}),"模型配置"]}),(0,s.jsx)("button",{onClick:()=>d(!n),disabled:c,className:"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50",title:n?"收起配置":"展开配置",children:(0,s.jsx)(D,{className:"transition-transform ".concat(n?"rotate-180":""),size:16})})]}),!n&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"当前模型:"}),(0,s.jsx)("span",{className:"font-medium",children:(null==o?void 0:o.name)||a})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"并发数:"}),(0,s.jsx)("span",{className:"font-medium",children:l})]})]}),n&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,s.jsx)(H,{className:"inline mr-1",size:14}),"AI 模型"]}),(0,s.jsx)("div",{className:"space-y-2",children:R.map(e=>(0,s.jsxs)("label",{className:"flex items-start space-x-3 cursor-pointer",children:[(0,s.jsx)("input",{type:"radio",name:"model",value:e.id,checked:a===e.id,onChange:e=>r(e.target.value),disabled:c,className:"mt-1"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-medium text-gray-800",children:e.name}),(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat((e=>{switch(e){case"fast":return"text-green-600 bg-green-100";case"medium":return"text-yellow-600 bg-yellow-100";case"slow":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(e.speed)),children:"fast"===e.speed?"快速":"medium"===e.speed?"中等":"慢速"}),(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat((e=>{switch(e){case"good":return"text-blue-600 bg-blue-100";case"excellent":return"text-purple-600 bg-purple-100";case"premium":return"text-indigo-600 bg-indigo-100";default:return"text-gray-600 bg-gray-100"}})(e.quality)),children:"good"===e.quality?"良好":"excellent"===e.quality?"优秀":"顶级"})]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description})]})]},e.id))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,s.jsx)(D,{className:"inline mr-1",size:14}),"并发数量"]}),(0,s.jsx)("div",{className:"space-y-2",children:L.map(e=>(0,s.jsxs)("label",{className:"flex items-start space-x-3 cursor-pointer",children:[(0,s.jsx)("input",{type:"radio",name:"concurrency",value:e.value,checked:l===e.value,onChange:e=>i(parseInt(e.target.value)),disabled:c,className:"mt-1"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-medium text-gray-800",children:e.label}),3===e.value&&(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-600",children:"推荐"})]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description})]})]},e.value))})]}),(0,s.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"text-yellow-600 mr-2",children:"⚠️"}),(0,s.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,s.jsx)("p",{className:"font-medium mb-1",children:"配置建议:"}),(0,s.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,s.jsx)("li",{children:'• 首次使用建议选择 "Gemini 2.5 Flash Lite" + 并发数 3'}),(0,s.jsx)("li",{children:"• 如果遇到 429 错误，请降低并发数"}),(0,s.jsx)("li",{children:"• API 配额充足时可以提高并发数以加快处理速度"}),(0,s.jsx)("li",{children:'• 高质量要求的内容建议使用 "Gemini 1.5 Pro"'})]})]})]})})]})]})}function B(e){let{message:a,type:l,onClose:r,duration:i=3e3}=e;return(0,t.useEffect)(()=>{if(i>0){let e=setTimeout(r,i);return()=>clearTimeout(e)}},[i,r]),(0,s.jsx)("div",{className:"fixed top-4 right-4 z-50 max-w-sm w-full ".concat((()=>{switch(l){case"success":return"bg-green-50 border-green-200";case"error":return"bg-red-50 border-red-200";case"info":return"bg-blue-50 border-blue-200"}})()," border rounded-lg shadow-lg p-4 animate-in slide-in-from-right-full duration-300"),children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(()=>{switch(l){case"success":return(0,s.jsx)(N.CheckCircle,{className:"text-green-500",size:20});case"error":return(0,s.jsx)(w.XCircle,{className:"text-red-500",size:20});case"info":return(0,s.jsx)(C,{className:"text-blue-500",size:20})}})(),(0,s.jsx)("div",{className:"flex-1 text-sm text-gray-800",children:a}),(0,s.jsx)("button",{onClick:r,className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)(v,{size:16})})]})})}let J=(0,r.default)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);function F(){let[e,l]=(0,t.useState)(null),[r,i]=(0,t.useState)(""),[c,d]=(0,t.useState)(""),[o,x]=(0,t.useState)([]),[h,u]=(0,t.useState)(!1),[p,g]=(0,t.useState)(null),[j,y]=(0,t.useState)(!1),[v,N]=(0,t.useState)("gemini-2.5-flash"),[w,k]=(0,t.useState)(4),[C,S]=(0,t.useState)({show:!1,message:"",type:"info"}),M=(e,s)=>{S({show:!0,message:e,type:s})},A=async e=>{let s=prompt("请输入预设名称:");if(!s)return;let t=prompt("请输入预设描述 (可选):")||"";try{let a=await fetch("/api/presets",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:s,description:t,rules:e})}),l=await a.json();l.success?M("规则已保存到预设","success"):M("保存失败: ".concat(l.error),"error")}catch(e){console.error("保存预设失败:",e),M("保存预设失败","error")}},z=async()=>{if(!e||!r||!c)return void M("请完整填写所有信息","error");u(!0);try{let s=c;if(o.length>0){let e=o.map(e=>"".concat(e.name,"(").concat(e.role).concat(e.description?": "+e.description:"",")")).join("、");s="人物设定：".concat(e,"\n\n").concat(c)}let t=await fetch("/api/rewrite",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({novelId:e.id,chapterRange:r,rules:s,model:v,concurrency:w})});if(!t.ok)throw Error("HTTP error! status: ".concat(t.status));let a=await t.json();a&&a.success?(g(a.data.jobId),M("改写任务已开始","info")):(M("改写失败: ".concat((null==a?void 0:a.error)||"未知错误"),"error"),u(!1))}catch(e){console.error("改写请求失败:",e),M("改写请求失败，请检查网络连接","error"),u(!1)}},I=()=>{u(!1),g(null),M("改写完成！","success")};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsxs)("div",{className:"container mx-auto px-4 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:"小说改写工具"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("button",{onClick:()=>y(!j),className:"flex items-center px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,s.jsxs)("svg",{className:"mr-1",width:18,height:18,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,s.jsx)("rect",{x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"}),(0,s.jsx)("path",{d:"M9 9h6v6H9z"})]}),"任务管理"]}),(0,s.jsx)("button",{onClick:z,disabled:h||!e||!r||!c,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-6 rounded-lg transition-colors",title:e?r?c?"开始改写任务":"请输入改写规则":"请选择章节范围":"请先选择小说",children:h?"改写中...":"开始改写"}),(0,s.jsxs)(a.default,{href:"/merge",className:"flex items-center px-3 py-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors",children:[(0,s.jsxs)("svg",{className:"mr-1",width:18,height:18,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,s.jsx)("path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}),(0,s.jsx)("polyline",{points:"7,10 12,15 17,10"}),(0,s.jsx)("line",{x1:"12",y1:"15",x2:"12",y2:"3"})]}),"合并章节"]}),(0,s.jsxs)(a.default,{href:"/help",className:"flex items-center px-3 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors",children:[(0,s.jsx)(J,{className:"mr-1",size:18}),"帮助"]})]})]}),j&&(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(O,{currentJobId:p||void 0,onJobComplete:I})}),h&&p&&!j&&(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsx)(T,{jobId:p,onComplete:I})}),!j&&(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-4",children:[(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsx)(b,{rules:c,onRulesChange:d,onSaveToPreset:A,disabled:h})}),(0,s.jsxs)("div",{className:"lg:col-span-1 space-y-4",children:[(0,s.jsx)(n,{selectedNovel:e,onNovelSelect:l,disabled:h}),(0,s.jsx)(f,{novelId:null==e?void 0:e.id,characters:o,onCharactersChange:x,disabled:h})]}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsx)(m,{novel:e,selectedChapters:r,onChaptersChange:i,disabled:h})}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsx)(X,{selectedModel:v,selectedConcurrency:w,onModelChange:N,onConcurrencyChange:k,disabled:h})})]})]}),C.show&&(0,s.jsx)(B,{message:C.message,type:C.type,onClose:()=>{S({show:!1,message:"",type:"info"})}})]})}}]);