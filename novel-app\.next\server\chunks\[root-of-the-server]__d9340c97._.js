module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},22734,(e,t,r)=>{t.exports=e.x("fs",()=>require("fs"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},84168,e=>{"use strict";e.s(["chapterContextDb",()=>A,"chapterDb",()=>I,"characterDb",()=>y,"jobDb",()=>w,"novelContextDb",()=>m,"novelDb",()=>v,"presetDb",()=>S,"ruleDb",()=>R]);var t=e.i(22734),r=e.i(14747),n=e.i(54799);let a=r.default.join(process.cwd(),"data"),s=r.default.join(a,"novels.json"),o=r.default.join(a,"chapters.json"),i=r.default.join(a,"rewrite_rules.json"),d=r.default.join(a,"rewrite_jobs.json"),l=r.default.join(a,"characters.json"),u=r.default.join(a,"presets.json"),c=r.default.join(a,"novel-contexts.json"),p=r.default.join(a,"chapter-contexts.json");function x(){t.default.existsSync(a)||t.default.mkdirSync(a,{recursive:!0})}function f(e){if(x(),!t.default.existsSync(e))return[];try{let r=t.default.readFileSync(e,"utf-8");return JSON.parse(r)}catch(t){return console.error(`Error reading ${e}:`,t),[]}}function g(e,r){x();try{t.default.writeFileSync(e,JSON.stringify(r,null,2),"utf-8")}catch(t){throw console.error(`Error writing ${e}:`,t),t}}function h(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}let v={getAll:()=>f(s),getById:e=>f(s).find(t=>t.id===e),create:e=>{var t;let r=f(s),a=(t=e.title,n.default.createHash("md5").update(t).digest("hex").substring(0,18)),o=r.find(e=>e.id===a);if(o)return o.filename=e.filename,o.chapterCount=e.chapterCount,g(s,r),o;let i={...e,id:a,createdAt:new Date().toISOString()};return r.push(i),g(s,r),i},update:(e,t)=>{let r=f(s),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t},g(s,r),r[n])},delete:e=>{let t=f(s),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(s,t),!0)}},I={getAll:()=>f(o),getByNovelId:e=>f(o).filter(t=>t.novelId===e),getById:e=>f(o).find(t=>t.id===e),create:e=>{let t=f(o),r={...e,id:h(),createdAt:new Date().toISOString()};return t.push(r),g(o,t),r},createBatch:e=>{let t=f(o),r=e.map(e=>({...e,id:h(),createdAt:new Date().toISOString()}));return t.push(...r),g(o,t),r},delete:e=>{let t=f(o),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(o,t),!0)},deleteByNovelId:e=>{let t=f(o).filter(t=>t.novelId!==e);return g(o,t),!0}},R={getAll:()=>f(i),getById:e=>f(i).find(t=>t.id===e),create:e=>{let t=f(i),r={...e,id:h(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(i,t),r},update:(e,t)=>{let r=f(i),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(i,r),r[n])},delete:e=>{let t=f(i),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(i,t),!0)}},w={getAll:()=>f(d),getById:e=>f(d).find(t=>t.id===e),create:e=>{let t=f(d),r={...e,id:h(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(d,t),r},update:(e,t)=>{let r=f(d),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(d,r),r[n])},delete:e=>{let t=f(d),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(d,t),!0)}},y={getAll:()=>f(l),getByNovelId:e=>f(l).filter(t=>t.novelId===e),getById:e=>f(l).find(t=>t.id===e),create:e=>{let t=f(l),r={...e,id:h(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(l,t),r},update:(e,t)=>{let r=f(l),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(l,r),r[n])},delete:e=>{let t=f(l),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(l,t),!0)},deleteByNovelId:e=>{let t=f(l).filter(t=>t.novelId!==e);return g(l,t),!0}},S={getAll:()=>f(u),getById:e=>f(u).find(t=>t.id===e),create:e=>{let t=f(u),r={...e,id:h(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(u,t),r},update:(e,t)=>{let r=f(u),n=r.findIndex(t=>t.id===e);return -1===n?null:(r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(u,r),r[n])},delete:e=>{let t=f(u),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(u,t),!0)}},m={getAll:()=>f(c),getByNovelId:e=>f(c).find(t=>t.novelId===e),getById:e=>f(c).find(t=>t.id===e),create:e=>{let t=f(c),r={...e,id:h(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(c,t),r},update:(e,t)=>{let r=f(c),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(c,r),r[n]},delete:e=>{let t=f(c),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(c,t),!0)}},A={getAll:()=>f(p),getByNovelId:e=>f(p).filter(t=>t.novelId===e),getByChapter:(e,t)=>f(p).find(r=>r.novelId===e&&r.chapterNumber===t),getById:e=>f(p).find(t=>t.id===e),create:e=>{let t=f(p),r={...e,id:h(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return t.push(r),g(p,t),r},update:(e,t)=>{let r=f(p),n=r.findIndex(t=>t.id===e);if(-1!==n)return r[n]={...r[n],...t,updatedAt:new Date().toISOString()},g(p,r),r[n]},delete:e=>{let t=f(p),r=t.findIndex(t=>t.id===e);return -1!==r&&(t.splice(r,1),g(p,t),!0)},getContextWindow:(e,t,r=2)=>{let n=f(p).filter(t=>t.novelId===e),a=Math.max(1,t-r),s=t+r;return n.filter(e=>e.chapterNumber>=a&&e.chapterNumber<=s).sort((e,t)=>e.chapterNumber-t.chapterNumber)}}},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54166,(e,t,r)=>{},27029,e=>{"use strict";e.s(["handler",()=>b,"patchFetch",()=>D,"routeModule",()=>m,"serverHooks",()=>N,"workAsyncStorage",()=>A,"workUnitAsyncStorage",()=>j],27029);var t=e.i(47909),r=e.i(74017),n=e.i(96250),a=e.i(59756),s=e.i(61916),o=e.i(69741),i=e.i(16795),d=e.i(87718),l=e.i(95169),u=e.i(47587),c=e.i(66012),p=e.i(70101),x=e.i(26937),f=e.i(10372),g=e.i(93695);e.i(52474);var h=e.i(220);e.s(["DELETE",()=>y,"GET",()=>R,"PUT",()=>w],63577);var v=e.i(89171),I=e.i(84168);async function R(e){try{let{searchParams:t}=new URL(e.url),r=t.get("novelId");if(!r)return v.NextResponse.json({success:!1,error:"小说ID不能为空"},{status:400});let n=I.novelContextDb.getByNovelId(r);if(!n)return v.NextResponse.json({success:!1,error:"未找到小说上下文，请先进行分析"},{status:404});return v.NextResponse.json({success:!0,data:n})}catch(e){return console.error("获取小说上下文失败:",e),v.NextResponse.json({success:!1,error:"获取小说上下文失败"},{status:500})}}async function w(e){try{let{novelId:t,...r}=await e.json();if(!t)return v.NextResponse.json({success:!1,error:"小说ID不能为空"},{status:400});let n=I.novelContextDb.getByNovelId(t);if(!n)return v.NextResponse.json({success:!1,error:"未找到小说上下文"},{status:404});let a=I.novelContextDb.update(n.id,r);if(!a)return v.NextResponse.json({success:!1,error:"更新失败"},{status:500});return v.NextResponse.json({success:!0,data:a,message:"小说上下文更新成功"})}catch(e){return console.error("更新小说上下文失败:",e),v.NextResponse.json({success:!1,error:"更新小说上下文失败"},{status:500})}}async function y(e){try{let{searchParams:t}=new URL(e.url),r=t.get("novelId");if(!r)return v.NextResponse.json({success:!1,error:"小说ID不能为空"},{status:400});let n=I.novelContextDb.getByNovelId(r);if(!n)return v.NextResponse.json({success:!1,error:"未找到小说上下文"},{status:404});if(!I.novelContextDb.delete(n.id))return v.NextResponse.json({success:!1,error:"删除失败"},{status:500});return v.NextResponse.json({success:!0,message:"小说上下文删除成功"})}catch(e){return console.error("删除小说上下文失败:",e),v.NextResponse.json({success:!1,error:"删除小说上下文失败"},{status:500})}}var S=e.i(63577);let m=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/context/novel/route",pathname:"/api/context/novel",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/context/novel/route.ts",nextConfigOutput:"",userland:S}),{workAsyncStorage:A,workUnitAsyncStorage:j,serverHooks:N}=m;function D(){return(0,n.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:j})}async function b(e,t,n){var v;let I="/api/context/novel/route";I=I.replace(/\/index$/,"")||"/";let R=await m.prepare(e,t,{srcPage:I,multiZoneDraftMode:!1});if(!R)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:S,isDraftMode:A,prerenderManifest:j,routerServerContext:N,isOnDemandRevalidate:D,revalidateOnlyGenerated:b,resolvedPathname:C}=R,E=(0,o.normalizeAppPath)(I),O=!!(j.dynamicRoutes[E]||j.routes[C]);if(O&&!A){let e=!!j.routes[C],t=j.dynamicRoutes[E];if(t&&!1===t.fallback&&!e)throw new g.NoFallbackError}let q=null;!O||m.isDev||A||(q="/index"===(q=C)?"/":q);let T=!0===m.isDev||!O,B=O&&!T,P=e.method||"GET",k=(0,s.getTracer)(),_=k.getActiveScopeSpan(),U={params:y,prerenderManifest:j,renderOpts:{experimental:{cacheComponents:!!S.experimental.cacheComponents,authInterrupts:!!S.experimental.authInterrupts},supportsDynamicResponse:T,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=S.experimental)?void 0:v.cacheLife,isRevalidate:B,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>m.onRequestError(e,t,n,N)},sharedContext:{buildId:w}},H=new i.NodeNextRequest(e),M=new i.NodeNextResponse(t),$=d.NextRequestAdapter.fromNodeNextRequest(H,(0,d.signalFromNodeResponse)(t));try{let o=async r=>m.handle($,U).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=k.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${P} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${P} ${e.url}`)}),i=async s=>{var i,d;let l=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&D&&b&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(s);e.fetchMetrics=U.renderOpts.fetchMetrics;let d=U.renderOpts.pendingWaitUntil;d&&n.waitUntil&&(n.waitUntil(d),d=void 0);let l=U.renderOpts.collectedTags;if(!O)return await (0,c.sendResponse)(H,M,i,U.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(i.headers);l&&(t[f.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==U.renderOpts.collectedRevalidate&&!(U.renderOpts.collectedRevalidate>=f.INFINITE_CACHE)&&U.renderOpts.collectedRevalidate,n=void 0===U.renderOpts.collectedExpire||U.renderOpts.collectedExpire>=f.INFINITE_CACHE?void 0:U.renderOpts.collectedExpire;return{value:{kind:h.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await m.onRequestError(e,t,{routerKind:"App Router",routePath:I,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:B,isOnDemandRevalidate:D})},N),t}},g=await m.handleResponse({req:e,nextConfig:S,cacheKey:q,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:j,isRoutePPREnabled:!1,isOnDemandRevalidate:D,revalidateOnlyGenerated:b,responseGenerator:l,waitUntil:n.waitUntil});if(!O)return null;if((null==g||null==(i=g.value)?void 0:i.kind)!==h.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==g||null==(d=g.value)?void 0:d.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",D?"REVALIDATED":g.isMiss?"MISS":g.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,p.fromNodeOutgoingHttpHeaders)(g.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&O||v.delete(f.NEXT_CACHE_TAGS_HEADER),!g.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,x.getCacheControlHeader)(g.cacheControl)),await (0,c.sendResponse)(H,M,new Response(g.value.body,{headers:v,status:g.value.status||200})),null};_?await i(_):await k.withPropagatedContext(e.headers,()=>k.trace(l.BaseServerSpan.handleRequest,{spanName:`${P} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":P,"http.target":e.url}},i))}catch(t){if(_||t instanceof g.NoFallbackError||await m.onRequestError(e,t,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:B,isOnDemandRevalidate:D})}),O)throw t;return await (0,c.sendResponse)(H,M,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__d9340c97._.js.map