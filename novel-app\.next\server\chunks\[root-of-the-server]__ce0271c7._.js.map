{"version": 3, "sources": ["turbopack:///[project]/src/lib/database.ts", "turbopack:///[project]/src/app/api/context/chapter/route.ts", "turbopack:///[project]/node_modules/next/dist/esm/build/templates/app-route.js"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport crypto from 'crypto';\n\n// 数据类型定义\nexport interface Novel {\n  id: string;\n  title: string;\n  filename: string;\n  createdAt: string;\n  chapterCount?: number;\n}\n\nexport interface Chapter {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  title: string;\n  content: string;\n  filename: string;\n  createdAt: string;\n}\n\nexport interface RewriteRule {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Character {\n  id: string;\n  novelId: string;\n  name: string;\n  role: string; // 角色类型：男主、女主、配角、反派、其他\n  description: string;\n  personality?: string; // 性格特点\n  appearance?: string; // 外貌描述\n  relationships?: string; // 人物关系\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 小说整体上下文\nexport interface NovelContext {\n  id: string;\n  novelId: string;\n  summary: string; // 小说整体摘要\n  mainCharacters: Array<{\n    name: string;\n    role: string;\n    description: string;\n    relationships?: string;\n  }>; // 主要人物信息\n  worldSetting: string; // 世界观设定\n  writingStyle: string; // 写作风格特征\n  mainPlotlines: string[]; // 主要情节线\n  themes: string[]; // 主题\n  tone: string; // 语调风格\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 章节上下文\nexport interface ChapterContext {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  keyEvents: string[]; // 关键事件\n  characterStates: Array<{\n    name: string;\n    status: string; // 人物在本章的状态\n    emotions: string; // 情感状态\n    relationships: string; // 关系变化\n  }>; // 人物状态\n  plotProgress: string; // 情节推进要点\n  previousChapterSummary?: string; // 前一章摘要\n  nextChapterHints?: string; // 对下一章的暗示\n  contextualNotes: string; // 上下文注释\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Preset {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface RewriteJob {\n  id: string;\n  novelId: string;\n  chapters: number[];\n  ruleId: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n  // 新增详细信息字段\n  details?: {\n    totalChapters: number;\n    completedChapters: number;\n    failedChapters: number;\n    totalTokensUsed: number;\n    totalProcessingTime: number;\n    averageTimePerChapter: number;\n    apiKeyStats: Array<{\n      name: string;\n      requestCount: number;\n      weight: number;\n      isAvailable: boolean;\n    }>;\n    chapterResults: Array<{\n      chapterNumber: number;\n      chapterTitle: string;\n      success: boolean;\n      error?: string;\n      apiKeyUsed?: string;\n      tokensUsed?: number;\n      processingTime?: number;\n      completedAt?: string;\n    }>;\n    model?: string;\n    concurrency?: number;\n  };\n}\n\n// 数据存储路径\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst NOVELS_FILE = path.join(DATA_DIR, 'novels.json');\nconst CHAPTERS_FILE = path.join(DATA_DIR, 'chapters.json');\nconst RULES_FILE = path.join(DATA_DIR, 'rewrite_rules.json');\nconst JOBS_FILE = path.join(DATA_DIR, 'rewrite_jobs.json');\nconst CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');\nconst PRESETS_FILE = path.join(DATA_DIR, 'presets.json');\nconst NOVEL_CONTEXTS_FILE = path.join(DATA_DIR, 'novel-contexts.json');\nconst CHAPTER_CONTEXTS_FILE = path.join(DATA_DIR, 'chapter-contexts.json');\n\n// 确保数据目录存在\nfunction ensureDataDir() {\n  if (!fs.existsSync(DATA_DIR)) {\n    fs.mkdirSync(DATA_DIR, { recursive: true });\n  }\n}\n\n// 读取JSON文件\nfunction readJsonFile<T>(filePath: string): T[] {\n  ensureDataDir();\n  if (!fs.existsSync(filePath)) {\n    return [];\n  }\n  try {\n    const data = fs.readFileSync(filePath, 'utf-8');\n    return JSON.parse(data);\n  } catch (error) {\n    console.error(`Error reading ${filePath}:`, error);\n    return [];\n  }\n}\n\n// 写入JSON文件\nfunction writeJsonFile<T>(filePath: string, data: T[]) {\n  ensureDataDir();\n  try {\n    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');\n  } catch (error) {\n    console.error(`Error writing ${filePath}:`, error);\n    throw error;\n  }\n}\n\n// 生成唯一ID\nfunction generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substring(2);\n}\n\n// 基于内容生成确定性ID\nfunction generateDeterministicId(content: string): string {\n  return crypto.createHash('md5').update(content).digest('hex').substring(0, 18);\n}\n\n// 小说相关操作\nexport const novelDb = {\n  getAll: (): Novel[] => readJsonFile<Novel>(NOVELS_FILE),\n\n  getById: (id: string): Novel | undefined => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    return novels.find(novel => novel.id === id);\n  },\n\n  create: (novel: Omit<Novel, 'id' | 'createdAt'>): Novel => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n\n    // 使用书名生成确定性ID\n    const novelId = generateDeterministicId(novel.title);\n\n    // 检查是否已存在相同ID的小说\n    const existingNovel = novels.find(n => n.id === novelId);\n    if (existingNovel) {\n      // 如果已存在，更新现有记录\n      existingNovel.filename = novel.filename;\n      existingNovel.chapterCount = novel.chapterCount;\n      writeJsonFile(NOVELS_FILE, novels);\n      return existingNovel;\n    }\n\n    const newNovel: Novel = {\n      ...novel,\n      id: novelId,\n      createdAt: new Date().toISOString(),\n    };\n    novels.push(newNovel);\n    writeJsonFile(NOVELS_FILE, novels);\n    return newNovel;\n  },\n\n  update: (id: string, updates: Partial<Novel>): Novel | null => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return null;\n\n    novels[index] = { ...novels[index], ...updates };\n    writeJsonFile(NOVELS_FILE, novels);\n    return novels[index];\n  },\n\n  delete: (id: string): boolean => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return false;\n\n    novels.splice(index, 1);\n    writeJsonFile(NOVELS_FILE, novels);\n    return true;\n  }\n};\n\n// 章节相关操作\nexport const chapterDb = {\n  getAll: (): Chapter[] => readJsonFile<Chapter>(CHAPTERS_FILE),\n\n  getByNovelId: (novelId: string): Chapter[] => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.filter(chapter => chapter.novelId === novelId);\n  },\n\n  getById: (id: string): Chapter | undefined => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.find(chapter => chapter.id === id);\n  },\n\n  create: (chapter: Omit<Chapter, 'id' | 'createdAt'>): Chapter => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapter: Chapter = {\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    chapters.push(newChapter);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return newChapter;\n  },\n\n  createBatch: (chapters: Omit<Chapter, 'id' | 'createdAt'>[]): Chapter[] => {\n    const existingChapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapters = chapters.map(chapter => ({\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    }));\n    existingChapters.push(...newChapters);\n    writeJsonFile(CHAPTERS_FILE, existingChapters);\n    return newChapters;\n  },\n\n  delete: (id: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const index = chapters.findIndex(chapter => chapter.id === id);\n    if (index === -1) return false;\n\n    chapters.splice(index, 1);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const filteredChapters = chapters.filter(chapter => chapter.novelId !== novelId);\n    writeJsonFile(CHAPTERS_FILE, filteredChapters);\n    return true;\n  }\n};\n\n// 改写规则相关操作\nexport const ruleDb = {\n  getAll: (): RewriteRule[] => readJsonFile<RewriteRule>(RULES_FILE),\n\n  getById: (id: string): RewriteRule | undefined => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    return rules.find(rule => rule.id === id);\n  },\n\n  create: (rule: Omit<RewriteRule, 'id' | 'createdAt' | 'updatedAt'>): RewriteRule => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const newRule: RewriteRule = {\n      ...rule,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    rules.push(newRule);\n    writeJsonFile(RULES_FILE, rules);\n    return newRule;\n  },\n\n  update: (id: string, updates: Partial<RewriteRule>): RewriteRule | null => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return null;\n\n    rules[index] = {\n      ...rules[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(RULES_FILE, rules);\n    return rules[index];\n  },\n\n  delete: (id: string): boolean => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return false;\n\n    rules.splice(index, 1);\n    writeJsonFile(RULES_FILE, rules);\n    return true;\n  }\n};\n\n// 改写任务相关操作\nexport const jobDb = {\n  getAll: (): RewriteJob[] => readJsonFile<RewriteJob>(JOBS_FILE),\n\n  getById: (id: string): RewriteJob | undefined => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    return jobs.find(job => job.id === id);\n  },\n\n  create: (job: Omit<RewriteJob, 'id' | 'createdAt' | 'updatedAt'>): RewriteJob => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const newJob: RewriteJob = {\n      ...job,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    jobs.push(newJob);\n    writeJsonFile(JOBS_FILE, jobs);\n    return newJob;\n  },\n\n  update: (id: string, updates: Partial<RewriteJob>): RewriteJob | null => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return null;\n\n    jobs[index] = {\n      ...jobs[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(JOBS_FILE, jobs);\n    return jobs[index];\n  },\n\n  delete: (id: string): boolean => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return false;\n\n    jobs.splice(index, 1);\n    writeJsonFile(JOBS_FILE, jobs);\n    return true;\n  }\n};\n\n// 人物设定相关操作\nexport const characterDb = {\n  getAll: (): Character[] => readJsonFile<Character>(CHARACTERS_FILE),\n\n  getByNovelId: (novelId: string): Character[] => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.filter(character => character.novelId === novelId);\n  },\n\n  getById: (id: string): Character | undefined => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.find(character => character.id === id);\n  },\n\n  create: (character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>): Character => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const newCharacter: Character = {\n      ...character,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    characters.push(newCharacter);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return newCharacter;\n  },\n\n  update: (id: string, updates: Partial<Character>): Character | null => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return null;\n\n    characters[index] = {\n      ...characters[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return characters[index];\n  },\n\n  delete: (id: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return false;\n\n    characters.splice(index, 1);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const filteredCharacters = characters.filter(character => character.novelId !== novelId);\n    writeJsonFile(CHARACTERS_FILE, filteredCharacters);\n    return true;\n  }\n};\n\n// 预设相关操作\nexport const presetDb = {\n  getAll: (): Preset[] => readJsonFile<Preset>(PRESETS_FILE),\n\n  getById: (id: string): Preset | undefined => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    return presets.find(preset => preset.id === id);\n  },\n\n  create: (preset: Omit<Preset, 'id' | 'createdAt' | 'updatedAt'>): Preset => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const newPreset: Preset = {\n      ...preset,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    presets.push(newPreset);\n    writeJsonFile(PRESETS_FILE, presets);\n    return newPreset;\n  },\n\n  update: (id: string, updates: Partial<Preset>): Preset | null => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const index = presets.findIndex(preset => preset.id === id);\n    if (index === -1) return null;\n\n    presets[index] = {\n      ...presets[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(PRESETS_FILE, presets);\n    return presets[index];\n  },\n\n  delete: (id: string): boolean => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const index = presets.findIndex(preset => preset.id === id);\n    if (index === -1) return false;\n\n    presets.splice(index, 1);\n    writeJsonFile(PRESETS_FILE, presets);\n    return true;\n  }\n};\n\n// 小说上下文相关操作\nexport const novelContextDb = {\n  getAll: (): NovelContext[] => readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE),\n\n  getByNovelId: (novelId: string): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    return contexts.find(context => context.novelId === novelId);\n  },\n\n  getById: (id: string): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    return contexts.find(context => context.id === id);\n  },\n\n  create: (context: Omit<NovelContext, 'id' | 'createdAt' | 'updatedAt'>): NovelContext => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const newContext: NovelContext = {\n      ...context,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    contexts.push(newContext);\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return newContext;\n  },\n\n  update: (id: string, updates: Partial<Omit<NovelContext, 'id' | 'createdAt'>>): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return undefined;\n\n    contexts[index] = {\n      ...contexts[index],\n      ...updates,\n      updatedAt: new Date().toISOString(),\n    };\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return contexts[index];\n  },\n\n  delete: (id: string): boolean => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return false;\n\n    contexts.splice(index, 1);\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return true;\n  }\n};\n\n// 章节上下文相关操作\nexport const chapterContextDb = {\n  getAll: (): ChapterContext[] => readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE),\n\n  getByNovelId: (novelId: string): ChapterContext[] => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.filter(context => context.novelId === novelId);\n  },\n\n  getByChapter: (novelId: string, chapterNumber: number): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.find(context =>\n      context.novelId === novelId && context.chapterNumber === chapterNumber\n    );\n  },\n\n  getById: (id: string): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.find(context => context.id === id);\n  },\n\n  create: (context: Omit<ChapterContext, 'id' | 'createdAt' | 'updatedAt'>): ChapterContext => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const newContext: ChapterContext = {\n      ...context,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    contexts.push(newContext);\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return newContext;\n  },\n\n  update: (id: string, updates: Partial<Omit<ChapterContext, 'id' | 'createdAt'>>): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return undefined;\n\n    contexts[index] = {\n      ...contexts[index],\n      ...updates,\n      updatedAt: new Date().toISOString(),\n    };\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return contexts[index];\n  },\n\n  delete: (id: string): boolean => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return false;\n\n    contexts.splice(index, 1);\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return true;\n  },\n\n  // 获取章节的上下文窗口（前后几章的上下文）\n  getContextWindow: (novelId: string, chapterNumber: number, windowSize: number = 2): ChapterContext[] => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const novelContexts = contexts.filter(context => context.novelId === novelId);\n\n    const startChapter = Math.max(1, chapterNumber - windowSize);\n    const endChapter = chapterNumber + windowSize;\n\n    return novelContexts.filter(context =>\n      context.chapterNumber >= startChapter && context.chapterNumber <= endChapter\n    ).sort((a, b) => a.chapterNumber - b.chapterNumber);\n  }\n};\n", "import { NextRequest, NextResponse } from 'next/server';\nimport { chapterContextDb } from '@/lib/database';\n\n// GET - 获取章节上下文\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const novelId = searchParams.get('novelId');\n    const chapterNumber = searchParams.get('chapterNumber');\n    \n    if (!novelId) {\n      return NextResponse.json(\n        { success: false, error: '小说ID不能为空' },\n        { status: 400 }\n      );\n    }\n    \n    if (chapterNumber) {\n      // 获取特定章节的上下文\n      const context = chapterContextDb.getByChapter(novelId, parseInt(chapterNumber));\n      \n      if (!context) {\n        return NextResponse.json(\n          { success: false, error: '未找到章节上下文' },\n          { status: 404 }\n        );\n      }\n      \n      return NextResponse.json({\n        success: true,\n        data: context,\n      });\n    } else {\n      // 获取小说所有章节的上下文\n      const contexts = chapterContextDb.getByNovelId(novelId);\n      \n      return NextResponse.json({\n        success: true,\n        data: contexts,\n      });\n    }\n  } catch (error) {\n    console.error('获取章节上下文失败:', error);\n    return NextResponse.json(\n      { success: false, error: '获取章节上下文失败' },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT - 更新章节上下文\nexport async function PUT(request: NextRequest) {\n  try {\n    const { novelId, chapterNumber, ...updates } = await request.json();\n    \n    if (!novelId || chapterNumber === undefined) {\n      return NextResponse.json(\n        { success: false, error: '小说ID和章节号不能为空' },\n        { status: 400 }\n      );\n    }\n    \n    const existingContext = chapterContextDb.getByChapter(novelId, chapterNumber);\n    if (!existingContext) {\n      return NextResponse.json(\n        { success: false, error: '未找到章节上下文' },\n        { status: 404 }\n      );\n    }\n    \n    const updatedContext = chapterContextDb.update(existingContext.id, updates);\n    \n    if (!updatedContext) {\n      return NextResponse.json(\n        { success: false, error: '更新失败' },\n        { status: 500 }\n      );\n    }\n    \n    return NextResponse.json({\n      success: true,\n      data: updatedContext,\n      message: '章节上下文更新成功'\n    });\n  } catch (error) {\n    console.error('更新章节上下文失败:', error);\n    return NextResponse.json(\n      { success: false, error: '更新章节上下文失败' },\n      { status: 500 }\n    );\n  }\n}\n\n// DELETE - 删除章节上下文\nexport async function DELETE(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const novelId = searchParams.get('novelId');\n    const chapterNumber = searchParams.get('chapterNumber');\n    \n    if (!novelId || !chapterNumber) {\n      return NextResponse.json(\n        { success: false, error: '小说ID和章节号不能为空' },\n        { status: 400 }\n      );\n    }\n    \n    const existingContext = chapterContextDb.getByChapter(novelId, parseInt(chapterNumber));\n    if (!existingContext) {\n      return NextResponse.json(\n        { success: false, error: '未找到章节上下文' },\n        { status: 404 }\n      );\n    }\n    \n    const deleted = chapterContextDb.delete(existingContext.id);\n    \n    if (!deleted) {\n      return NextResponse.json(\n        { success: false, error: '删除失败' },\n        { status: 500 }\n      );\n    }\n    \n    return NextResponse.json({\n      success: true,\n      message: '章节上下文删除成功'\n    });\n  } catch (error) {\n    console.error('删除章节上下文失败:', error);\n    return NextResponse.json(\n      { success: false, error: '删除章节上下文失败' },\n      { status: 500 }\n    );\n  }\n}\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/context/chapter/route\",\n        pathname: \"/api/context/chapter\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/src/app/api/context/chapter/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/context/chapter/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n"], "names": [], "mappings": "m1CAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAoIA,IAAM,EAAW,EAAA,OAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAI,QACpC,EAAc,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,eAClC,EAAgB,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,iBACpC,EAAa,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,sBACjC,EAAY,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,qBAChC,EAAkB,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,mBACtC,EAAe,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,gBACnC,EAAsB,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,uBAC1C,EAAwB,EAAA,OAAI,CAAC,IAAI,CAAC,EAAU,yBAGlD,SAAS,IACH,AAAC,EAAA,OAAE,CAAC,UAAU,CAAC,IACjB,EAAA,KAD4B,EAC1B,CAAC,SAAS,CAAC,EAAU,CAAE,WAAW,CAAK,EAE7C,CAGA,SAAS,EAAgB,CAAgB,EAEvC,GADA,IACI,CAAC,EAAA,OAAE,CAAC,UAAU,CAAC,GACjB,MAAO,EADqB,AACnB,CAEX,GAAI,CACF,IAAM,EAAO,EAAA,OAAE,CAAC,YAAY,CAAC,EAAU,SACvC,OAAO,KAAK,KAAK,CAAC,EACpB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,EAAS,CAAC,CAAC,CAAE,GACrC,EAAE,AACX,CACF,CAGA,SAAS,EAAiB,CAAgB,CAAE,CAAS,EACnD,IACA,GAAI,CACF,EAAA,OAAE,CAAC,aAAa,CAAC,EAAU,KAAK,SAAS,CAAC,EAAM,KAAM,GAAI,QAC5D,CAAE,MAAO,EAAO,CAEd,MADA,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,EAAS,CAAC,CAAC,CAAE,GACtC,CACR,CACF,CAGA,SAAS,IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,IAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,EACxE,CAQO,IAAM,EAAU,CACrB,OAAQ,IAAe,EAAoB,GAE3C,QAAU,AAAD,GACQ,AACR,EAD4B,GACrB,IAAI,CAAC,GAAS,EAAM,EAAE,GAAK,GAG3C,OAAQ,AAAC,UACP,IAAM,EAAS,EAAoB,GAG7B,GAjBuB,EAiBW,EAAM,GAA9B,AAjB4B,EAiBO,CAhB9C,EAAA,OAAM,CAAC,UAAU,CAAC,OAAO,MAAM,CAAC,GAAS,MAAM,CAAC,OAAO,SAAS,CAAC,EAAG,KAmBnE,EAAgB,EAAO,IAAI,CAAC,GAAK,EAAE,EAAE,GAAK,GAChD,GAAI,EAKF,OAHA,EAAc,IAFG,IAEK,CAAG,EAAM,QAAQ,CACvC,EAAc,YAAY,CAAG,EAAM,YAAY,CAC/C,EAAc,EAAa,GACpB,EAGT,IAAM,EAAkB,CACtB,GAAG,CAAK,CACR,GAAI,EACJ,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAO,IAAI,CAAC,GACZ,EAAc,EAAa,GACpB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAS,EAAoB,GAC7B,EAAQ,EAAO,SAAS,CAAC,GAAS,EAAM,EAAE,GAAK,UACrD,AAAc,CAAC,GAAG,CAAd,EAAqB,MAEzB,CAAM,CAAC,EAAM,CAAG,CAAE,GAAG,CAAM,CAAC,EAAM,CAAE,GAAG,CAAO,AAAC,EAC/C,EAAc,EAAa,GACpB,CAAM,CAAC,EAAM,CACtB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAS,EAAoB,GAC7B,EAAQ,EAAO,SAAS,CAAC,GAAS,EAAM,EAAE,GAAK,UACrD,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAElB,MAAM,CAAC,EAAO,GACrB,EAAc,EAAa,IACpB,EACT,CACF,EAGa,EAAY,CACvB,OAAQ,IAAiB,EAAsB,GAE/C,aAAc,AAAC,GAEN,AADU,EAAsB,GACvB,MAAM,CAAC,GAAW,EAAQ,OAAO,GAAK,GAGxD,QAAS,AAAC,GACS,AACV,EADgC,GACvB,IAAI,CAAC,GAAW,EAAQ,EAAE,GAAK,GAGjD,OAAQ,AAAC,IACP,IAAM,EAAW,EAAsB,GACjC,EAAsB,CAC1B,GAAG,CAAO,CACV,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAS,IAAI,CAAC,GACd,EAAc,EAAe,GACtB,CACT,EAEA,YAAa,AAAC,IACZ,IAAM,EAAmB,EAAsB,GACzC,EAAc,EAAS,GAAG,CAAC,IAAY,CAC3C,GAAG,CAAO,CADgC,AAE1C,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACnC,CAAC,EAGD,OAFA,EAAiB,IAAI,IAAI,GACzB,EAAc,EAAe,GACtB,CACT,EAEA,OAAQ,AAAC,IACP,IAAM,EAAW,EAAsB,GACjC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,UAC3D,AAAc,CAAC,GAAG,CAAd,IAEJ,EAAS,AAFgB,MAEV,CAAC,EAAO,GACvB,EAAc,EAAe,GACtB,GACT,EAEA,gBAAiB,AAAC,IAEhB,IAAM,EADW,AACQ,EADc,GACL,MAAM,CAAC,GAAW,EAAQ,OAAO,GAAK,GAExE,OADA,EAAc,EAAe,IACtB,CACT,CACF,EAGa,EAAS,CACpB,OAAQ,IAAqB,EAA0B,GAEvD,QAAS,AAAC,GACM,AACP,EADiC,GAC3B,IAAI,CAAC,GAAQ,EAAK,EAAE,GAAK,GAGxC,OAAS,AAAD,IACN,IAAM,EAAQ,EAA0B,GAClC,EAAuB,CAC3B,GAAG,CAAI,CACP,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAM,IAAI,CAAC,GACX,EAAc,EAAY,GACnB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAQ,EAA0B,GAClC,EAAQ,EAAM,SAAS,CAAC,GAAQ,EAAK,EAAE,GAAK,UAClD,AAAc,CAAC,GAAG,CAAd,EAAqB,MAEzB,CAAK,CAAC,EAAM,CAAG,CACb,GAAG,CAAK,CAAC,EAAM,CACf,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAY,GACnB,CAAK,CAAC,EAAM,CACrB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAQ,EAA0B,GAClC,EAAQ,EAAM,SAAS,CAAC,GAAQ,EAAK,EAAE,GAAK,UAClD,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAEnB,MAAM,CAAC,EAAO,GACpB,EAAc,EAAY,IACnB,EACT,CACF,EAGa,EAAQ,CACnB,OAAQ,IAAoB,EAAyB,GAErD,QAAS,AAAC,GACK,AACN,EAD+B,GAC1B,IAAI,CAAC,GAAO,EAAI,EAAE,GAAK,GAGrC,OAAQ,AAAC,IACP,IAAM,EAAO,EAAyB,GAChC,EAAqB,CACzB,GAAG,CAAG,CACN,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAK,IAAI,CAAC,GACV,EAAc,EAAW,GAClB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAO,EAAyB,GAChC,EAAQ,EAAK,SAAS,CAAC,GAAO,EAAI,EAAE,GAAK,UAC/C,AAAc,CAAC,GAAG,CAAd,EAAqB,MAEzB,CAAI,CAAC,EAAM,CAAG,CACZ,GAAG,CAAI,CAAC,EAAM,CACd,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAW,GAClB,CAAI,CAAC,EAAM,CACpB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAO,EAAyB,GAChC,EAAQ,EAAK,SAAS,CAAC,GAAO,EAAI,EAAE,GAAK,UACjC,AAAd,CAAe,GAAG,CAAd,IAEJ,EAFyB,AAEpB,MAAM,CAAC,EAAO,GACnB,EAAc,EAAW,IAClB,EACT,CACF,EAGa,EAAc,CACzB,OAAQ,IAAmB,EAAwB,GAEnD,aAAc,AAAC,GACM,AACZ,EADoC,GACzB,MAAM,CAAC,GAAa,EAAU,OAAO,GAAK,GAG9D,QAAS,AAAC,GACW,AACZ,EADoC,GACzB,IAAI,CAAC,GAAa,EAAU,EAAE,GAAK,GAGvD,OAAQ,AAAC,IACP,IAAM,EAAa,EAAwB,GACrC,EAA0B,CAC9B,GAAG,CAAS,CACZ,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAW,IAAI,CAAC,GAChB,EAAc,EAAiB,GACxB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAa,EAAwB,GACrC,EAAQ,EAAW,SAAS,CAAC,GAAa,EAAU,EAAE,GAAK,UACjE,AAAc,CAAC,GAAG,CAAd,EAAqB,MAEzB,CAAU,CAAC,EAAM,CAAG,CAClB,GAAG,CAAU,CAAC,EAAM,CACpB,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAiB,GACxB,CAAU,CAAC,EAAM,CAC1B,EAEA,OAAQ,AAAC,IACP,IAAM,EAAa,EAAwB,GACrC,EAAQ,EAAW,SAAS,CAAC,GAAa,EAAU,EAAE,GAAK,UACjE,AAAc,CAAC,GAAG,CAAd,IAEJ,EAAW,AAFc,MAER,CAAC,EAAO,GACzB,EAAc,EAAiB,IACxB,EACT,EAEA,gBAAiB,AAAC,IAEhB,IAAM,EAAqB,AADR,EAAwB,GACL,MAAM,CAAC,GAAa,EAAU,OAAO,GAAK,GAEhF,OADA,EAAc,EAAiB,IACxB,CACT,CACF,EAGa,EAAW,CACtB,OAAQ,IAAgB,EAAqB,GAE7C,QAAU,AAAD,GAEA,AADS,EAAqB,GACtB,IAAI,CAAC,GAAU,EAAO,EAAE,GAAK,GAG9C,OAAQ,AAAC,IACP,IAAM,EAAU,EAAqB,GAC/B,EAAoB,CACxB,GAAG,CAAM,CACT,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAQ,IAAI,CAAC,GACb,EAAc,EAAc,GACrB,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAU,EAAqB,GAC/B,EAAQ,EAAQ,SAAS,CAAC,GAAU,EAAO,EAAE,GAAK,UACxD,AAAI,AAAU,CAAC,GAAG,GAAO,MAEzB,CAAO,CAAC,EAAM,CAAG,CACf,GAAG,CAAO,CAAC,EAAM,CACjB,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAc,GACrB,CAAO,CAAC,EAAM,CACvB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAU,EAAqB,GAC/B,EAAQ,EAAQ,SAAS,CAAC,GAAU,EAAO,EAAE,GAAK,UACxD,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAEjB,MAAM,CAAC,EAAO,GACtB,EAAc,EAAc,IACrB,EACT,CACF,EAGa,EAAiB,CAC5B,OAAQ,IAAsB,EAA2B,GAEzD,aAAc,AAAC,GACI,AACV,EADqC,GAC5B,IAAI,CAAC,GAAW,EAAQ,OAAO,GAAK,GAGtD,QAAS,AAAC,GACS,AACV,EADqC,GAC5B,IAAI,CAAC,GAAW,EAAQ,EAAE,GAAK,GAGjD,OAAS,AAAD,IACN,IAAM,EAAW,EAA2B,GACtC,EAA2B,CAC/B,GAAG,CAAO,CACV,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAS,IAAI,CAAC,GACd,EAAc,EAAqB,GAC5B,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAW,EAA2B,GACtC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,GAC3D,GAAc,CAAC,GAAG,CAAd,EAQJ,IARyB,GAEzB,CAAQ,CAAC,EAAM,CAAG,CAChB,GAAG,CAAQ,CAAC,EAAM,CAClB,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAqB,GAC5B,CAAQ,CAAC,EAAM,AACxB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAW,EAA2B,GACtC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,UAC3D,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAEhB,MAAM,CAAC,EAAO,GACvB,EAAc,EAAqB,IAC5B,EACT,CACF,EAGa,EAAmB,CAC9B,OAAQ,IAAwB,EAA6B,GAE7D,aAAc,AAAC,GAEN,AADU,EAA6B,GAC9B,MAAM,CAAC,GAAW,EAAQ,OAAO,GAAK,GAGxD,aAAc,CAAC,EAAiB,IACb,AACV,EADuC,GAC9B,IAAI,CAAC,GACnB,EAAQ,OAAO,GAAK,GAAW,EAAQ,aAAa,GAAK,GAI7D,QAAU,AAAD,GACU,AACV,EADuC,GAC9B,IAAI,CAAC,GAAW,EAAQ,EAAE,GAAK,GAGjD,OAAQ,AAAC,IACP,IAAM,EAAW,EAA6B,GACxC,EAA6B,CACjC,GAAG,CAAO,CACV,GAAI,IACJ,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,EAGA,OAFA,EAAS,IAAI,CAAC,GACd,EAAc,EAAuB,GAC9B,CACT,EAEA,OAAQ,CAAC,EAAY,KACnB,IAAM,EAAW,EAA6B,GACxC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,GAC3D,GAAc,CAAC,GAAG,CAAd,EAQJ,IARyB,GAEzB,CAAQ,CAAC,EAAM,CAAG,CAChB,GAAG,CAAQ,CAAC,EAAM,CAClB,GAAG,CAAO,CACV,UAAW,IAAI,OAAO,WAAW,EACnC,EACA,EAAc,EAAuB,GAC9B,CAAQ,CAAC,EAAM,AACxB,EAEA,OAAQ,AAAC,IACP,IAAM,EAAW,EAA6B,GACxC,EAAQ,EAAS,SAAS,CAAC,GAAW,EAAQ,EAAE,GAAK,UAC3D,AAAc,CAAC,GAAG,CAAd,IAEJ,EAFyB,AAEhB,MAAM,CAAC,EAAO,GACvB,EAAc,EAAuB,IAC9B,EACT,EAGA,iBAAkB,CAAC,EAAiB,EAAuB,EAAqB,CAAC,IAE/E,IAAM,EADW,AACK,EADwB,GACf,MAAM,CAAC,GAAW,EAAQ,OAAO,GAAK,GAE/D,EAAe,KAAK,GAAG,CAAC,EAAG,EAAgB,GAC3C,EAAa,EAAgB,EAEnC,OAAO,EAAc,MAAM,CAAC,GAC1B,EAAQ,aAAa,EAAI,GAAgB,EAAQ,aAAa,EAAI,GAClE,IAAI,CAAC,CAAC,EAAG,IAAM,EAAE,aAAa,CAAG,EAAE,aAAa,CACpD,CACF,ggBE7mBA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,yDDfA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAGO,eAAe,EAAI,CAAoB,EAC5C,GAAI,CACF,GAAM,cAAE,CAAY,CAAE,CAAG,IAAI,IAAI,EAAQ,GAAG,EACtC,EAAU,EAAa,GAAG,CAAC,WAC3B,EAAgB,EAAa,GAAG,CAAC,iBAEvC,GAAI,CAAC,EACH,OADY,AACL,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,QAAS,GAAO,MAAO,UAAW,EACpC,CAAE,OAAQ,GAAI,GAIlB,GAAI,EAAe,CAEjB,IAAM,EAAU,EAAA,gBAAgB,CAAC,YAAY,CAAC,EAAS,SAAS,IAEhE,GAAI,CAAC,EACH,OADY,AACL,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,QAAS,GAAO,MAAO,UAAW,EACpC,CAAE,OAAQ,GAAI,GAIlB,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,SAAS,EACT,KAAM,CACR,EACF,CAAO,CAEL,IAAM,EAAW,EAAA,gBAAgB,CAAC,YAAY,CAAC,GAE/C,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,SAAS,EACT,KAAM,CACR,EACF,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,aAAc,GACrB,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,WAAY,EACrC,CAAE,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe,EAAI,CAAoB,EAC5C,GAAI,CACF,GAAM,SAAE,CAAO,eAAE,CAAa,CAAE,GAAG,EAAS,CAAG,MAAM,EAAQ,IAAI,GAEjE,GAAI,CAAC,QAA6B,IAAlB,EACd,KAD2C,EACpC,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,cAAe,EACxC,CAAE,OAAQ,GAAI,GAIlB,IAAM,EAAkB,EAAA,gBAAgB,CAAC,YAAY,CAAC,EAAS,GAC/D,GAAI,CAAC,EACH,OAAO,EAAA,MADa,MACD,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,UAAW,EACpC,CAAE,OAAQ,GAAI,GAIlB,IAAM,EAAiB,EAAA,gBAAgB,CAAC,MAAM,CAAC,EAAgB,EAAE,CAAE,GAEnE,GAAI,CAAC,EACH,OAAO,EAAA,KADY,OACA,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,MAAO,EAChC,CAAE,OAAQ,GAAI,GAIlB,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,SAAS,EACT,KAAM,EACN,QAAS,WACX,EACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,aAAc,GACrB,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,WAAY,EACrC,CAAE,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe,EAAO,CAAoB,EAC/C,GAAI,CACF,GAAM,cAAE,CAAY,CAAE,CAAG,IAAI,IAAI,EAAQ,GAAG,EACtC,EAAU,EAAa,GAAG,CAAC,WAC3B,EAAgB,EAAa,GAAG,CAAC,iBAEvC,GAAI,CAAC,GAAW,CAAC,EACf,OAAO,EAAA,IADuB,QACX,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,cAAe,EACxC,CAAE,OAAQ,GAAI,GAIlB,IAAM,EAAkB,EAAA,gBAAgB,CAAC,YAAY,CAAC,EAAS,SAAS,IACxE,GAAI,CAAC,EACH,OAAO,EAAA,MADa,MACD,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,UAAW,EACpC,CAAE,OAAQ,GAAI,GAMlB,GAAI,CAFY,AAEX,EAFW,OAEF,SAFkB,CAAC,MAAM,CAAC,EAAgB,EAAE,EAGxD,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,MAAO,EAChC,CAAE,OAAQ,GAAI,GAIlB,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,SAAS,EACT,QAAS,WACX,EACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,aAAc,GACrB,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,WAAY,EACrC,CAAE,OAAQ,GAAI,EAElB,CACF,CCvHA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,6BACN,SAAU,uBACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,iDAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,CAAE,aAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,uBACA,CACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,6BAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,YAAE,CAAU,aAAE,CAAW,mBAAE,CAAiB,qBAAE,CAAmB,sBAAE,CAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,EAAQ,GAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,IAC+B,IAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,EACgB,KAAtB,EAAY,CAAkB,IAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAa,EAAO,UAVyE,QAUvD,GACtC,EAAU,CACZ,2BACA,EACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAc,AAAd,EAAe,EAAK,oBACtC,kBAAmB,AAAwD,MAAvD,GAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAU,AAAD,IACL,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,sBAAkB,EAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAc,AAAd,EAAe,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,GAAoB,GAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAc,AAAd,GAAiB,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAS,KAA8C,IAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,CAAG,OAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,CACV,oBACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAA,AAAO,EAAE,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,WAAY,qBACZ,EACA,mBAAmB,uBACnB,0BACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZI,AAAE,CAAA,AAAD,EAAC,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAD,AAAK,SAAS,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAeV,GAbI,AAAC,GAAgB,WAAF,CAAC,CAAgB,EAAA,eAAe,EAC/C,CADkD,KAC5C,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [2]}