var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/novels/route.js")
R.c("server/chunks/node_modules_next_e29485f9._.js")
R.c("server/chunks/[root-of-the-server]__634e52a1._.js")
R.m("[project]/.next-internal/server/app/api/novels/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/novels/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/novels/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
